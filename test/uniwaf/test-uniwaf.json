{"BEGIN_TIME": "2024-05-30 07:16:48.917", "END_TIME": "2024-05-30 07:16:48.918", "PROBE_VER": "20240527-128", "host": "***********", "SYSTEM": "Ubuntu 22.04.3", "SRC_IP": "*************", "DST_IP": "************", "SRC_PORT": "46152", "DST_PORT": "80", "NIC": "any", "CARD": "br-vmr", "CARD_IP": "*************", "SRC_NET_DELAY": "0.01", "DST_NET_DELAY": "0.13", "NET_DELAY": "0.14", "RST_PKTS": "0", "RETRANS_PKTS": "0", "BYTES": "1171", "HTTP_RESPONSE": "0.792", "PAGELOAD": "0.792", "RETCODE": "403", "METHOD": "GET", "URL": "/?t=../../etc/passwd", "DOMAIN": "************", "FORWARD": "0.0.0.0", "AGENT": "curl/7.81.0", "REQ_HEADER": "GET /?t=../../etc/passwd HTTP/1.1\r\nHost: ************\r\nUser-Agent: curl/7.81.0\r\nAccept: */*\r\n\r\n", "REQ_BODY": "", "RSP_HEADER": "HTTP/1.1 403 Forbidden\r\nDate: Thu, 30 May 2024 07:16:49 GMT\r\nContent-Type: text/html; charset=UTF-8\r\nTransfer-Encoding: chunked\r\nConnection: keep-alive\r\nserver: ZhongKui WAF\r\n\r\n", "RSP_BODY": "223sdfalkjsflkajf", "L7_PROTOCOL": "http", "L4_PROTOCOL": "tcp", "message": "jxit", "message-type": "comm", "gl2_source_collector": "64210464315174928", "DISK_RATIO": "46.73", "TOTAL_CPU": "4", "LICENSE": "Only for POC"}