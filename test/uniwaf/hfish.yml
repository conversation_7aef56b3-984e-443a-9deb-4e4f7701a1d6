apiVersion: apps/v1
kind: Deployment
metadata:
  name: hfish
  namespace: uniserver
  labels:
    app: hfish
spec:
  replicas: 1
  selector:
    matchLabels:
      app: hfish
  template:
    metadata:
      labels:
        app: hfish
    spec:
      nodeSelector:
        node-role.kubernetes.io/master: "true"
      hostNetwork: true
      containers:
      - name: hfish
        image: registry.jxit.net.cn:5000/uniserver/hfish-server:latest
