#!/bin/bash

now=$(date +%Y%m%d-%H%M%S)

server="10.212.25.68:9000"
tmppath="/tmp/sidecars.json"
outpath="/tmp/probe.csv"

# 定义一个函数，将给定的日期时间字符串加上8小时
add_eight_hours() {
    new_datetime=$(date -d "$1 0 hours" +"%Y-%m-%dT%H:%M:%S.%3NZ")
    echo $new_datetime
}

curl "http://$server/api/sidecars?&page=1&per_page=5000&only_active=false&sort=last_seen&order=asc" \
  -u 'admin:admin@123' \
  -H 'Accept: application/json' \
  -H 'Accept-Language: zh-CN,zh;q=0.9' \
  -H 'Connection: keep-alive' \
  -H 'Content-Type: application/json' \
  -H 'Referer: http://$server/system/sidecars' \
  -H 'X-Graylog-No-Session-Extension: true' \
  -H 'X-Requested-By: XMLHttpRequest' \
  -H 'X-Requested-With: XMLHttpRequest' \
  --compressed \
  --insecure > $tmppath

jq -r '.sidecars[] | "\(.node_name), \(.last_seen), \(.node_details.operating_system), \(.sidecar_version)"' $tmppath > $outpath

while IFS=, read -r col1 col2 col3 col4
do
    new_col2=$(add_eight_hours "$col2")
    echo "$col1, $new_col2, $col3, $col4"
done < $outpath
