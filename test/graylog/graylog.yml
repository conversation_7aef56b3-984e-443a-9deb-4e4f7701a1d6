apiVersion: apps/v1
kind: DaemonSet
metadata:
  name: uniserver
  namespace: uniserver
spec:
  selector:
    matchLabels:
      app: uniserver
  template:
    metadata:
      labels:
        app: uniserver
    spec:
      hostNetwork: true
      dnsPolicy: ClusterFirstWithHostNet
      containers:
      - name: uniserver
        image: registry.jxit.net.cn:5000/graylog/graylog:4.0.16
        ports:
        - containerPort: 12201
          hostPort: 12201
          name: gelf
        - containerPort: 1514
          hostPort: 1514
          name: syslog
        env:
        - name: GRAYLOG_PASSWORD_SECRET
          value: "somepasswordpepper"
        - name: GRAYLOG_ROOT_PASSWORD_SHA2 # password: admin@123
          value: "7676aaafb027c825bd9abab78b234070e702752f625b752e55e55b48e607e358"
        - name: GRAY<PERSON>OG_ROOT_TIMEZONE
          value: "Asia/Shanghai"
        - name: GRAYLOG_SERVER_JAVA_OPTS
          value: "-Xms2g -Xmx2g"
        - name: GRAYLOG_HTTP_EXTERNAL_URI
          value: "http://demo.microflow.io:9000/"
        - name: GRAYLOG_ELASTICSEARCH_HOSTS
          value: "http://127.0.0.1:9200"
        - name: GRAYLOG_MONGODB_URI
          value: "mongodb://mongo/graylog"
