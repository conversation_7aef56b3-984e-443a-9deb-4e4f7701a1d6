services:
    mongo:
      container_name: mongo
      image: registry.jxit.net.cn:5000/uniserver/mongodb:4.2
      networks:
        - uniserver
      volumes:
        - mongo_data:/data/db
        - /etc/localtime:/etc/localtime
    elasticsearch:
      container_name: elasticsearch 
      image: registry.jxit.net.cn:5000/uniserver/elasticsearch:7.10.2
      volumes:
        - es_data:/usr/share/elasticsearch/data
        - /etc/localtime:/etc/localtime
      ulimits:
       nproc: 65535
       nofile:
         soft: 65535
         hard: 65535
       memlock:
         soft: -1
         hard: -1
      environment:
        - http.host=0.0.0.0
        - transport.host=localhost
        - network.host=0.0.0.0
        - "ES_JAVA_OPTS=-Dlog4j2.formatMsgNoLookups=true -Xms1g -Xmx1g"
      networks:
        - uniserver               
    uniserver:
      container_name: uniserver 
      image: registry.jxit.net.cn:5000/uniserver/uniserver:2024-07-12-4.1
      volumes:
        - uniserver_data:/usr/share/graylog/data
        - /etc/localtime:/etc/localtime
      environment:
        # CHANGE ME (must be at least 16 characters)!
        - GRAYLOG_PASSWORD_SECRET=somepasswordpepper
        # GRAYLOG_ROOT_PASSWORD_SHA2: 指定登录密码，默认Password为：admin@123
        # 更改密码可使用"echo -n admin@123|shasum -a 256"生成加密字符替换
        - GRAYLOG_ROOT_PASSWORD_SHA2=7676aaafb027c825bd9abab78b234070e702752f625b752e55e55b48e607e358
        - GRAYLOG_HTTP_EXTERNAL_URI=http://127.0.0.1:9000/
        - GRAYLOG_ROOT_TIMEZONE=Asia/Shanghai
        - GRAYLOG_TRANSPORT_EMAIL_USE_TLS=TRUE
        - GRAYLOG_TRANSPORT_EMAIL_ENABLED=TRUE
        - GRAYLOG_TRANSPORT_EMAIL_USE_SSL=FALSE
        - GRAYLOG_TRANSPORT_EMAIL_USE_AUTH=TRUE
        - GRAYLOG_TRANSPORT_EMAIL_HOSTNAME=smtp.163.com
        - GRAYLOG_TRANSPORT_EMAIL_PORT=25
        - GRAYLOG_HTTP_ENABLE_CORS=true
        - GRAYLOG_SERVER_JAVA_OPTS=-Xms1g -Xmx1g
        #- GRAYLOG_TRANSPORT_EMAIL_AUTH_USERNAME=<EMAIL>
        #- GRAYLOG_TRANSPORT_EMAIL_AUTH_PASSWORD=HYSQPHEGKAOWEEZE
        #- GRAYLOG_TRANSPORT_EMAIL_SUBJECT_PREFIX=[graylog]
        #- GRAYLOG_TRANSPORT_EMAIL_FROM_EMAIL=<EMAIL>
      entrypoint: /usr/bin/tini -- wait-for-it elasticsearch:9200 --  /docker-entrypoint.sh
      networks:
        - uniserver
      restart: always
      depends_on:
        - mongo
        - elasticsearch
      ports:
        # uniserver web interface and REST API
        - 9000:9000
        # GELF TCP
        - 12201:12201
        # GELF UDP
        - 12201:12201/udp
networks:
    uniserver:
      driver: bridge
volumes:
  mongo_data:
    driver: local
  es_data:
    driver: local
  uniserver_data:
    driver: local

