#!/bin/bash

# 平台架构
platform=$1
if [ -z "$platform" ]; then
  echo "usage: $0 [platform]"
  exit
fi

# 目标文件
date=$(date +%Y%m%d)
ver=$(git log --oneline | wc -l)
target="uniserver-images-$date-$ver-$platform.tar"

# 拉取镜像
imageList=$(grep image: -rwn k8s/ | awk '{print $3}' | sed 's/\r//g' | grep uniserver | grep -v mongo | grep -v elasticsearch| sort | uniq)
for image in ${imageList[@]}; do
  ctr image pull $image
done

# 生成文件
rm -rf $target
ctr image export $target $(echo $imageList | xargs)

