import pandas as pd
import argparse
import datetime
import os
import re
import numpy as np
from openpyxl.styles import Alignment


def convert_to_mb(value):
    """将带单位的流量值转换为MB"""
    if pd.isna(value):
        return 0
    # 确保值是字符串类型
    value_str = str(value)
    # 提取数值和单位，支持B、KB、MB、GB、TB
    match = re.match(r"^(\d+\.?\d*) (B|[KMGT]B)$", value_str.strip(), re.IGNORECASE)
    if not match:
        raise ValueError(f"无效的流量格式: {value_str}")
    number = float(match.group(1))
    unit = match.group(2).upper()

    # 转换为MB
    if unit == "MB":
        return number
    elif unit == "KB":
        return number / 1024
    elif unit == "B":
        return number / (1024 * 1024)
    elif unit == "GB":
        return number * 1024
    elif unit == "TB":
        return number * 1024 * 1024
    else:
        raise ValueError(f"不支持的单位: {unit}")


def compute_ratios(input_file, output_file):
    # 读取Excel文件
    try:
        df = pd.read_excel(input_file)
    except Exception as e:
        print(f"读取Excel文件失败: {e}")
        return

    # 检查必要的列是否存在
    required_columns = ["系统访问总量", "系统数据流量", "数据库访问量"]
    for col in required_columns:
        if col not in df.columns:
            print(f"错误: 表格中缺少必要的列 '{col}'")
            return

    # 转换流量单位为MB（仅转换系统数据流量）
    try:
        df["系统数据流量_MB"] = df["系统数据流量"].apply(convert_to_mb)
        # 数据库访问量已经是数值，直接使用
        df["数据库访问量"] = pd.to_numeric(df["数据库访问量"], errors='coerce').fillna(0)
    except Exception as e:
        print(f"转换数据格式时出错: {e}")
        return

    # 逐行计算比值
    try:
        # 计算系统访问总量与系统数据流量的比值，处理除零情况
        df["系统访问总量/系统数据流量"] = df["系统访问总量"].div(
            df["系统数据流量_MB"], fill_value="/"
        )

        # 处理除数为0导致的无穷大值
        df["系统访问总量/系统数据流量"].replace([np.inf, -np.inf], "/", inplace=True)
        df["系统访问总量/系统数据流量"] = df["系统访问总量/系统数据流量"].apply(
            lambda x: "/" if pd.isna(x) or x == np.inf or x == -np.inf else x
        )
        # 保留两位小数
        df["系统访问总量/系统数据流量"] = df["系统访问总量/系统数据流量"].apply(
            lambda x: x if x == "/" else round(x, 2)
        )

        # 计算系统访问总量与数据库访问量的比值，处理除零情况
        df["系统访问总量/数据库访问量"] = df["系统访问总量"].div(
            df["数据库访问量"], fill_value="/"
        )

        # 处理除数为0导致的无穷大值
        df["系统访问总量/数据库访问量"].replace([np.inf, -np.inf], "/", inplace=True)
        df["系统访问总量/数据库访问量"] = df["系统访问总量/数据库访问量"].apply(
            lambda x: "/" if pd.isna(x) or x == np.inf or x == -np.inf else x
        )
        # 保留两位小数
        df["系统访问总量/数据库访问量"] = df["系统访问总量/数据库访问量"].apply(
            lambda x: x if x == "/" else round(x, 2)
        )

    except Exception as e:
        print(f"计算比值时出错: {e}")
        return

    # 保存到新的Excel文件
    try:
        # 选择需要输出的列，排除中间计算列
        columns_to_keep = [
            col
            for col in df.columns
            if col not in ["系统数据流量_MB"]
        ]
        # 使用ExcelWriter并设置单元格居中
        with pd.ExcelWriter(output_file, engine='openpyxl') as writer:
            df[columns_to_keep].to_excel(writer, index=False, sheet_name='Sheet1')
            worksheet = writer.sheets['Sheet1']
            # 设置所有单元格居中
            for column_cells in worksheet.columns:
                for cell in column_cells:
                    cell.alignment = Alignment(horizontal='center', vertical='center')
        print(f"计算完成，结果已保存到: {output_file}")
    except Exception as e:
        print(f"保存Excel文件失败: {e}")
        return


if __name__ == "__main__":
    # 解析命令行参数
    parser = argparse.ArgumentParser(
        description="计算系统访问相关比值并输出到新Excel文件"
    )
    parser.add_argument("--input", dest="input_file", help="输入Excel文件路径")
    parser.add_argument("--output", dest="output_file", help="输出Excel文件路径")
    args = parser.parse_args()

    # 检查输入文件是否存在
    if not os.path.exists(args.input_file):
        print(f"错误: 输入文件 '{args.input_file}' 不存在")
        exit(1)

    # 获取今天的日期并进行格式化
    today = datetime.datetime.now().strftime("%Y%m%d")
    output_file = os.path.join(args.output_file, today, "系统访问量比值计算结果.xlsx")

    # 检查输出文件目录是否存在
    output_dir = os.path.dirname(output_file)
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)

    # 调用计算函数
    compute_ratios(args.input_file, output_file)
