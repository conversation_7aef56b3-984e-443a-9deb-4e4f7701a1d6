{"cells": [{"cell_type": "code", "execution_count": 1, "id": "0a141926", "metadata": {}, "outputs": [], "source": ["import sys\n", "import pandas as pd\n", "import logging\n", "from functools import partial"]}, {"cell_type": "code", "execution_count": 2, "id": "11a20d96", "metadata": {}, "outputs": [], "source": ["def setup_logging(log_file=None, debug=False):\n", "    \"\"\"\n", "    设置日志器\\n\n", "    :param log_file: 日志文件路径，如果为None则不写入文件\\n\n", "    :param debug: 是否开启调试模式，调试模式下日志级别为DEBUG，否则为INFO\\n\n", "    :return: None\n", "    \"\"\"\n", "    handlers = []\n", "    console_handler = logging.StreamHandler(sys.stderr)\n", "    handlers.append(console_handler)\n", "\n", "    if log_file:\n", "        file_handler = logging.FileHandler(log_file)\n", "        handlers.append(file_handler)\n", "\n", "    log_format = \"%(asctime)s [%(levelname)s] %(name)s: %(message)s\" if debug else \"%(levelname)s: %(message)s\"\n", "    \n", "    logging.basicConfig(\n", "        level=logging.DEBUG if debug else logging.INFO,\n", "        format=log_format,\n", "        handlers=handlers,\n", "        #encoding='utf-8'\n", "    )\n", "\n", "setup_logging(debug=True)\n", "logger =  logging.getLogger(__name__)"]}, {"cell_type": "code", "execution_count": 3, "id": "459b2204", "metadata": {}, "outputs": [], "source": ["def parse_origin_file(origin_file_path,sheet_name,required_columns,skiprows=None):\n", "    \"\"\"\n", "    通过Pandas解析原始文件内容\\n\n", "    :param origin_file_path: 原始Excel文件路径\\n\n", "    :param sheet_name: 要解析的sheet名称\\n\n", "    :return: DataFrame对象\\n\n", "    \"\"\"\n", "    try:\n", "        logger.info(f\"开始解析文件：{origin_file_path}，sheet名称：{sheet_name}\")\n", "        # 读取Excel文件，可根据需要调整sheet_name参数\n", "        df = pd.read_excel(origin_file_path, engine='openpyxl', sheet_name=sheet_name,skiprows=skiprows)\n", "        df.dropna(how='all', inplace=True)  # 删除全为空值的行\n", "        # 检查是否包含必要的列\n", "        for col in required_columns:\n", "            if col not in df.columns:\n", "                logger.error(f\"错误：文档中缺少必要的列：{col}，请检查文档格式。\", exc_info=True)\n", "                raise ValueError(f\"缺少必要的列：{col}\")\n", "        return df\n", "    except FileNotFoundError:\n", "        logger.error(f\"错误：未找到{origin_file_path}文件，请检查路径和文件名是否正确。\",exc_info=True)\n", "        raise FileNotFoundError(f\"未找到文件：{origin_file_path}\")\n", "    except Exception as e:\n", "        logger.error(f\"读取文档{origin_file_path}过程中发生错误：{str(e)}\",exc_info=True)\n", "        raise Exception(f\"读取文档{origin_file_path}时发生错误：{str(e)}\")"]}, {"cell_type": "code", "execution_count": 4, "id": "226bfde8", "metadata": {}, "outputs": [], "source": ["origin_file = r\"D:/内网匹配结果202506181725.xlsx\"\n", "origin_sheet_name = \"Sheet1\"\n", "ip_match_file = r\"D:/原始数据/ip匹配.xlsx\"\n", "ip_match_sheet_name = \"Sheet1\"\n", "order_file = r\"D:/downloads/微信下载/WeChat Files/wxid_0zd37hfwbnih21/FileStorage/File/2025-06/20250619.xlsx\"\n", "order_sheet_name = \"Sheet1\"\n", "network_order_file = r\"D:/原始数据/补充-安全策略工单_2025060902/安全策略工单_2025060902全量.xlsx\"\n", "network_order_sheet_name = \"temp_orders_20250609\""]}, {"cell_type": "code", "execution_count": 5, "id": "2a06dcc8", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-06-19 16:50:09,842 [INFO] __main__: 开始解析文件：D:/内网匹配结果202506181725.xlsx，sheet名称：Sheet1\n", "2025-06-19 16:50:17,109 [INFO] __main__: 开始解析文件：D:/原始数据/ip匹配.xlsx，sheet名称：Sheet1\n", "2025-06-19 16:50:17,160 [INFO] __main__: 开始解析文件：D:/downloads/微信下载/WeChat Files/wxid_0zd37hfwbnih21/FileStorage/File/2025-06/20250619.xlsx，sheet名称：Sheet1\n", "2025-06-19 16:50:17,521 [INFO] __main__: 开始解析文件：D:/原始数据/补充-安全策略工单_2025060902/安全策略工单_2025060902全量.xlsx，sheet名称：temp_orders_20250609\n"]}], "source": ["# 读取表格\n", "origin_df = parse_origin_file(origin_file,origin_sheet_name,[])\n", "ip_match_df = parse_origin_file(ip_match_file,ip_match_sheet_name,[])\n", "order_df = parse_origin_file(order_file,order_sheet_name,[])\n", "network_order_df = parse_origin_file(network_order_file,network_order_sheet_name,[])"]}, {"cell_type": "code", "execution_count": 6, "id": "38ad1c5d", "metadata": {}, "outputs": [], "source": ["# 初始化收集数据数组\n", "result_data = []"]}, {"cell_type": "code", "execution_count": 7, "id": "5d925100", "metadata": {}, "outputs": [], "source": ["def match_order(row,ip_list):\n", "    if row[\"IP地址\"] in ip_list:\n", "        return row[\"IP地址\"]\n", "    else:\n", "        return \"\""]}, {"cell_type": "code", "execution_count": 8, "id": "361faf63", "metadata": {}, "outputs": [], "source": ["for index,row in ip_match_df.iterrows():\n", "    match_data = origin_df[origin_df[\"系统名称\"] == row[\"信息系统名称\"]].drop_duplicates(subset=['IP地址'])\n", "    if match_data.empty:\n", "        continue\n", "    # 填报ip\n", "    ip_list = match_data[\"IP地址\"].values.tolist()\n", "    # 订单ip列表\n", "    order_ip_list = order_df[order_df[\"信息系统\"] == row[\"信息系统名称\"]][\"挂载资源IP(ipv4)\"].values.tolist()\n", "    #  网络策略ip列表\n", "    network_ip_list = network_order_df[network_order_df[\"信息系统\"] == row[\"信息系统名称\"]][\"源ip\"].values.tolist()\n", "    match_data[\"订单IP\"] = match_data.apply(lambda row: match_order(row, order_ip_list), axis=1)\n", "    match_data[\"网络策略IP\"] = match_data.apply(lambda row: match_order(row, network_ip_list),axis=1)\n", "    result_data.extend(match_data.values.tolist())\n", "    # 复制一行匹配数据\n", "    copy_data = match_data.iloc[0].copy()\n", "    # 匹配截单Ip差集\n", "    for ip in order_ip_list:\n", "        if ip not in ip_list:\n", "            copy_data[\"订单IP\"] = ip\n", "            copy_data[\"IP地址\"] = \"\"\n", "            copy_data[\"网络策略IP\"] = \"\"\n", "            result_data.append(copy_data.values.tolist())\n", "    # 匹配网络策略差集\n", "    for ip in network_ip_list:\n", "        if ip not in ip_list:\n", "            copy_data[\"网络策略IP\"] = ip\n", "            copy_data[\"IP地址\"] = \"\"\n", "            copy_data[\"订单IP\"] = \"\"\n", "            result_data.append(copy_data.values.tolist())\n", "    \n"]}, {"cell_type": "code", "execution_count": 9, "id": "8142c463", "metadata": {}, "outputs": [], "source": ["df = pd.DataFrame(columns=[*list(origin_df.head()),\"订单IP\",\"网络策略IP\"],data=result_data)\n", "df.to_excel(r\"D:/IP匹配结果_202506191650.xlsx\",index=False)"]}], "metadata": {"kernelspec": {"display_name": "uniarch", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 5}