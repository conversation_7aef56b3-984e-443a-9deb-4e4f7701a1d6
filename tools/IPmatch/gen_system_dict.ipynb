{"cells": [{"cell_type": "code", "execution_count": 1, "id": "fe7637c5", "metadata": {}, "outputs": [], "source": ["import sys\n", "import os\n", "import pandas as pd\n", "import logging\n", "import json"]}, {"cell_type": "code", "execution_count": 2, "id": "777ab906", "metadata": {}, "outputs": [], "source": ["def setup_logging(log_file=None, debug=False):\n", "    \"\"\"\n", "    设置日志器\\n\n", "    :param log_file: 日志文件路径，如果为None则不写入文件\\n\n", "    :param debug: 是否开启调试模式，调试模式下日志级别为DEBUG，否则为INFO\\n\n", "    :return: None\n", "    \"\"\"\n", "    handlers = []\n", "    console_handler = logging.StreamHandler(sys.stderr)\n", "    handlers.append(console_handler)\n", "\n", "    if log_file:\n", "        file_handler = logging.FileHandler(log_file)\n", "        handlers.append(file_handler)\n", "\n", "    log_format = \"%(asctime)s [%(levelname)s] %(name)s: %(message)s\" if debug else \"%(levelname)s: %(message)s\"\n", "    \n", "    logging.basicConfig(\n", "        level=logging.DEBUG if debug else logging.INFO,\n", "        format=log_format,\n", "        handlers=handlers,\n", "        #encoding='utf-8'\n", "    )\n", "\n", "setup_logging(debug=True)\n", "logger =  logging.getLogger(__name__)"]}, {"cell_type": "code", "execution_count": 3, "id": "6b8b8807", "metadata": {}, "outputs": [], "source": ["def parse_origin_file(origin_file_path,sheet_name,required_columns,skiprows=None):\n", "    \"\"\"\n", "    通过Pandas解析原始文件内容\\n\n", "    :param origin_file_path: 原始Excel文件路径\\n\n", "    :param sheet_name: 要解析的sheet名称\\n\n", "    :return: DataFrame对象\\n\n", "    \"\"\"\n", "    try:\n", "        logger.info(f\"开始解析文件：{origin_file_path}，sheet名称：{sheet_name}\")\n", "        # 读取Excel文件，可根据需要调整sheet_name参数\n", "        df = pd.read_excel(origin_file_path, engine='openpyxl', sheet_name=sheet_name,skiprows=skiprows)\n", "        df.dropna(how='all', inplace=True)  # 删除全为空值的行\n", "        # 检查是否包含必要的列\n", "        for col in required_columns:\n", "            if col not in df.columns:\n", "                logger.error(f\"错误：文档中缺少必要的列：{col}，请检查文档格式。\", exc_info=True)\n", "                raise ValueError(f\"缺少必要的列：{col}\")\n", "        return df\n", "    except FileNotFoundError:\n", "        logger.error(f\"错误：未找到{origin_file_path}文件，请检查路径和文件名是否正确。\",exc_info=True)\n", "        raise FileNotFoundError(f\"未找到文件：{origin_file_path}\")\n", "    except Exception as e:\n", "        logger.error(f\"读取文档{origin_file_path}过程中发生错误：{str(e)}\",exc_info=True)\n", "        raise Exception(f\"读取文档{origin_file_path}时发生错误：{str(e)}\")"]}, {"cell_type": "code", "execution_count": 4, "id": "305b662a", "metadata": {}, "outputs": [], "source": ["online_system_file = r\"D:/downloads/edge下载/2025年评估.xlsx\"\n", "online_sheet_name = \"非参评系统下线登记\""]}, {"cell_type": "code", "execution_count": 5, "id": "cff62435", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-06-28 17:12:22,875 [INFO] __main__: 开始解析文件：D:/downloads/edge下载/2025年评估.xlsx，sheet名称：非参评系统下线登记\n"]}], "source": ["online_system_df = parse_origin_file(online_system_file,online_sheet_name,[],skiprows=1)"]}, {"cell_type": "code", "execution_count": 6, "id": "f8a04bab", "metadata": {}, "outputs": [], "source": ["system_dict = {}\n", "for index,row in online_system_df.iterrows():\n", "    system_dict[row.系统编号] = row.信息系统名称\n", "json_str = json.dumps(system_dict, ensure_ascii=False, indent=4)\n", "with open(f\"D:/工作/uniserver/tools/IPmatch/system_dict.json\", 'w', encoding='utf-8') as f:\n", "    f.write(json_str)"]}], "metadata": {"kernelspec": {"display_name": "uniarch", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 5}