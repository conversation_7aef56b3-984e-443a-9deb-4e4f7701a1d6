import os
import pandas as pd
import logging
from typing import List, Dict

# 配置日志：记录处理过程中的错误信息
logging.basicConfig(
    filename='excel_process.log',
    level=logging.ERROR,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

class MultiExcelProcessor:
    def __init__(self, target_columns: List[str], check_rows: int = 10):
        """
        初始化处理器
        :param target_columns: 需要提取的目标列名（如['系统编号', '操作量', '日期']）
        :param check_rows: 最多检查前N行寻找表头（默认10行）
        """
        self.target_columns = [col.strip().lower() for col in target_columns]  # 标准化列名（去空格+小写）
        self.check_rows = check_rows
        self.all_data = []  # 存储所有文件的有效数据

    def _find_header_row(self, file_path: str) -> int:
        """
        寻找Excel文件中包含目标列最多的行（表头行）
        :return: 表头行索引（从0开始），未找到返回-1
        """
        try:
            # 只读取前check_rows行，用于识别表头
            sample = pd.read_excel(file_path, nrows=self.check_rows, header=None)
            max_matches = 0
            header_row = -1

            # 遍历每行，计算与目标列的匹配数
            for row_idx in range(sample.shape[0]):
                # 标准化当前行的单元格（去空格+小写）
                row_cells = [str(cell).strip().lower() for cell in sample.iloc[row_idx].tolist()]
                # 统计匹配的目标列数量
                matches = sum(1 for col in self.target_columns if col in row_cells)

                # 更新最佳匹配行
                if matches > max_matches:
                    max_matches = matches
                    header_row = row_idx

            # 如果没有匹配到任何目标列，返回-1
            return header_row if max_matches > 0 else -1

        except Exception as e:
            logging.error(f"读取文件{file_path}的表头时出错：{str(e)}")
            return -1

    def process_file(self, file_path: str) -> None:
        """处理单个Excel文件，提取目标列数据"""
        # 1. 寻找表头行
        header_row = self._find_header_row(file_path)
        if header_row == -1:
            logging.error(f"文件{file_path}中未找到目标列（{self.target_columns}），已跳过")
            return

        try:
            # 2. 读取文件：跳过表头行之前的行，指定表头行，只读取目标列
            # 注意：pandas读取时header参数是“表头行索引”，skiprows会跳过该索引之前的行
            df = pd.read_excel(
                file_path,
                skiprows=header_row,  # 跳过表头行之前的行（若表头在第3行，skiprows=3会跳过0-2行）
                header=0,  # 此时表头行已成为第0行（因为skiprows跳过了前面的行）
                usecols=[col.strip() for col in self.target_columns]  # 用原始列名匹配（去空格）
            )

            # 3. 添加“来源文件”列，便于追溯数据来源
            df['来源文件'] = os.path.basename(file_path)
            self.all_data.append(df)
            print(f"成功处理：{file_path}（表头行：{header_row + 1}）")  # +1是因为Excel行号从1开始

        except Exception as e:
            logging.error(f"处理文件{file_path}时出错：{str(e)}")

    def process_directory(self, dir_path: str) -> pd.DataFrame:
        """处理目录中所有Excel文件，返回合并后的数据"""
        # 遍历目录中的所有.xlsx和.xls文件
        for filename in os.listdir(dir_path):
            if filename.endswith(('.xlsx', '.xls')) and not filename.startswith('~$'):  # 排除临时文件
                file_path = os.path.join(dir_path, filename)
                self.process_file(file_path)

        # 合并所有数据
        if self.all_data:
            merged_df = pd.concat(self.all_data, ignore_index=True)
            print(f"\n处理完成，共合并{len(self.all_data)}个文件，总数据量：{len(merged_df)}行")
            return merged_df
        else:
            print("未找到有效数据")
            return pd.DataFrame()

if __name__ == "__main__":
    # 配置：需要提取的目标列名（根据实际需求修改）
    TARGET_COLUMNS = ['系统编号', '操作量', '查询量', '日期']
    # 处理的目录路径（根据实际情况修改）
    DIRECTORY_PATH = "./excel_files"  # 例如"./data"或"D:/excel_files"

    # 初始化处理器并处理
    processor = MultiExcelProcessor(target_columns=TARGET_COLUMNS, check_rows=10)
    result_df = processor.process_directory(DIRECTORY_PATH)

    # 保存合并结果（可选）
    if not result_df.empty:
        result_df.to_excel("merged_result.xlsx", index=False)
        print(f"合并结果已保存至：merged_result.xlsx")
