"""
Author: linc <EMAIL>
Date: 2025-08-15 09:27:43
LastEditors: linc <EMAIL>
LastEditTime: 2025-08-18 16:55:18
FilePath: /uniserver/tools/database_data_analysis/gen_database_data_analysis.py
Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
"""

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库数据分析脚本
用于分析效能报告和数据库指标专项核实数据的差异
"""

import argparse
import os
import sys
import pandas as pd
import numpy as np
import glob
import datetime

"""
description: 解析命令行参数
param {*} args 命令行参数
return {*}
"""


def parse_arguments():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description="数据库数据分析工具")
    parser.add_argument(
        "-i", "--input-file", required=True, help="输入的效能报告Excel文件路径"
    )
    parser.add_argument(
        "-d", "--origin-dir", required=True, help="数据库指标专项核实原始数据目录"
    )
    parser.add_argument(
        "-o", "--output-dir", required=True, help="输出的差异分析Excel文件目录"
    )
    return parser.parse_args()


"""
description: 导出结果到Excel文件
param {*} output_dir 输出目录
param {*} filename 文件名    输出的Excel文件名
param {*} **kwargs 数据    关键字参数，格式为 sheet_name=data，其中data可以是:
- 列表的列表 (如[[1,2],[3,4]])
- 字典的列表 (如[{'a':1}, {'b':2}])
- pandas DataFrame
- 其他可转换为DataFrame的类型（如Series、NumPy数组等）
- 字符串、数字等简单类型（会被转换为单单元格DataFrame）
return {*}
"""


def export_results_to_excel(output_dir: str, filename: str, **kwargs):
    """
    将多个不同类型的数据导出到Excel文件的不同工作表中

    参数:
        filename (str): 输出的Excel文件名
        **kwargs: 关键字参数，格式为 sheet_name=data，其中data可以是:
        - 列表的列表 (如[[1,2],[3,4]])
        - 字典的列表 (如[{'a':1}, {'b':2}])
        - pandas DataFrame
    """
    # 构建输出文件路径
    today = datetime.datetime.now().strftime("%m%d")
    output_path = os.path.join(output_dir, today, filename)
    os.makedirs(os.path.dirname(output_path), exist_ok=True)

    # 使用pandas的ExcelWriter来创建Excel文件
    try:
        with pd.ExcelWriter(output_path, engine="openpyxl") as writer:
            # 遍历所有传入的数据
            for sheet_name, data in kwargs.items():
                # 根据数据类型处理并转换为DataFrame
                if isinstance(data, pd.DataFrame):
                    # 已经是DataFrame，直接使用
                    df_to_write = data
                elif isinstance(data, list):
                    if data and isinstance(data[0], dict):
                        # 处理字典列表类型（如[{'a':1}, {'b':2}]）
                        df_to_write = pd.DataFrame(data)
                    else:
                        # 处理列表的列表类型（如[[1,2],[3,4]]）
                        df_to_write = pd.DataFrame(data)
                else:
                    # 处理其他类型数据
                    df_to_write = pd.DataFrame([str(data)])

                # 将数据写入对应的工作表
                df_to_write.to_excel(
                    writer, sheet_name=sheet_name, index=False, header=True
                )

        print(f"成功将数据导出到 {output_path}")
    except Exception as e:
        print(f"导出数据时发生错误: {str(e)}")
        # 如果Excel导出失败，尝试保存为CSV
        csv_path = output_path.replace(".xlsx", ".csv").replace(".xls", ".csv")
        print(f"尝试保存为CSV格式到: {csv_path}")

        # 合并所有数据到一个DataFrame中
        all_data = []
        for sheet_name, data in kwargs.items():
            if isinstance(data, (list, pd.DataFrame)) and len(data) > 0:
                if isinstance(data, pd.DataFrame):
                    temp_df = data
                elif isinstance(data, list) and isinstance(data[0], dict):
                    temp_df = pd.DataFrame(data)
                else:
                    temp_df = pd.DataFrame(data)

                # 添加工作表名称列
                temp_df["工作表名称"] = sheet_name
                all_data.append(temp_df)

        if all_data:
            combined_df = pd.concat(all_data, ignore_index=True)
            combined_df.to_csv(csv_path, index=False, encoding="utf-8-sig")
            print(f"数据已保存为CSV格式到: {csv_path}")


"""
description: 保存结果到文件
param {*} results 结果    要保存的结果数据
param {*} output_path 输出路径    输出文件的目录路径
param {*} output_filename 输出文件名    输出文件的名称
param {*} columns 列名    数据框的列名
param {*} delimiter="@" 分隔符    分隔符，默认是"@"
param {*} sheet_name=None 工作表名称    Excel文件中的工作表名称，默认是None
return {*}
"""


def save_results_to_file(
    results, output_path, output_filename, columns, delimiter="@", sheet_name=None
):
    """根据文件名后缀保存结果到CSV或Excel文件"""
    # 创建结果DataFrame
    if isinstance(results, pd.DataFrame):
        result_df = results
    else:
        result_df = pd.DataFrame(results, columns=columns)

    # 删除空行
    result_df.dropna(how="all", inplace=True)

    # 确保输出目录存在
    if not os.path.exists(output_path):
        os.makedirs(output_path)

    # 构建输出文件路径
    today = datetime.datetime.now().strftime("%m%d")
    output_path = os.path.join(output_path, today, output_filename)
    os.makedirs(os.path.dirname(output_path), exist_ok=True)

    # 获取文件扩展名
    file_ext = os.path.splitext(output_filename)[1].lower()

    if file_ext == ".csv":
        # 保存为CSV
        result_df.to_csv(
            output_path,
            index=False,
            encoding="utf-8-sig",
            sep=",",
            quoting=1,  # 1表示QUOTE_ALL，所有字段都加引号
            quotechar='"',
            header=True,
            sheet_name=sheet_name,
        )

        # 读取 CSV 文件，为每一行末尾添加行记录分隔符
        with open(output_path, "r", encoding="utf-8-sig") as file:
            lines = file.readlines()

        # 再写入修改后的内容
        with open(output_path, "w", encoding="utf-8-sig") as file:
            for line in lines:
                file.write(line.rstrip() + f",{delimiter}\n")

    elif file_ext in [".xlsx", ".xls"]:
        # 保存为Excel
        try:
            # 添加engine参数确保使用openpyxl
            result_df.to_excel(output_path, index=False, engine="openpyxl")
        except ImportError:
            print("保存为Excel失败: 请安装openpyxl库")
            # 保存为CSV
            output_path = output_path.replace(file_ext, ".csv")
            result_df.to_csv(
                output_path,
                index=False,
                encoding="utf-8-sig",
                sep=",",
                quoting=1,
                quotechar='"',
                header=True,
            )
        except Exception as e:
            print(f"保存为Excel时发生错误: {str(e)}")
            # 保存为CSV作为备选
            output_path = output_path.replace(file_ext, ".csv")
            result_df.to_csv(
                output_path,
                index=False,
                encoding="utf-8-sig",
                sep=",",
                quoting=1,
                quotechar='"',
                header=True,
            )
    else:
        print(f"不支持的文件格式: {file_ext}，默认保存为CSV")
        # 默认保存为CSV
        output_path = os.path.splitext(output_path)[0] + ".csv"
        result_df.to_csv(
            output_path,
            index=False,
            encoding="utf-8-sig",
            sep=",",
            quoting=1,
            quotechar='"',
            header=True,
        )

    print(f"匹配结果已保存到 {output_path}")
    print(f"匹配结果记录数: {len(result_df)}\n")


"""
description: 读取输入文件
param {*} input_file 输入文件    输入的效能报告文件路径
return {*}
"""


def read_input_file(input_file):
    """
    读取输入的效能报告文件
    获取序号、日期、一级单位、系统编号、系统名称
    数据库操作量、数据库访问量
    返回DataFrame格式数据
    """
    try:
        # 读取Excel文件
        df = pd.read_excel(input_file)

        # 获取所需列
        required_columns = [
            "序号",
            "日期",
            # "时间",
            "一级单位",
            "系统编号",
            "系统名称",
            # "主机数量",
            "数据库操作量",
            "数据库访问量",
            # "数据库访问流量",
        ]

        # 检查列是否存在
        missing_columns = [col for col in required_columns if col not in df.columns]
        if missing_columns:
            print(f"警告：以下列在输入文件中不存在：{missing_columns}")
            # 只保留 required_columns 存在的列
            available_columns = [col for col in required_columns if col in df.columns]
            df = df[available_columns]
        else:
            df = df[required_columns]

        # 按系统编号生成字典
        input_dict = {}
        for _, row in df.iterrows():
            system_code = str(row["系统编号"]).strip()
            unit = str(row["一级单位"]).strip()
            system_name = str(row["系统名称"]).strip()

            # host_num = pd.to_numeric(str(row["主机数量"]).strip(), errors="coerce") or 0
            db_operate = (
                pd.to_numeric(str(row["数据库操作量"]).strip(), errors="coerce") or 0
            )
            db_access = (
                pd.to_numeric(str(row["数据库访问量"]).strip(), errors="coerce") or 0
            )
            # db_traffic = (
            #     pd.to_numeric(str(row["数据库访问流量"]).strip(), errors="coerce") or 0
            # )
            input_dict[system_code] = {
                "一级单位": unit,
                "系统名称": system_name,
                # "主机数量": host_num,
                "数据库操作量": db_operate,
                "数据库访问量": db_access,
                # "数据库访问流量": db_traffic,
            }

        return df, input_dict

    except Exception as e:
        print(f"读取输入文件失败: {e}")
        sys.exit(1)


"""
description: 清理DataFrame列名中的换行符、回车符和空格
param {*} df DataFrame    要清理的DataFrame
return {*}
"""


def clean_columns(df):
    """
    清理DataFrame列名中的换行符、回车符和空格
    """
    df.columns = [
        str(col).replace("\n", "").replace("\r", "").strip() for col in df.columns
    ]
    return df


"""
description: 智能查找列名 - 支持精确和模糊匹配
param {*} df DataFrame    要查找的DataFrame
param {*} column_mapping 列映射    要查找的列名映射
return {*}
"""


def find_columns(df, column_mapping):
    """智能查找列名 - 支持精确和模糊匹配"""
    found_columns = {}
    df_columns = [str(col).strip() for col in df.columns]

    # 遍历所有可能的列名
    for standard_name, possible_names in column_mapping.items():
        for possible_name in possible_names:
            # 精确匹配 - 直接匹配列名
            if possible_name in df_columns:
                found_columns[standard_name] = possible_name
                break
            # 模糊匹配 - 包含关系
            for df_col in df_columns:
                if possible_name in df_col or df_col in possible_name:
                    found_columns[standard_name] = df_col
                    break
            if standard_name in found_columns:
                break
    return found_columns


"""
description: 获取列的第一个有效值
param {*} df DataFrame    要查找的DataFrame
param {*} column_name 列名    要查找的列名
return {*}
"""


def get_first_valid_value(df, column_name):
    """获取列的第一个有效值"""
    if column_name in df.columns:
        # values = df[column_name].dropna()
        values = df[column_name]
        if len(values) > 0:
            return str(values.iloc[0]).strip()
    return None

"""
description: 安全除法函数，避免除数为0的情况
param {*} numerator 分子    被除数
param {*} denominator 分母    除数
return {*}
"""

def safe_divide(tool_value, user_value):
    """安全除法函数，避免除数为0的情况"""
    try:
        tool_value_float = float(tool_value)
        user_value_float = float(user_value)
        if user_value_float > 0.001:
            return round((tool_value_float - user_value_float) / user_value_float * 100, 2)
        return "/"
    except (ValueError, ZeroDivisionError, TypeError):
        return "/"


"""
description: 读取origin_dir目录下的所有文件
param {*} origin_dir 目录    要读取的目录路径
return {*}
"""


def read_origin_files(origin_dir):
    """
    智能读取origin_dir目录下的所有文件
    自动查找所需列，不管跳过几行都能正确读取
    合并basic_info和data_info的数据，根据system_code进行数据整合
    返回DataFrame格式数据和数据字典
    """
    # 定义数据字典
    origin_dict = {}
    all_data = []

    # 获取目录下所有Excel文件
    excel_files = glob.glob(os.path.join(origin_dir, "*.xlsx")) + glob.glob(
        os.path.join(origin_dir, "*.xls")
    )

    if not excel_files:
        print(f"警告：在目录 {origin_dir} 中未找到Excel文件")
        return pd.DataFrame(), origin_dict

    # 定义所有可能需要的列名（包含别名）
    # 基本信息列
    basic_columns_mapping = {
        "一级单位": ["一级单位", "单位", "一级单位名称", "单位名称"],
        "系统编号": ["系统编号", "编号", "系统代码", "系统ID"],
        "信息系统名称": ["信息系统名称", "系统名称", "应用系统名称", "系统"],
    }

    # 数据信息列
    data_columns_mapping = {
        "（近一个月内）累计插入与更新数据库记录数量（条数）": [
            "（近一个月内）累计插入与更新数据库记录数量（条数）",
            "累计插入与更新数据库记录数量",
            "插入与更新数据库记录数量",
            "数据库插入更新量",
        ],
        "（近一个月内）累计查询数据库记录数量（条数）": [
            "（近一个月内）累计查询数据库记录数量（条数）",
            "累计查询数据库记录数量",
            "查询数据库记录数量",
            "数据库查询量",
        ],
        "数据库已有数据记录条数（条数）": [
            "数据库已有数据记录条数（条数）",
            "数据库已有记录条数",
            "数据库记录总数",
            "数据库总记录数",
        ],
    }

    # 遍历所有目录下的 excel 文件
    for file_path in excel_files:
        try:

            file_name = os.path.basename(file_path)
            # 如果文件名包含“~$”，则跳过
            if "~$" in file_name:
                print(f"跳过文件: {file_name}（临时文件）")
                continue

            print(f"⚪ 正在处理文件: {file_name}")

            # 定义匹配成功的 df
            basic_info = None
            data_info = None

            # 定义最佳匹配结果
            best_basic_found = {}
            best_data_found = {}

            # 先尝试 skip_rows=2（用户确认有效的跳过行数）
            # 优先尝试 skip_rows=2，然后尝试0-10行
            # for skip_rows in [2] + [i for i in range(0, 11) if i != 2]:
            # 尝试不同的跳过行数，从0到10行
            for skip_rows in range(0, 11):
                try:
                    df = pd.read_excel(file_path, skiprows=skip_rows)
                    if df.empty:
                        continue

                    # 清理列名
                    df = clean_columns(df)

                    # 查找基本信息列
                    if not best_basic_found:
                        # 返回所有找到的基本信息列
                        basic_found = find_columns(df, basic_columns_mapping)
                        if "系统编号" in basic_found:  # 只要找到系统编号就接受
                            best_basic_found = basic_found
                            basic_info = df
                            print(
                                f">> 在跳过{skip_rows}行后找到基本信息，系统编号列: {basic_found['系统编号']}"
                            )

                    # 查找数据信息列
                    if not best_data_found:
                        # 返回所有找到的数据信息列
                        data_found = find_columns(df, data_columns_mapping)
                        if len(data_found) > 0:  # 找到任意数据列就接受
                            best_data_found = data_found
                            data_info = df
                            print(
                                f">> 在跳过{skip_rows}行后找到数据信息列: {list(data_found.values())}"
                            )

                    # 如果两种信息都找到了，立即退出循环
                    if best_basic_found and best_data_found:
                        break

                except Exception as e:
                    print(f">> 跳过{skip_rows}行时出错: {e}")
                    continue

            # 检查是否找到了所需列
            if not best_basic_found or "系统编号" not in best_basic_found:
                print(f"警告：文件 未找到系统编号列")
                continue

            # 定义异常关键词列表
            invalid_keywords = [
                "字段信息",
                "测试",
                "示例",
                "模板",
                "样例",
                "test",
                "demo",
                "example",
            ]

            # 获取所有有效列名
            system_code_col = best_basic_found["系统编号"]
            unit_col = best_basic_found.get("一级单位", "")
            system_name_col = best_basic_found.get("信息系统名称", "")

            # 获取数据列名
            insert_update_col = ""
            query_col = ""
            total_records_col = ""

            # 检查数据列是否存在
            if data_found:
                insert_update_col = data_found.get(
                    "（近一个月内）累计插入与更新数据库记录数量（条数）", ""
                )
                query_col = data_found.get(
                    "（近一个月内）累计查询数据库记录数量（条数）", ""
                )
                total_records_col = data_found.get("数据库已有数据记录条数（条数）", "")

            print("---")
            # 逐行处理数据
            processed_rows = 0
            skipped_rows = 0

            # 使用basic_info进行逐行处理
            for idx, row in basic_info.iterrows():
                try:
                    # 检查系统编号列是否存在
                    if system_code_col not in basic_info.columns:
                        print(f">>>> 跳过行 {idx+1}: 系统编号列不存在")
                        continue

                    # 获取系统编号
                    system_code = str(row[system_code_col]).strip()

                    # 检查系统编号是否为空
                    if (
                        not system_code
                        or system_code.lower() == "nan"
                        or system_code == ""
                    ):
                        print(f">>>> 跳过行 {idx+1}: 系统编号为空")
                        continue

                    # 获取一级单位
                    unit = ""
                    if unit_col and unit_col in basic_info.columns:
                        unit = str(row[unit_col]).strip()

                    # 检查一级单位是否包含异常关键词
                    invalid_keywords_lower = [k.lower() for k in invalid_keywords]
                    if unit and any(
                        keyword in str(unit).lower()
                        for keyword in invalid_keywords_lower
                    ):
                        print(f">>>> 跳过行 {idx+1}: 一级单位包含异常关键词 '{unit}'")
                        skipped_rows += 1
                        continue

                    # 如果一级单位为空，填入"/"
                    if not unit or unit.lower() == "nan" or unit.strip() == "":
                        print(f">>>> 跳过行 {idx+1}: 一级单位为空")
                        unit = "未填写一级单位"

                    # 获取系统名称
                    system_name = ""
                    if system_name_col and system_name_col in basic_info.columns:
                        system_name = str(row[system_name_col]).strip()
                        if not system_name or system_name.lower() == "nan":
                            print(f">>>> 跳过行 {idx+1}: 系统名称为空")
                            system_name = "未填写系统名称"

                    # 获取数据信息
                    insert_update = 0
                    query = 0
                    total_records = 0

                    # 从data_info获取数据，如果存在对应行
                    if data_info is not None and idx < len(data_info):
                        data_row = data_info.iloc[idx]

                        # 获取插入更新数量
                        if insert_update_col and insert_update_col in data_info.columns:
                            val = str(data_row[insert_update_col]).strip()
                            try:
                                insert_update = (
                                    float(val) if val and val != "nan" else 0
                                )
                            except:
                                insert_update = 0

                        # 获取查询数量
                        if query_col and query_col in data_info.columns:
                            val = str(data_row[query_col]).strip()
                            try:
                                query = float(val) if val and val != "nan" else 0
                            except:
                                query = 0

                        # 获取总记录数
                        if total_records_col and total_records_col in data_info.columns:
                            val = str(data_row[total_records_col]).strip()
                            try:
                                total_records = (
                                    float(val) if val and val != "nan" else 0
                                )
                            except:
                                total_records = 0

                    # 转换为按天数据（假设一个月30天）
                    daily_insert_update = int(insert_update / 30) if insert_update > 0 else 0
                    daily_query = int(query / 30) if query > 0 else 0
                    daily_total_records = int(total_records / 30) if total_records > 0 else 0

                    # 存储到数据字典
                    origin_dict[system_code] = {
                        "一级单位": unit,
                        "系统名称": system_name,
                        "（近一个月内）累计插入与更新数据库记录数量（条数）": insert_update,
                        "（近一个月内）累计查询数据库记录数量（条数）": query,
                        "数据库已有数据记录条数（条数）": total_records,
                        "（按天）累计插入与更新数据库记录数量（条数）": daily_insert_update,
                        "（按天）累计查询数据库记录数量（条数）": daily_query,
                        "（按天）数据库已有数据记录条数（条数）": daily_total_records,
                    }

                    # 添加到DataFrame数据列表
                    all_data.append(
                        {
                            "一级单位": unit,
                            "系统编号": system_code,
                            "系统名称": system_name,
                            "（近一个月内）累计插入与更新数据库记录数量（条数）": insert_update,
                            "（近一个月内）累计查询数据库记录数量（条数）": query,
                            "数据库已有数据记录条数（条数）": total_records,
                            "（按天）累计插入与更新数据库记录数量（条数）": daily_insert_update,
                            "（按天）累计查询数据库记录数量（条数）": daily_query,
                            "（按天）数据库已有数据记录条数（条数）": daily_total_records,
                        }
                    )

                    processed_rows += 1

                except Exception as e:
                    print(f"  处理行 {idx+1} 时出错: {e}")
                    continue

            print(
                f"  文件 {file_name} 读取完成: 成功处理 {processed_rows} 行, 跳过 {skipped_rows} 行"
            )
        except Exception as e:
            print(f"  文件 {file_name} 处理出错: {e}")
            continue

    # 创建DataFrame
    columns = [
        "一级单位",
        "系统编号",
        "系统名称",
        "（近一个月内）累计插入与更新数据库记录数量（条数）",
        "（近一个月内）累计查询数据库记录数量（条数）",
        "数据库已有数据记录条数（条数）",
        "（按天）累计插入与更新数据库记录数量（条数）",
        "（按天）累计查询数据库记录数量（条数）",
        "（按天）数据库已有数据记录条数（条数）",
    ]
    result_df = pd.DataFrame(all_data, columns=columns)

    # 确保系统编号为字符串类型
    result_df["系统编号"] = result_df["系统编号"].astype(str)

    # 去重：按系统编号去重，保留第一个
    result_df = result_df.drop_duplicates(subset=["系统编号"], keep="first")

    print(f"---------\n成功处理 {len(result_df)} 个系统的数据")

    return result_df, origin_dict


"""
description: 合并数据
param {*} input_df 输入数据
param {*} input_dict 输入数据字典
param {*} origin_dict 原始数据字典
param {*} output_columns 输出列
return {*} 合并后的数据
"""


def merge_data(input_df, input_dict, origin_dict, output_columns):
    """
    合并输入数据和原始数据
    返回合并后的DataFrame
    """
    merged_data = []
    match_system_code = []

    for _, input_row in input_df.iterrows():
        system_code = str(input_row["系统编号"]).strip()

        # 获取原始数据
        origin_data = origin_dict.get(system_code, {})
        daily_insert_update = origin_data.get(
            "（按天）累计插入与更新数据库记录数量（条数）", 0
        )
        daily_query = origin_data.get("（按天）累计查询数据库记录数量（条数）", 0)
        daily_total_records = origin_data.get(
            "（按天）数据库已有数据记录条数（条数）", 0
        )

        # 合并数据
        merged_row = input_row.to_dict()

        # 添加原始数据字段
        merged_row["（按天）累计插入与更新数据库记录数量（条数）"] = daily_insert_update
        merged_row["（按天）累计查询数据库记录数量（条数）"] = daily_query
        merged_row["（按天）数据库已有数据记录条数（条数）"] = daily_total_records

        # 计算差异率 - 优化后的逻辑
        # 差异率A: 数据库更新差异率 = |工具值 - 用户值| / 用户值 * 100%
        tool_db_ops = merged_row.get("数据库操作量", 0)
        user_insert_update = merged_row.get(
            "（按天）累计插入与更新数据库记录数量（条数）", 0
        )

        # 计算差异率
        # 差异率A: 数据库更新差异率 = |工具值 - 用户值| / 用户值 * 100%
        tool_db_ops = merged_row.get("数据库操作量", 0)
        user_insert_update = merged_row.get("（按天）累计插入与更新数据库记录数量（条数）", 0)
        db_update_diff_rate = safe_divide(tool_db_ops, user_insert_update)

        # 差异率B: 数据库查询差异率 = |工具值 - 用户值| / 用户值 * 100%
        tool_db_visits = merged_row.get("数据库访问量", 0)
        tool_db_operations = merged_row.get("数据库操作量", 0)
        tool_query_value = tool_db_visits - tool_db_operations
        user_query_value = merged_row.get("（按天）累计查询数据库记录数量（条数）", 0)
        db_query_diff_rate = safe_divide(tool_query_value, user_query_value)

        # 使用新的列名
        merged_row["数据库更新差异率(%)"] = db_update_diff_rate
        merged_row["数据库查询差异率(%)"] = db_query_diff_rate

        merged_data.append(merged_row)

    # 打印成功匹配的一级单位和系统编号并逐行换行
    print("成功匹配的一级单位和系统编号为:")

    for system_code in origin_dict.keys():
        if system_code in input_dict:
            # print(f"一级单位: {origin_dict[system_code]['一级单位']}, 系统编号: {system_code}")
            match_system_code.append(
                {
                    "一级单位": origin_dict[system_code]["一级单位"],
                    "系统编号": system_code,
                    "系统名称": origin_dict[system_code]["系统名称"],
                }
            )

    # 创建DataFrame并确保所有列都存在
    merged_df = pd.DataFrame(merged_data)
    for col in output_columns:
        if col not in merged_df.columns:
            merged_df[col] = np.nan

    merged_df = merged_df[output_columns]
    return match_system_code, merged_df


"""

    主函数
"""


def main():
    """主函数"""
    args = parse_arguments()
    input_file = os.path.abspath(args.input_file)
    origin_dir = os.path.abspath(args.origin_dir)
    output_dir = os.path.abspath(args.output_dir)

    print("=== 数据库数据分析工具 ===")
    print(f"输入文件: {input_file}")
    print(f"原始数据目录: {origin_dir}")
    print(f"输出目录: {output_dir}")

    # 检查文件和目录是否存在
    if not os.path.exists(input_file):
        print(f"错误：输入文件 {input_file} 不存在")
        sys.exit(1)

    if not os.path.exists(origin_dir):
        print(f"错误：原始数据目录 {origin_dir} 不存在")
        sys.exit(1)

    # 步骤1: 读取输入文件
    print("\n========= 步骤1: 读取输入文件... ========= ")
    input_df, input_dict = read_input_file(input_file)

    # 保存输入文件处理结果
    input_columns = [
        "序号",
        "日期",
        # "时间",
        "一级单位",
        "系统编号",
        "系统名称",
        # "主机数量",
        "数据库操作量",
        "数据库访问量",
        # "数据库访问流量",
    ]
    save_results_to_file(
        input_df,
        output_dir,
        "input_data_processed.xlsx",
        input_columns,
        sheet_name="Sheet1",
    )
    print(f"成功读取 {len(input_df)} 条记录")

    # 步骤2: 读取原始数据文件
    print("\n========= 步骤2: 读取原始数据文件... ========= ")

    origin_df, origin_dict = read_origin_files(origin_dir)

    # 保存原始数据处理结果
    origin_columns = [
        "一级单位",
        "系统编号",
        "系统名称",
        "（按天）累计插入与更新数据库记录数量（条数）",
        "（按天）累计查询数据库记录数量（条数）",
        "（按天）数据库已有数据记录条数（条数）",
    ]
    save_results_to_file(
        origin_df,
        output_dir,
        "origin_data_processed.xlsx",
        origin_columns,
        sheet_name="Sheet1",
    )

    # 步骤3: 合并数据
    print("\n========= 步骤3: 合并数据... ========= ")
    # 定义输出列顺序

    # 保存匹配系统编号处理结果
    match_system_code_columns = [
        "一级单位",
        "系统编号",
        "系统名称",
    ]

    # 保存合并数据处理结果
    output_columns = [
        "序号",
        "日期",
        # "时间",
        "一级单位",
        "系统编号",
        "系统名称",
        # "主机数量",
        "数据库操作量",
        "数据库访问量",
        # "数据库访问流量",
        "（按天）累计插入与更新数据库记录数量（条数）",
        "（按天）累计查询数据库记录数量（条数）",
        "（按天）数据库已有数据记录条数（条数）",
        "数据库更新差异率(%)",
        "数据库查询差异率(%)",
    ]

    # 合并数据
    match_system_code, result_df = merge_data(
        input_df, input_dict, origin_dict, output_columns
    )

    # 步骤4和5: 计算差异率并输出结果
    print("\n========= 步骤4-5: 计算差异率并输出结果... ========= ")

    # 转换为DataFrame
    match_system_code_df = pd.DataFrame(
        match_system_code, columns=match_system_code_columns
    )
    print(match_system_code_df.head())

    # 提取需要匹配的system_code列表
    match_codes = match_system_code_df["系统编号"].unique()

    # 筛选出result_df中system_code在匹配列表中的行
    result_df = result_df[result_df["系统编号"].isin(match_codes)]

    # 保存 match_system_code, result_df 到 Excel 文件
    export_results_to_excel(
        output_dir=output_dir,
        filename="result_database_analysis.xlsx",
        匹配的系统名称=match_system_code,
        计算结果=result_df,
    )

    # 打印统计信息
    print(f"\n总计处理记录数: {len(result_df)}")
    print(
        f"成功匹配系统数: {len([x for x in result_df['系统编号'] if str(x) in origin_dict])}"
    )

    # 显示前几条结果
    print("\n前5条结果预览:")
    print(result_df.head())

    # 输出当前执行时间
    print(f"当前执行时间: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

    return result_df
    


"""
    程序入口
"""
if __name__ == "__main__":
    try:
        result = main()
    except KeyboardInterrupt:
        print("\n用户中断执行")
        sys.exit(1)
    except Exception as e:
        print(f"执行过程中发生错误: {e}")
        sys.exit(1)
