{"cells": [{"cell_type": "code", "execution_count": 88, "id": "95d41a77", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import glob\n", "import os"]}, {"cell_type": "code", "execution_count": 89, "id": "7be8394e", "metadata": {}, "outputs": [], "source": ["# origin_dir = r'D:\\Working\\ZhangCe\\项目材料\\广州市政数局\\系统效能评估\\运维知识库\\数据库指标专项核实\\origin\\汇总'\n", "origin_file = r'D:\\Working\\ZhangCe\\项目材料\\广州市政数局\\系统效能评估\\运维知识库\\数据库指标专项核实\\origin\\汇总\\广州市民政局.xlsx'\n", "\n", "# excel_files = glob.glob(os.path.join(origin_dir, \"*.xlsx\")) + glob.glob(os.path.join(origin_dir, \"*.xls\"))\n"]}, {"cell_type": "code", "execution_count": 90, "id": "a69f981f", "metadata": {}, "outputs": [], "source": ["def clean_columns(df):\n", "    df.columns = [str(col).replace('\\n', '').replace('\\r', '').strip() for col in df.columns]\n", "    return df"]}, {"cell_type": "code", "execution_count": 91, "id": "bf34bdf1", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["系统编号列存在\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>序号</th>\n", "      <th>一级单位</th>\n", "      <th>当前所属单位</th>\n", "      <th>系统编号</th>\n", "      <th>信息系统名称</th>\n", "      <th>信息系统状态</th>\n", "      <th>访问终端</th>\n", "      <th>系统建设主体</th>\n", "      <th>数据编目</th>\n", "      <th>数据共享</th>\n", "      <th>...</th>\n", "      <th>系统活跃度</th>\n", "      <th>Unnamed: 14</th>\n", "      <th>Unnamed: 15</th>\n", "      <th>Unnamed: 16</th>\n", "      <th>数据新鲜度</th>\n", "      <th>Unnamed: 18</th>\n", "      <th>Unnamed: 19</th>\n", "      <th>系统应用效果</th>\n", "      <th>是/否具备运营条件</th>\n", "      <th>备注</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>...</td>\n", "      <td>非固定用户数系统</td>\n", "      <td>NaN</td>\n", "      <td>固定用户数系统</td>\n", "      <td>NaN</td>\n", "      <td>（近一个月内）累计插入与更新数据库记录数量（条数）</td>\n", "      <td>（近一个月内）累计查询数据库记录数量\\n（条数）</td>\n", "      <td>数据库已有数据记录条数（条数）</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>...</td>\n", "      <td>（近一个月内）日最高用户访问数量（次数）</td>\n", "      <td>（历史）日最高用户访问数量（次数）</td>\n", "      <td>（近一个月内）日最高已注册用户访问数（个）</td>\n", "      <td>累计已注册用户总数（个）</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>1</td>\n", "      <td>广州市民政局</td>\n", "      <td>广州市民政局</td>\n", "      <td>GZXT-JS-N-025-000697</td>\n", "      <td>社会救助业务一体化管理子平台</td>\n", "      <td>已建</td>\n", "      <td>PC 端（含大屏展示等）</td>\n", "      <td>广州市民政局</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>...</td>\n", "      <td>/</td>\n", "      <td>/</td>\n", "      <td>1223</td>\n", "      <td>1957</td>\n", "      <td>53420</td>\n", "      <td>3530100</td>\n", "      <td>191510283</td>\n", "      <td>对标社会救助审核审批业务，通过提供低保、低边、特困、支出型困难家庭、临时救助等业务全流程审核...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>2</td>\n", "      <td>广州市民政局</td>\n", "      <td>广州市民政局</td>\n", "      <td>GZXT-JS-N-025-000695</td>\n", "      <td>广州市困难群众兜底保障安全网综合平台</td>\n", "      <td>已建</td>\n", "      <td>PC 端（含大屏展示等）</td>\n", "      <td>广州市民政局</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>...</td>\n", "      <td>/</td>\n", "      <td>/</td>\n", "      <td>806</td>\n", "      <td>4043</td>\n", "      <td>23420</td>\n", "      <td>1890000</td>\n", "      <td>89239010</td>\n", "      <td>对标服务类社会救助业务，通过提供跨部门数据整合、智能分析及动态监测预警功能，服务于服务于低保...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>3</td>\n", "      <td>广州市民政局</td>\n", "      <td>广州市家庭经济核对和养老服务指导中心</td>\n", "      <td>GZXT-JS-N-025-025816</td>\n", "      <td>广州市居民家庭经济状况智能化核对平台</td>\n", "      <td>已建</td>\n", "      <td>PC 端（含大屏展示等）</td>\n", "      <td>广州市家庭经济核对和养老服务指导中心</td>\n", "      <td>/</td>\n", "      <td>/</td>\n", "      <td>...</td>\n", "      <td>/</td>\n", "      <td>/</td>\n", "      <td>343</td>\n", "      <td>455</td>\n", "      <td>310009601</td>\n", "      <td>401544386</td>\n", "      <td>2602453495</td>\n", "      <td>通过开展信息共享，对申请人的收入、财产、支出等信息进行核对，为各类社会救助、社会福利事项管理...</td>\n", "      <td>否</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>5 rows × 23 columns</p>\n", "</div>"], "text/plain": ["    序号    一级单位              当前所属单位                  系统编号              信息系统名称  \\\n", "0  NaN     NaN                 NaN                   NaN                 NaN   \n", "1  NaN     NaN                 NaN                   NaN                 NaN   \n", "2    1  广州市民政局              广州市民政局  GZXT-JS-N-025-000697      社会救助业务一体化管理子平台   \n", "3    2  广州市民政局              广州市民政局  GZXT-JS-N-025-000695  广州市困难群众兜底保障安全网综合平台   \n", "4    3  广州市民政局  广州市家庭经济核对和养老服务指导中心  GZXT-JS-N-025-025816  广州市居民家庭经济状况智能化核对平台   \n", "\n", "  信息系统状态          访问终端              系统建设主体 数据编目 数据共享  ...  \\\n", "0    NaN           NaN                 NaN  NaN  NaN  ...   \n", "1    NaN           NaN                 NaN  NaN  NaN  ...   \n", "2     已建  PC 端（含大屏展示等）              广州市民政局  NaN  NaN  ...   \n", "3     已建  PC 端（含大屏展示等）              广州市民政局  NaN  NaN  ...   \n", "4     已建  PC 端（含大屏展示等）  广州市家庭经济核对和养老服务指导中心    /    /  ...   \n", "\n", "                  系统活跃度        Unnamed: 14            Unnamed: 15  \\\n", "0              非固定用户数系统                NaN                固定用户数系统   \n", "1  （近一个月内）日最高用户访问数量（次数）  （历史）日最高用户访问数量（次数）  （近一个月内）日最高已注册用户访问数（个）   \n", "2                     /                  /                   1223   \n", "3                     /                  /                    806   \n", "4                     /                  /                    343   \n", "\n", "    Unnamed: 16                      数据新鲜度               Unnamed: 18  \\\n", "0           NaN  （近一个月内）累计插入与更新数据库记录数量（条数）  （近一个月内）累计查询数据库记录数量\\n（条数）   \n", "1  累计已注册用户总数（个）                        NaN                       NaN   \n", "2          1957                      53420                   3530100   \n", "3          4043                      23420                   1890000   \n", "4           455                  310009601                 401544386   \n", "\n", "       Unnamed: 19                                             系统应用效果  \\\n", "0  数据库已有数据记录条数（条数）                                                NaN   \n", "1              NaN                                                NaN   \n", "2        191510283  对标社会救助审核审批业务，通过提供低保、低边、特困、支出型困难家庭、临时救助等业务全流程审核...   \n", "3         89239010  对标服务类社会救助业务，通过提供跨部门数据整合、智能分析及动态监测预警功能，服务于服务于低保...   \n", "4       2602453495  通过开展信息共享，对申请人的收入、财产、支出等信息进行核对，为各类社会救助、社会福利事项管理...   \n", "\n", "  是/否具备运营条件   备注  \n", "0       NaN  NaN  \n", "1       NaN  NaN  \n", "2       NaN  NaN  \n", "3       NaN  NaN  \n", "4         否  NaN  \n", "\n", "[5 rows x 23 columns]"]}, "execution_count": 91, "metadata": {}, "output_type": "execute_result"}], "source": ["df = pd.read_excel(origin_file, skiprows=2)\n", "columns = df.columns.to_list()\n", "if \"系统编号\" in columns:\n", "    print(\"系统编号列存在\")\n", "\n", "df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "b7adc828", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.4"}}, "nbformat": 4, "nbformat_minor": 5}