{"cells": [{"cell_type": "code", "execution_count": 1, "id": "66d65f2a", "metadata": {}, "outputs": [], "source": ["import os\n", "import sys\n", "import pandas as pd\n", "import logging\n", "from glob import glob"]}, {"cell_type": "code", "execution_count": 2, "id": "33212fa4", "metadata": {}, "outputs": [], "source": ["def setup_logging(log_file=None, debug=False):\n", "    \"\"\"\n", "    设置日志器\\n\n", "    :param log_file: 日志文件路径，如果为None则不写入文件\\n\n", "    :param debug: 是否开启调试模式，调试模式下日志级别为DEBUG，否则为INFO\\n\n", "    :return: None\n", "    \"\"\"\n", "    handlers = []\n", "    console_handler = logging.StreamHandler(sys.stderr)\n", "    handlers.append(console_handler)\n", "\n", "    if log_file:\n", "        file_handler = logging.FileHandler(log_file)\n", "        handlers.append(file_handler)\n", "\n", "    log_format = \"%(asctime)s [%(levelname)s] %(name)s: %(message)s\" if debug else \"%(levelname)s: %(message)s\"\n", "    \n", "    logging.basicConfig(\n", "        level=logging.DEBUG if debug else logging.INFO,\n", "        format=log_format,\n", "        handlers=handlers,\n", "        #encoding='utf-8'\n", "    )\n", "\n", "setup_logging(debug=True)\n", "logger =  logging.getLogger(__name__)"]}, {"cell_type": "code", "execution_count": 3, "id": "6f0e8e18", "metadata": {}, "outputs": [], "source": ["def parse_origin_file(origin_file_path,sheet_name,required_columns,skiprows=None):\n", "    \"\"\"\n", "    通过Pandas解析原始文件内容\\n\n", "    :param origin_file_path: 原始Excel文件路径\\n\n", "    :param sheet_name: 要解析的sheet名称\\n\n", "    :return: DataFrame对象\\n\n", "    \"\"\"\n", "    try:\n", "        logger.info(f\"开始解析文件：{origin_file_path}，sheet名称：{sheet_name}\")\n", "        # 读取Excel文件，可根据需要调整sheet_name参数\n", "        df = pd.read_excel(origin_file_path, engine='openpyxl', sheet_name=sheet_name,skiprows=skiprows)\n", "        df.dropna(how='all', inplace=True)  # 删除全为空值的行\n", "        # 检查是否包含必要的列\n", "        for col in required_columns:\n", "            if col not in df.columns:\n", "                logger.error(f\"错误：文档中缺少必要的列：{col}，请检查文档格式。\", exc_info=True)\n", "                raise ValueError(f\"缺少必要的列：{col}\")\n", "        return df\n", "    except FileNotFoundError:\n", "        logger.error(f\"错误：未找到{origin_file_path}文件，请检查路径和文件名是否正确。\",exc_info=True)\n", "        raise FileNotFoundError(f\"未找到文件：{origin_file_path}\")\n", "    except Exception as e:\n", "        logger.error(f\"读取文档{origin_file_path}过程中发生错误：{str(e)}\",exc_info=True)\n", "        raise Exception(f\"读取文档{origin_file_path}时发生错误：{str(e)}\")"]}, {"cell_type": "code", "execution_count": 4, "id": "16a64b36", "metadata": {}, "outputs": [], "source": ["def get_reflect_ip(ip):\n", "    if isinstance(ip,float):\n", "        return \"\"\n", "    if \".\" in ip:\n", "        ip_list = ip.split(\".\")\n", "        for i in range(3,5):\n", "            match_ip = \".\".join(ip_list[0:i])\n", "            reflect_ip =  reflect.get(match_ip,\"\")\n", "            if reflect_ip == \"\":\n", "                # logger.warning(\"ip:{} is not in reflect.json\".format(ip))\n", "                continue\n", "            else:\n", "                return reflect_ip+\".\"+ip_list[3] if i == 3 else reflect_ip\n", "        else:\n", "            return \"\"\n", "    else:\n", "        return \"\""]}, {"cell_type": "code", "execution_count": 5, "id": "e7835f5e", "metadata": {}, "outputs": [], "source": ["def merge_excel_files(folder_path, output_file=None):\n", "    \"\"\"\n", "    合并文件夹下所有Excel文件到一个DataFrame（或输出到文件）\n", "    \n", "    参数:\n", "    folder_path (str): 包含Excel文件的文件夹路径\n", "    output_file (str, optional): 合并结果保存路径（.xlsx或.csv格式），不提供则返回DataFrame\n", "    \n", "    返回:\n", "    DataFrame (如果output_file=None) 或 None (结果直接保存到文件)\n", "    \"\"\"\n", "    # 获取所有Excel文件路径\n", "    excel_files = glob(os.path.join(folder_path, \"*.xlsx\")) + \\\n", "        glob(os.path.join(folder_path, \"*.xls\"))\n", "\n", "    # 跳过文件名包含“固化数据”的文件\n", "    excel_files = [f for f in excel_files if \"固化数据\" not in os.path.basename(f)]\n", "\n", "    if not excel_files:\n", "        raise FileNotFoundError(f\"在 {folder_path} 中未找到Excel文件\")\n", "    # 存储所有DataFrame\n", "    dfs = []\n", "    for file in excel_files:\n", "        try:\n", "            # 读取单个Excel文件（包含多个sheet时只读取第一个）\n", "            df = pd.read_excel(file, sheet_name=0, usecols=[\n", "                               '所属一级单位', '系统名称', '系统编号', '主机用途', 'IP地址', '端口'])\n", "            dfs.append(df)\n", "            logger.info(\n", "                f\"已加载: {os.path.basename(file)} ({len(df)}行 × {len(df.columns)}列)\")\n", "        except Exception as e:\n", "            logger.info(f\"跳过 {file} - 读取错误: {str(e)}\")\n", "    if not dfs:\n", "        raise ValueError(\"所有文件读取失败，无有效数据\")\n", "    # 合并所有DataFrame（自动对齐列名）\n", "    merged_df = pd.concat(dfs, ignore_index=True)\n", "    merge_data = merged_df.values.tolist()\n", "    for index, row in merged_df.iterrows():\n", "        if pd.isna(row.端口):\n", "            continue\n", "        # 处理用\"、\"分割的端口\n", "        if not isinstance(row.端口, int) and not isinstance(row.端口, float) and \"、\" in row.端口:\n", "            port_list = row.端口.split(\"、\")\n", "            for port in port_list:\n", "                row['端口'] = port\n", "                merge_data.append(row.values.tolist())\n", "    regular_df = pd.DataFrame(\n", "        columns=['所属一级单位', '系统名称', '系统编号', '主机用途', 'IP地址', '端口'], data=merge_data)\n", "    regular_df[\"私网IP\"] = regular_df[\"IP地址\"].apply(get_reflect_ip)\n", "    logger.info(\n", "        f\"\\n合并完成: 总行数={len(regular_df)}, 总列数={len(regular_df.columns)}\")\n", "    # 根据参数决定输出方式\n", "    if output_file:\n", "        if output_file.endswith('.csv'):\n", "            regular_df.to_csv(output_file, index=False)\n", "        else:\n", "            regular_df.to_excel(output_file, index=False)\n", "        logger.info(f\"结果已保存至: {output_file}\")\n", "    return regular_df"]}, {"cell_type": "code", "execution_count": 6, "id": "ee7a7d2c", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-08-13 18:38:19,146 [INFO] __main__: 开始解析文件：D:\\Working\\ZhangCe\\项目材料\\广州市政数局\\系统效能评估\\运维知识库\\Python生成csv\\业务私网匹配表.xlsx，sheet名称：Sheet1\n"]}], "source": ["reflect_file = r\"D:\\Working\\ZhangCe\\项目材料\\广州市政数局\\系统效能评估\\运维知识库\\Python生成csv\\业务私网匹配表.xlsx\"\n", "reflect_sheet_name = \"Sheet1\"\n", "reflect_df = parse_origin_file(reflect_file,reflect_sheet_name,[])"]}, {"cell_type": "code", "execution_count": 7, "id": "c9422f25", "metadata": {}, "outputs": [], "source": ["# 构造外网与内网映射关系\n", "reflect = {}\n", "for index,row in reflect_df.iterrows():\n", "    reflect[row['业务IP段']] = row['关联私网IP段']"]}, {"cell_type": "code", "execution_count": 8, "id": "f41d9b1f", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-08-13 18:38:19,487 [INFO] __main__: 跳过 D:\\Working\\ZhangCe\\项目材料\\广州市政数局\\系统效能评估\\生产环境探针测试&核对\\固化csv\\数据固化\\~$市人力资源社会保障局.xlsx - 读取错误: Excel file format cannot be determined, you must specify an engine manually.\n", "2025-08-13 18:38:19,491 [INFO] __main__: 跳过 D:\\Working\\ZhangCe\\项目材料\\广州市政数局\\系统效能评估\\生产环境探针测试&核对\\固化csv\\数据固化\\~$市住房城乡建设局.xlsx - 读取错误: Excel file format cannot be determined, you must specify an engine manually.\n", "2025-08-13 18:38:19,493 [INFO] __main__: 跳过 D:\\Working\\ZhangCe\\项目材料\\广州市政数局\\系统效能评估\\生产环境探针测试&核对\\固化csv\\数据固化\\~$市体育局.xlsx - 读取错误: Excel file format cannot be determined, you must specify an engine manually.\n", "2025-08-13 18:38:19,497 [INFO] __main__: 跳过 D:\\Working\\ZhangCe\\项目材料\\广州市政数局\\系统效能评估\\生产环境探针测试&核对\\固化csv\\数据固化\\~$市医保局.xlsx - 读取错误: Excel file format cannot be determined, you must specify an engine manually.\n", "2025-08-13 18:38:19,501 [INFO] __main__: 跳过 D:\\Working\\ZhangCe\\项目材料\\广州市政数局\\系统效能评估\\生产环境探针测试&核对\\固化csv\\数据固化\\~$市发展改革委.xlsx - 读取错误: Excel file format cannot be determined, you must specify an engine manually.\n", "2025-08-13 18:38:19,505 [INFO] __main__: 跳过 D:\\Working\\ZhangCe\\项目材料\\广州市政数局\\系统效能评估\\生产环境探针测试&核对\\固化csv\\数据固化\\~$市商务局.xlsx - 读取错误: Excel file format cannot be determined, you must specify an engine manually.\n", "2025-08-13 18:38:19,510 [INFO] __main__: 跳过 D:\\Working\\ZhangCe\\项目材料\\广州市政数局\\系统效能评估\\生产环境探针测试&核对\\固化csv\\数据固化\\~$市工业和信息化局.xlsx - 读取错误: Excel file format cannot be determined, you must specify an engine manually.\n", "2025-08-13 18:38:19,514 [INFO] __main__: 跳过 D:\\Working\\ZhangCe\\项目材料\\广州市政数局\\系统效能评估\\生产环境探针测试&核对\\固化csv\\数据固化\\~$市工业和信息化局_20250627.xlsx - 读取错误: Excel file format cannot be determined, you must specify an engine manually.\n", "2025-08-13 18:38:19,518 [INFO] __main__: 跳过 D:\\Working\\ZhangCe\\项目材料\\广州市政数局\\系统效能评估\\生产环境探针测试&核对\\固化csv\\数据固化\\~$市市场监管局.xlsx - 读取错误: Excel file format cannot be determined, you must specify an engine manually.\n", "2025-08-13 18:38:19,521 [INFO] __main__: 跳过 D:\\Working\\ZhangCe\\项目材料\\广州市政数局\\系统效能评估\\生产环境探针测试&核对\\固化csv\\数据固化\\~$市政务服务和数据管理局.xlsx - 读取错误: Excel file format cannot be determined, you must specify an engine manually.\n", "2025-08-13 18:38:19,524 [INFO] __main__: 跳过 D:\\Working\\ZhangCe\\项目材料\\广州市政数局\\系统效能评估\\生产环境探针测试&核对\\固化csv\\数据固化\\~$市文化广电旅游局.xlsx - 读取错误: Excel file format cannot be determined, you must specify an engine manually.\n", "2025-08-13 18:38:19,530 [INFO] __main__: 跳过 D:\\Working\\ZhangCe\\项目材料\\广州市政数局\\系统效能评估\\生产环境探针测试&核对\\固化csv\\数据固化\\~$市林业园林局.xlsx - 读取错误: Excel file format cannot be determined, you must specify an engine manually.\n", "2025-08-13 18:38:19,534 [INFO] __main__: 跳过 D:\\Working\\ZhangCe\\项目材料\\广州市政数局\\系统效能评估\\生产环境探针测试&核对\\固化csv\\数据固化\\~$市民政局.xlsx - 读取错误: Excel file format cannot be determined, you must specify an engine manually.\n", "2025-08-13 18:38:19,537 [INFO] __main__: 跳过 D:\\Working\\ZhangCe\\项目材料\\广州市政数局\\系统效能评估\\生产环境探针测试&核对\\固化csv\\数据固化\\~$市水务局.xlsx - 读取错误: Excel file format cannot be determined, you must specify an engine manually.\n", "2025-08-13 18:38:19,540 [INFO] __main__: 跳过 D:\\Working\\ZhangCe\\项目材料\\广州市政数局\\系统效能评估\\生产环境探针测试&核对\\固化csv\\数据固化\\~$市生态环境局.xlsx - 读取错误: Excel file format cannot be determined, you must specify an engine manually.\n", "2025-08-13 18:38:19,544 [INFO] __main__: 跳过 D:\\Working\\ZhangCe\\项目材料\\广州市政数局\\系统效能评估\\生产环境探针测试&核对\\固化csv\\数据固化\\~$市统计局.xlsx - 读取错误: Excel file format cannot be determined, you must specify an engine manually.\n", "2025-08-13 18:38:19,548 [INFO] __main__: 跳过 D:\\Working\\ZhangCe\\项目材料\\广州市政数局\\系统效能评估\\生产环境探针测试&核对\\固化csv\\数据固化\\~$市财政局.xlsx - 读取错误: Excel file format cannot be determined, you must specify an engine manually.\n", "2025-08-13 18:38:19,684 [INFO] __main__: 已加载: 市交通运输局.xlsx (1644行 × 6列)\n", "2025-08-13 18:38:19,793 [INFO] __main__: 已加载: 市人力资源社会保障局.xlsx (642行 × 6列)\n", "2025-08-13 18:38:19,876 [INFO] __main__: 已加载: 市住房城乡建设局.xlsx (816行 × 6列)\n", "2025-08-13 18:38:19,898 [INFO] __main__: 已加载: 市体育局.xlsx (76行 × 6列)\n", "2025-08-13 18:38:19,910 [INFO] __main__: 已加载: 市信访局.xlsx (22行 × 6列)\n", "2025-08-13 18:38:19,933 [INFO] __main__: 已加载: 市农业农村局.xlsx (92行 × 6列)\n", "2025-08-13 18:38:32,766 [INFO] __main__: 已加载: 市医保局.xlsx (2388行 × 6列)\n", "2025-08-13 18:38:32,844 [INFO] __main__: 已加载: 市卫生健康委.xlsx (507行 × 6列)\n", "2025-08-13 18:38:32,882 [INFO] __main__: 已加载: 市发展改革委.xlsx (250行 × 6列)\n", "2025-08-13 18:38:32,912 [INFO] __main__: 已加载: 市司法局.xlsx (128行 × 6列)\n", "2025-08-13 18:38:32,989 [INFO] __main__: 已加载: 市商务局.xlsx (683行 × 6列)\n", "2025-08-13 18:38:33,007 [INFO] __main__: 已加载: 市国资委.xlsx (67行 × 6列)\n", "2025-08-13 18:38:33,057 [INFO] __main__: 已加载: 市城市管理综合执法局.xlsx (484行 × 6列)\n", "2025-08-13 18:38:33,070 [INFO] __main__: 已加载: 市审计局.xlsx (41行 × 6列)\n", "2025-08-13 18:38:33,089 [INFO] __main__: 已加载: 市工业和信息化局.xlsx (64行 × 6列)\n", "2025-08-13 18:38:33,136 [INFO] __main__: 已加载: 市市场监管局.xlsx (405行 × 6列)\n", "2025-08-13 18:38:33,178 [INFO] __main__: 已加载: 市应急管理局.xlsx (308行 × 6列)\n", "2025-08-13 18:38:33,318 [INFO] __main__: 已加载: 市政务服务和数据管理局.xlsx (1782行 × 6列)\n", "2025-08-13 18:38:33,353 [INFO] __main__: 已加载: 市教育局.xlsx (257行 × 6列)\n", "2025-08-13 18:38:33,398 [INFO] __main__: 已加载: 市文化广电旅游局.xlsx (454行 × 6列)\n", "2025-08-13 18:38:33,434 [INFO] __main__: 已加载: 市林业园林局.xlsx (316行 × 6列)\n", "2025-08-13 18:38:33,468 [INFO] __main__: 已加载: 市民政局.xlsx (252行 × 6列)\n", "2025-08-13 18:38:33,479 [INFO] __main__: 已加载: 市民族宗教局.xlsx (21行 × 6列)\n", "2025-08-13 18:38:33,503 [INFO] __main__: 已加载: 市水务局.xlsx (112行 × 6列)\n", "2025-08-13 18:38:33,523 [INFO] __main__: 已加载: 市港务局.xlsx (90行 × 6列)\n", "2025-08-13 18:38:33,574 [INFO] __main__: 已加载: 市生态环境局.xlsx (445行 × 6列)\n", "2025-08-13 18:38:33,593 [INFO] __main__: 已加载: 市科技局.xlsx (53行 × 6列)\n", "2025-08-13 18:38:33,608 [INFO] __main__: 已加载: 市统计局.xlsx (66行 × 6列)\n", "2025-08-13 18:38:33,719 [INFO] __main__: 已加载: 市规划和自然资源局.xlsx (1239行 × 6列)\n", "2025-08-13 18:38:33,735 [INFO] __main__: 已加载: 市财政局.xlsx (54行 × 6列)\n", "2025-08-13 18:38:33,760 [INFO] __main__: 已加载: 市退役军人事务局.xlsx (114行 × 6列)\n", "2025-08-13 18:38:34,042 [INFO] __main__: \n", "合并完成: 总行数=13878, 总列数=7\n", "2025-08-13 18:38:35,045 [INFO] __main__: 结果已保存至: D:\\Working\\ZhangCe\\项目材料\\广州市政数局\\系统效能评估\\生产环境探针测试&核对\\固化csv\\数据固化\\固化数据.xlsx\n"]}], "source": ["confirm_file_path = r\"D:\\Working\\ZhangCe\\项目材料\\广州市政数局\\系统效能评估\\生产环境探针测试&核对\\固化csv\\数据固化\"\n", "confirm_df = merge_excel_files(confirm_file_path,rf\"{confirm_file_path}\\固化数据.xlsx\")"]}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.4"}}, "nbformat": 4, "nbformat_minor": 5}