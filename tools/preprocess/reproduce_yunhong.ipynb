{"cells": [{"cell_type": "code", "execution_count": 68, "id": "a9289c2c", "metadata": {}, "outputs": [], "source": ["import os\n", "import sys\n", "import pandas as pd\n", "import logging\n", "from glob import glob\n", "import json"]}, {"cell_type": "code", "execution_count": 69, "id": "846f0263", "metadata": {}, "outputs": [], "source": ["def setup_logging(log_file=None, debug=False):\n", "    \"\"\"\n", "    设置日志器\\n\n", "    :param log_file: 日志文件路径，如果为None则不写入文件\\n\n", "    :param debug: 是否开启调试模式，调试模式下日志级别为DEBUG，否则为INFO\\n\n", "    :return: None\n", "    \"\"\"\n", "    handlers = []\n", "    console_handler = logging.StreamHandler(sys.stderr)\n", "    handlers.append(console_handler)\n", "\n", "    if log_file:\n", "        file_handler = logging.FileHandler(log_file)\n", "        handlers.append(file_handler)\n", "\n", "    log_format = \"%(asctime)s [%(levelname)s] %(name)s: %(message)s\" if debug else \"%(levelname)s: %(message)s\"\n", "    \n", "    logging.basicConfig(\n", "        level=logging.DEBUG if debug else logging.INFO,\n", "        format=log_format,\n", "        handlers=handlers,\n", "        #encoding='utf-8'\n", "    )\n", "\n", "setup_logging(debug=True)\n", "logger =  logging.getLogger(__name__)"]}, {"cell_type": "code", "execution_count": 70, "id": "8207a003", "metadata": {}, "outputs": [], "source": ["# yunhong_file = r\"D:/原始数据/0627/0627/Data20250627/Data20250627_copy.xlsx\"\n", "# yunhong_sheet_name = \"关键端口列表\"\n", "yunhong_file = r\"D:\\原始数据\\0627\\0627\\数据固化\\固化数据.xlsx\"\n", "yunhong_sheet_name = \"Sheet1\"\n", "confirmed_data = r\"D:\\原始数据\\0627\\0627\\数据固化\"\n", "reflect_file = r\"D:/业务私网匹配表.xlsx\"\n", "reflect_sheet_name = \"Sheet1\"\n", "# system_list_file = r\"D:/原始数据/0620/0620/20250620效能评估系统清单-云宏/20250620效能评估系统清单-云宏_add.xlsx\"\n", "# system_sheet_name = \"结果-20250620\"\n", "manage_system_file = r\"D:/原始数据/应用类系统导出(2025-06-26+18_06_18).xlsx\"\n", "manage_sheet_name = \"基本信息\"\n", "online_system_file = r\"D:/downloads/edge下载/2025年评估.xlsx\"\n", "online_sheet_name = \"非参评系统下线登记\""]}, {"cell_type": "code", "execution_count": 71, "id": "7e90d822", "metadata": {}, "outputs": [], "source": ["def parse_origin_file(origin_file_path,sheet_name,required_columns,skiprows=None):\n", "    \"\"\"\n", "    通过Pandas解析原始文件内容\\n\n", "    :param origin_file_path: 原始Excel文件路径\\n\n", "    :param sheet_name: 要解析的sheet名称\\n\n", "    :return: DataFrame对象\\n\n", "    \"\"\"\n", "    try:\n", "        logger.info(f\"开始解析文件：{origin_file_path}，sheet名称：{sheet_name}\")\n", "        # 读取Excel文件，可根据需要调整sheet_name参数\n", "        df = pd.read_excel(origin_file_path, engine='openpyxl', sheet_name=sheet_name,skiprows=skiprows)\n", "        df.dropna(how='all', inplace=True)  # 删除全为空值的行\n", "        # 检查是否包含必要的列\n", "        for col in required_columns:\n", "            if col not in df.columns:\n", "                logger.error(f\"错误：文档中缺少必要的列：{col}，请检查文档格式。\", exc_info=True)\n", "                raise ValueError(f\"缺少必要的列：{col}\")\n", "        return df\n", "    except FileNotFoundError:\n", "        logger.error(f\"错误：未找到{origin_file_path}文件，请检查路径和文件名是否正确。\",exc_info=True)\n", "        raise FileNotFoundError(f\"未找到文件：{origin_file_path}\")\n", "    except Exception as e:\n", "        logger.error(f\"读取文档{origin_file_path}过程中发生错误：{str(e)}\",exc_info=True)\n", "        raise Exception(f\"读取文档{origin_file_path}时发生错误：{str(e)}\")"]}, {"cell_type": "code", "execution_count": 72, "id": "d2eb661f", "metadata": {}, "outputs": [], "source": ["import re\n", "def extract_ip_ports(text):\n", "\n", "    # 定义正则表达式匹配IPv4地址和端口\n", "    pattern = r'(\\d{1,3}\\.\\d{1,3}\\.\\d{1,3}\\.\\d{1,3})[:：](\\d{1,5})'\n", "    matches = re.findall(pattern, text)\n", "    \n", "    results = []\n", "    for ip, port in matches:\n", "        # 验证IP地址每个部分是否合法（0-255）\n", "        ip_parts = ip.split('.')\n", "        valid_ip = True\n", "        for part in ip_parts:\n", "            if not 0 <= int(part) <= 255:\n", "                valid_ip = False\n", "                break\n", "        \n", "        # 验证端口是否合法（0-65535）\n", "        valid_port = 0 <= int(port) <= 65535\n", "        \n", "        # 如果IP和端口都合法，则组合成\"ip:端口\"格式\n", "        if valid_ip and valid_port:\n", "            # 保留原始文本中的冒号（通过重新查找原始格式）\n", "            original_format = re.search(re.escape(ip) + r'[:：]' + re.escape(port), text)\n", "            if original_format:\n", "                results.append(original_format.group(0))\n", "    if len(results) == 0:\n", "        # 如果IPv4地址和端口没有匹配出结果，则匹配https://IPv4地址或http://IPv4地址\n", "        pattern = r'(https?://)?(\\d{1,3}\\.\\d{1,3}\\.\\d{1,3}\\.\\d{1,3})'\n", "        matches = re.findall(pattern, text)\n", "        \n", "        for protocol, ip in matches:\n", "            ip_parts = ip.split('.')\n", "            valid_ip = True\n", "            for part in ip_parts:\n", "                if not 0 <= int(part) <= 255:\n", "                    valid_ip = False\n", "                    break\n", "            if valid_ip and protocol:\n", "                if protocol == \"http://\":\n", "                    # http时端口默认为80\n", "                    results.append(f\"{ip}:{80}\")\n", "                if protocol == \"https://\":\n", "                    # http时端口默认为80\n", "                    results.append(f\"{ip}:{443}\")\n", "\n", "    return results"]}, {"cell_type": "code", "execution_count": 73, "id": "60177616", "metadata": {}, "outputs": [], "source": ["def get_reflect_ip(ip):\n", "    if isinstance(ip,float):\n", "        return \"\"\n", "    if \".\" in ip:\n", "        ip_list = ip.split(\".\")\n", "        for i in range(3,5):\n", "            match_ip = \".\".join(ip_list[0:i])\n", "            reflect_ip =  reflect.get(match_ip,\"\")\n", "            if reflect_ip == \"\":\n", "                # logger.warning(\"ip:{} is not in reflect.json\".format(ip))\n", "                continue\n", "            else:\n", "                return reflect_ip+\".\"+ip_list[3] if i == 3 else reflect_ip\n", "        else:\n", "            return \"\"\n", "    else:\n", "        return \"\""]}, {"cell_type": "code", "execution_count": 74, "id": "7321948c", "metadata": {}, "outputs": [], "source": ["def merge_excel_files(folder_path, output_file=None):\n", "    \"\"\"\n", "    合并文件夹下所有Excel文件到一个DataFrame（或输出到文件）\n", "    \n", "    参数:\n", "    folder_path (str): 包含Excel文件的文件夹路径\n", "    output_file (str, optional): 合并结果保存路径（.xlsx或.csv格式），不提供则返回DataFrame\n", "    \n", "    返回:\n", "    DataFrame (如果output_file=None) 或 None (结果直接保存到文件)\n", "    \"\"\"\n", "    # 获取所有Excel文件路径\n", "    excel_files = glob(os.path.join(folder_path, \"*.xlsx\")) + glob(os.path.join(folder_path, \"*.xls\"))\n", "    \n", "    if not excel_files:\n", "        raise FileNotFoundError(f\"在 {folder_path} 中未找到Excel文件\")\n", "    \n", "    # 存储所有DataFrame\n", "    dfs = []\n", "    \n", "    for file in excel_files:\n", "        try:\n", "            # 读取单个Excel文件（包含多个sheet时只读取第一个）\n", "            df = pd.read_excel(file, sheet_name=0)\n", "            dfs.append(df)\n", "            logger.info(f\"已加载: {os.path.basename(file)} ({len(df)}行 × {len(df.columns)}列)\")\n", "        except Exception as e:\n", "            logger.info(f\"跳过 {file} - 读取错误: {str(e)}\")\n", "    \n", "    if not dfs:\n", "        raise ValueError(\"所有文件读取失败，无有效数据\")\n", "    \n", "    # 合并所有DataFrame（自动对齐列名）\n", "    merged_df = pd.concat(dfs, ignore_index=True)\n", "    merged_df[\"私网IP\"] = merged_df[\"IP地址\"].apply(get_reflect_ip)\n", "    logger.info(f\"\\n合并完成: 总行数={len(merged_df)}, 总列数={len(merged_df.columns)}\")\n", "    # 根据参数决定输出方式\n", "    if output_file:\n", "        if output_file.endswith('.csv'):\n", "            merged_df.to_csv(output_file, index=False)\n", "        else:\n", "            merged_df.to_excel(output_file, index=False)\n", "        logger.info(f\"结果已保存至: {output_file}\")\n", "    return merged_df\n"]}, {"cell_type": "code", "execution_count": 75, "id": "2a2ab290", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-06-30 11:54:46,100 [INFO] __main__: 开始解析文件：D:\\原始数据\\0627\\0627\\数据固化\\固化数据.xlsx，sheet名称：Sheet1\n", "2025-06-30 11:54:55,424 [INFO] __main__: 开始解析文件：D:/业务私网匹配表.xlsx，sheet名称：Sheet1\n", "2025-06-30 11:54:55,460 [INFO] __main__: 开始解析文件：D:/原始数据/应用类系统导出(2025-06-26+18_06_18).xlsx，sheet名称：基本信息\n", "e:\\ProgramData\\miniconda3\\envs\\uniarch\\Lib\\site-packages\\openpyxl\\styles\\stylesheet.py:237: UserWarning: Workbook contains no default style, apply openpyxl's default\n", "  warn(\"Workbook contains no default style, apply openpyxl's default\")\n", "2025-06-30 11:54:59,738 [INFO] __main__: 开始解析文件：D:/downloads/edge下载/2025年评估.xlsx，sheet名称：非参评系统下线登记\n"]}], "source": ["yunhong_df = parse_origin_file(yunhong_file,yunhong_sheet_name,[])\n", "\n", "reflect_df = parse_origin_file(reflect_file,reflect_sheet_name,[])\n", "# system_list_df  = parse_origin_file(system_list_file,system_sheet_name,[],skiprows=2)\n", "manage_df = parse_origin_file(manage_system_file,manage_sheet_name,[])\n", "online_system_df = parse_origin_file(online_system_file,online_sheet_name,[],skiprows=1)"]}, {"cell_type": "code", "execution_count": 76, "id": "88d27eca", "metadata": {}, "outputs": [], "source": ["# 构造外网与内网映射关系\n", "reflect = {}\n", "for index,row in reflect_df.iterrows():\n", "    reflect[row['业务IP段']] = row['关联私网IP段']\n", "# reflect"]}, {"cell_type": "code", "execution_count": 77, "id": "02417858", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-06-30 11:54:59,865 [INFO] __main__: 跳过 D:\\原始数据\\0627\\0627\\数据固化\\~$市人力资源社会保障局.xlsx - 读取错误: Excel file format cannot be determined, you must specify an engine manually.\n", "2025-06-30 11:54:59,869 [INFO] __main__: 跳过 D:\\原始数据\\0627\\0627\\数据固化\\~$市体育局.xlsx - 读取错误: Excel file format cannot be determined, you must specify an engine manually.\n", "2025-06-30 11:54:59,871 [INFO] __main__: 跳过 D:\\原始数据\\0627\\0627\\数据固化\\~$市商务局.xlsx - 读取错误: Excel file format cannot be determined, you must specify an engine manually.\n", "2025-06-30 11:54:59,875 [INFO] __main__: 跳过 D:\\原始数据\\0627\\0627\\数据固化\\~$市工业和信息化局.xlsx - 读取错误: Excel file format cannot be determined, you must specify an engine manually.\n", "2025-06-30 11:54:59,878 [INFO] __main__: 跳过 D:\\原始数据\\0627\\0627\\数据固化\\~$市工业和信息化局_20250627.xlsx - 读取错误: Excel file format cannot be determined, you must specify an engine manually.\n", "2025-06-30 11:54:59,881 [INFO] __main__: 跳过 D:\\原始数据\\0627\\0627\\数据固化\\~$市市场监管局.xlsx - 读取错误: Excel file format cannot be determined, you must specify an engine manually.\n", "2025-06-30 11:54:59,884 [INFO] __main__: 跳过 D:\\原始数据\\0627\\0627\\数据固化\\~$市政务服务和数据管理局.xlsx - 读取错误: Excel file format cannot be determined, you must specify an engine manually.\n", "2025-06-30 11:54:59,887 [INFO] __main__: 跳过 D:\\原始数据\\0627\\0627\\数据固化\\~$市文化广电旅游局.xlsx - 读取错误: Excel file format cannot be determined, you must specify an engine manually.\n", "2025-06-30 11:54:59,891 [INFO] __main__: 跳过 D:\\原始数据\\0627\\0627\\数据固化\\~$市林业园林局.xlsx - 读取错误: Excel file format cannot be determined, you must specify an engine manually.\n", "2025-06-30 11:54:59,891 [INFO] __main__: 跳过 D:\\原始数据\\0627\\0627\\数据固化\\~$市统计局.xlsx - 读取错误: Excel file format cannot be determined, you must specify an engine manually.\n", "2025-06-30 11:54:59,895 [INFO] __main__: 跳过 D:\\原始数据\\0627\\0627\\数据固化\\~$市财政局.xlsx - 读取错误: Excel file format cannot be determined, you must specify an engine manually.\n", "2025-06-30 11:55:09,375 [INFO] __main__: 已加载: 固化数据.xlsx (26954行 × 21列)\n", "2025-06-30 11:55:09,714 [INFO] __main__: 已加载: 市交通运输局.xlsx (1618行 × 21列)\n", "2025-06-30 11:55:09,832 [INFO] __main__: 已加载: 市人力资源社会保障局.xlsx (605行 × 21列)\n", "2025-06-30 11:55:09,992 [INFO] __main__: 已加载: 市住房城乡建设局.xlsx (756行 × 20列)\n", "2025-06-30 11:55:10,023 [INFO] __main__: 已加载: 市体育局.xlsx (75行 × 21列)\n", "2025-06-30 11:55:10,035 [INFO] __main__: 已加载: 市信访局.xlsx (22行 × 21列)\n", "2025-06-30 11:55:10,075 [INFO] __main__: 已加载: 市农业农村局.xlsx (90行 × 21列)\n", "2025-06-30 11:55:10,737 [INFO] __main__: 已加载: 市医保局.xlsx (2301行 × 21列)\n", "2025-06-30 11:55:10,923 [INFO] __main__: 已加载: 市卫生健康委.xlsx (670行 × 21列)\n", "2025-06-30 11:55:11,011 [INFO] __main__: 已加载: 市发展改革委.xlsx (250行 × 21列)\n", "2025-06-30 11:55:11,053 [INFO] __main__: 已加载: 市司法局.xlsx (122行 × 21列)\n", "2025-06-30 11:55:11,275 [INFO] __main__: 已加载: 市商务局.xlsx (623行 × 21列)\n", "2025-06-30 11:55:11,304 [INFO] __main__: 已加载: 市国资委.xlsx (67行 × 21列)\n", "2025-06-30 11:55:11,456 [INFO] __main__: 已加载: 市城市管理综合执法局.xlsx (491行 × 21列)\n", "2025-06-30 11:55:11,475 [INFO] __main__: 已加载: 市审计局.xlsx (41行 × 21列)\n", "2025-06-30 11:55:11,509 [INFO] __main__: 已加载: 市工业和信息化局.xlsx (66行 × 21列)\n", "2025-06-30 11:55:11,598 [INFO] __main__: 已加载: 市市场监管局.xlsx (377行 × 21列)\n", "2025-06-30 11:55:11,675 [INFO] __main__: 已加载: 市应急管理局.xlsx (311行 × 21列)\n", "2025-06-30 11:55:11,985 [INFO] __main__: 已加载: 市政务服务和数据管理局.xlsx (1763行 × 21列)\n", "2025-06-30 11:55:12,090 [INFO] __main__: 已加载: 市教育局.xlsx (250行 × 21列)\n", "2025-06-30 11:55:12,174 [INFO] __main__: 已加载: 市文化广电旅游局.xlsx (358行 × 21列)\n", "2025-06-30 11:55:12,353 [INFO] __main__: 已加载: 市林业园林局.xlsx (309行 × 21列)\n", "2025-06-30 11:55:12,428 [INFO] __main__: 已加载: 市民政局.xlsx (233行 × 21列)\n", "2025-06-30 11:55:12,444 [INFO] __main__: 已加载: 市民族宗教局.xlsx (21行 × 21列)\n", "2025-06-30 11:55:12,487 [INFO] __main__: 已加载: 市水务局.xlsx (115行 × 21列)\n", "2025-06-30 11:55:12,523 [INFO] __main__: 已加载: 市港务局.xlsx (88行 × 21列)\n", "2025-06-30 11:55:12,623 [INFO] __main__: 已加载: 市生态环境局.xlsx (341行 × 21列)\n", "2025-06-30 11:55:12,650 [INFO] __main__: 已加载: 市科技局.xlsx (52行 × 21列)\n", "2025-06-30 11:55:12,670 [INFO] __main__: 已加载: 市统计局.xlsx (66行 × 20列)\n", "2025-06-30 11:55:12,876 [INFO] __main__: 已加载: 市规划和自然资源局.xlsx (1231行 × 21列)\n", "2025-06-30 11:55:12,901 [INFO] __main__: 已加载: 市财政局.xlsx (54行 × 21列)\n", "2025-06-30 11:55:12,942 [INFO] __main__: 已加载: 市退役军人事务局.xlsx (111行 × 21列)\n", "2025-06-30 11:55:13,011 [INFO] __main__: \n", "合并完成: 总行数=40431, 总列数=21\n", "2025-06-30 11:55:26,938 [INFO] __main__: 结果已保存至: D:\\原始数据\\0627\\0627\\数据固化\\固化数据.xlsx\n"]}], "source": ["confirm_df = merge_excel_files(confirmed_data,rf\"{confirmed_data}\\固化数据.xlsx\")"]}, {"cell_type": "code", "execution_count": 78, "id": "adf1dac3", "metadata": {}, "outputs": [], "source": ["# 参评系统名称列表\n", "# system_list = system_list_df[\"新信息系统名称\"].values.tolist()\n", "\n", "online_system_df = online_system_df[pd.isna(online_system_df[\"备注\"])]\n", "online_system_list = online_system_df[\"信息系统名称\"].values.tolist()\n", "# system_list = [system for  system in system_list if system  in online_system_list]"]}, {"cell_type": "code", "execution_count": 79, "id": "b0ad1b76", "metadata": {}, "outputs": [{"data": {"text/plain": ["'参评系统566个'"]}, "execution_count": 79, "metadata": {}, "output_type": "execute_result"}], "source": ["f\"参评系统{len(online_system_list)}个\""]}, {"cell_type": "code", "execution_count": 80, "id": "849a6ab8", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_23704\\1628520220.py:23: SettingWithCopyWarning: \n", "A value is trying to be set on a copy of a slice from a DataFrame.\n", "Try using .loc[row_indexer,col_indexer] = value instead\n", "\n", "See the caveats in the documentation: https://pandas.pydata.org/pandas-docs/stable/user_guide/indexing.html#returning-a-view-versus-a-copy\n", "  system_data_df.主机用途 = \"Web服务器\"\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_23704\\1628520220.py:24: SettingWithCopyWarning: \n", "A value is trying to be set on a copy of a slice from a DataFrame.\n", "Try using .loc[row_indexer,col_indexer] = value instead\n", "\n", "See the caveats in the documentation: https://pandas.pydata.org/pandas-docs/stable/user_guide/indexing.html#returning-a-view-versus-a-copy\n", "  system_data_df.IP地址 = ip\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_23704\\1628520220.py:25: SettingWithCopyWarning: \n", "A value is trying to be set on a copy of a slice from a DataFrame.\n", "Try using .loc[row_indexer,col_indexer] = value instead\n", "\n", "See the caveats in the documentation: https://pandas.pydata.org/pandas-docs/stable/user_guide/indexing.html#returning-a-view-versus-a-copy\n", "  system_data_df.端口 = port\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_23704\\1628520220.py:26: SettingWithCopyWarning: \n", "A value is trying to be set on a copy of a slice from a DataFrame.\n", "Try using .loc[row_indexer,col_indexer] = value instead\n", "\n", "See the caveats in the documentation: https://pandas.pydata.org/pandas-docs/stable/user_guide/indexing.html#returning-a-view-versus-a-copy\n", "  system_data_df.端口用途 = \"项目管理系统匹配结果\"\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_23704\\1628520220.py:34: SettingWithCopyWarning: \n", "A value is trying to be set on a copy of a slice from a DataFrame.\n", "Try using .loc[row_indexer,col_indexer] = value instead\n", "\n", "See the caveats in the documentation: https://pandas.pydata.org/pandas-docs/stable/user_guide/indexing.html#returning-a-view-versus-a-copy\n", "  system_data_df.主机用途 = \"Web服务器\"\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_23704\\1628520220.py:35: SettingWithCopyWarning: \n", "A value is trying to be set on a copy of a slice from a DataFrame.\n", "Try using .loc[row_indexer,col_indexer] = value instead\n", "\n", "See the caveats in the documentation: https://pandas.pydata.org/pandas-docs/stable/user_guide/indexing.html#returning-a-view-versus-a-copy\n", "  system_data_df.IP地址 = ip\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_23704\\1628520220.py:36: SettingWithCopyWarning: \n", "A value is trying to be set on a copy of a slice from a DataFrame.\n", "Try using .loc[row_indexer,col_indexer] = value instead\n", "\n", "See the caveats in the documentation: https://pandas.pydata.org/pandas-docs/stable/user_guide/indexing.html#returning-a-view-versus-a-copy\n", "  system_data_df.端口 = port\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_23704\\1628520220.py:37: SettingWithCopyWarning: \n", "A value is trying to be set on a copy of a slice from a DataFrame.\n", "Try using .loc[row_indexer,col_indexer] = value instead\n", "\n", "See the caveats in the documentation: https://pandas.pydata.org/pandas-docs/stable/user_guide/indexing.html#returning-a-view-versus-a-copy\n", "  system_data_df.端口用途 = \"项目管理系统匹配结果\"\n"]}], "source": ["# 将项目管理系统的匹配结果合并进云宏表格\n", "# 从云宏表格筛出没有WEB服务器的系统\n", "web_system_list = yunhong_df[(yunhong_df[\"主机用途\"] == \"Web服务器\") & (~yunhong_df[\"端口\"].isna()) & (yunhong_df[\"系统名称\"].isin(online_system_list))]\n", "# confirm_data_list = confirm_df.values.tolist()\n", "# confirm_ip_list = confirm_df[\"IP地址\"].unique()\n", "yunhong_data_list = yunhong_df.values.tolist()\n", "web_system = list(set(list(web_system_list[\"系统名称\"].values.tolist())))\n", "no_web_system = list(set(list(yunhong_df[~yunhong_df[\"系统名称\"].isin(web_system)][\"系统名称\"].values.tolist())))\n", "# 从项目管理系统匹配出没有WEB服务器的系统的ip端口\n", "xiangmu_no_web_system_df = manage_df[manage_df[\"信息系统名称\"].isin(no_web_system)]\n", "for index, row in xiangmu_no_web_system_df.iterrows():\n", "    # if row.信息系统名称 not in online_system_list:\n", "    #     continue\n", "    # 从【系统访问入口】字段中提取IP地址和端口\n", "    entrance = str(row.系统访问入口)\n", "    extract_res = extract_ip_ports(entrance)\n", "    if len(extract_res) == 0:\n", "        pass\n", "    if len(extract_res) == 1:\n", "        ip_port = extract_res[0].replace(\"：\",\":\").split(\":\")\n", "        ip, port = ip_port\n", "        system_data_df = yunhong_df[yunhong_df[\"系统名称\"] == row.信息系统名称]\n", "        system_data_df.主机用途 = \"Web服务器\"\n", "        system_data_df.IP地址 = ip\n", "        system_data_df.端口 = port\n", "        system_data_df.端口用途 = \"项目管理系统匹配结果\"\n", "        system_data = system_data_df.values.tolist()\n", "        yunhong_data_list.append(system_data[0])\n", "    elif len(extract_res) > 1:\n", "        for res in extract_res:\n", "            ip_port = res.replace(\"：\",\":\").split(\":\")\n", "            ip, port = ip_port\n", "            system_data_df = yunhong_df[yunhong_df[\"系统名称\"] == row.信息系统名称]\n", "            system_data_df.主机用途 = \"Web服务器\"\n", "            system_data_df.IP地址 = ip\n", "            system_data_df.端口 = port\n", "            system_data_df.端口用途 = \"项目管理系统匹配结果\"\n", "            system_data = system_data_df.values.tolist()\n", "            yunhong_data_list.append(system_data[0])"]}, {"cell_type": "code", "execution_count": 81, "id": "ffdde2bb", "metadata": {}, "outputs": [], "source": ["# 处理固化数据表\n", "for index,row in yunhong_df.iterrows():\n", "    if pd.isna(row.端口):\n", "        continue\n", "    # 处理用\"、\"分割的端口\n", "    if not isinstance(row.端口,int) and not isinstance(row.端口,float) and \"、\" in row.端口:\n", "        port_list = row.端口.split(\"、\")\n", "        for port in port_list:\n", "            row['端口'] = port\n", "            yunhong_data_list.append(row.values.tolist())\n"]}, {"cell_type": "code", "execution_count": 82, "id": "37a09a63", "metadata": {}, "outputs": [], "source": ["# 将带有,的ip拆分多行\n", "for index, row in yunhong_df.iterrows():\n", "    if pd.isna(row.IP地址):\n", "        continue\n", "    if \",\" in row.IP地址:\n", "        ip_list = row.IP地址.split(\",\")\n", "        for ip in ip_list:\n", "            copy_row = row.copy()\n", "            copy_row[\"IP地址\"] = ip\n", "            yunhong_data_list.append(copy_row.values.tolist())"]}, {"cell_type": "code", "execution_count": 83, "id": "e607ec4b", "metadata": {}, "outputs": [], "source": ["yunhong_data_df = pd.DataFrame(columns=yunhong_df.columns,data=yunhong_data_list)"]}, {"cell_type": "code", "execution_count": 84, "id": "9786b587", "metadata": {}, "outputs": [], "source": ["yunhong_data_df[\"私网IP\"] = yunhong_data_df[\"IP地址\"].apply(get_reflect_ip)"]}, {"cell_type": "code", "execution_count": 85, "id": "78dfc784", "metadata": {}, "outputs": [], "source": ["yunhong_data_df.to_excel(\"D:/内网匹配结果202506301120.xlsx\",index=False)"]}], "metadata": {"kernelspec": {"display_name": "uniarch", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 5}