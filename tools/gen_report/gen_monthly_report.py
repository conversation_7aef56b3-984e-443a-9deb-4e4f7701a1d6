import json
import os
import zipfile
import sys
import time
import argparse
import logging
import requests
from io import StringIO
import pandas as pd
from collections import defaultdict
from datetime import datetime

# 给定开始时间及结束时间，按时间步长生成时间范围内按步长生成的时间段
def gen_ts_range(start_ts, end_ts, step=None):
    current = start_ts
    while current < end_ts:
        if step is None:
            step = end_ts - start_ts
        end_time = current + step
        # next_time = min(s_ts-1, end_ts)
        yield current, end_time-1
        current = end_time

def setup_logging(log_file=None, debug=False):
    """
    设置日志器\n
    :param log_file: 日志文件路径，如果为None则不写入文件\n
    :param debug: 是否开启调试模式，调试模式下日志级别为DEBUG，否则为INFO\n
    :return: None
    """
    handlers = []
    console_handler = logging.StreamHandler(sys.stderr)
    handlers.append(console_handler)

    if log_file:
        file_handler = logging.FileHandler(log_file)
        handlers.append(file_handler)

    log_format = "%(asctime)s [%(levelname)s] %(name)s: %(message)s" if debug else "%(levelname)s: %(message)s"
    
    logging.basicConfig(
        level=logging.DEBUG if debug else logging.INFO,
        format=log_format,
        handlers=handlers,
        #encoding='utf-8'
    )

def get_host_num_dict(csv_file_path):
    host_num_dict = {}
    for i in range(1, 10):
        try:
            host_num_dict = {}
            if csv_file_path:
                df = pd.read_csv(csv_file_path)
            else:
                url = f'{os.getenv("UNISERVER_WEB_PROTOCOL","http")}://{os.getenv("UNISERVER_ADDR","")}:\
                {os.getenv("UNISERVER_WEB_PORT",9000)}/up-install/zhuji.csv'
                response = requests.get(url,verify=False)
                if response.status_code != 200:
                    raise Exception(f"请求失败，状态码:{response.status_code}")
            for index,row in df.iterrows():
                unit = row.iloc[2].replace('@','')
                system = row.iloc[1]
                if unit in host_num_dict:
                    if system in host_num_dict[unit]:
                        host_num_dict[unit][system] += 1
                    else:
                        host_num_dict[unit][system] = 1
                else:
                    host_num_dict[unit] = {system: 1}
            break
        except Exception as e:
            logger.error(f"get zhuji.csv Error: {e}")
            continue
    return host_num_dict

def get_host_network_dict(csv_file_path):
    host_network_dict = {}
    for i in range(1, 10):
        try:
            host_network_dict = {}
            if csv_file_path:
                df = pd.read_csv(csv_file_path)
            else:
                url = f'{os.getenv("UNISERVER_WEB_PROTOCOL","http")}://{os.getenv("UNISERVER_ADDR","")}:\
                {os.getenv("UNISERVER_WEB_PORT",9000)}/up-install/zhuji.csv'
                response = requests.get(url,verify=False)
                if response.status_code != 200:
                    raise Exception(f"请求失败，状态码:{response.status_code}")
            for index,row in df.iterrows():
                unit = row.iloc[2]
                system = row.iloc[1]
                if pd.isna(row.iloc[3]):
                    network = '政务外网'
                else:
                    network = row.iloc[3].replace('@','')
                if unit in host_network_dict:
                    host_network_dict[unit][system] = network
                else:
                    host_network_dict[unit] = {system: network}
            break
        except Exception as e:
            logger.error(f"get zhuji.csv Error: {e}",exc_info=True)
            continue
    return host_network_dict

class DateFormateChecker(argparse.Action):
    def __call__(self, parser, namespace, values, option_string=None):
        try:
            time.strptime(values, "%Y%m%d-%Y%m%d")
            setattr(namespace, self.dest, values)
        except ValueError:
            raise parser.error("传入日期不合符YYYYMMDD格式.")

def parse_args():

    parser = argparse.ArgumentParser()
    # 配置入参关键字
    parser.add_argument(

        "-d",

        "--date",

        help = "抓取生成报表的日期(格式:20250529)",

        dest = "date",

        type = str,

        # action = DateFormateChecker,

        required = True

    )

    parser.add_argument(

        "-g",

        "--generate_path",

        help = "生成结果保存路径(默认当前目录)",

        dest = "generate_path",

        type = str,

        default = os.getcwd()

    )
    
    parser.add_argument(

        "-cf",

        "--config_file",

        help="指定配置文件(默认为【settings.yaml】)",

        dest="config_file",

        type=str,

        default='settings.yaml'

    )
    args = parser.parse_args()

    return args, parser

def process_json_files(ratio,csv_file_path,json_files,host_cpu_avg_threashold,output_system):
    """
    处理JSON文件并生成按小时统计的报告数据
    :param json_files: JSON文件路径列表
    :return: 包含主机报告和系统报告的元组 (host_report_df, system_report_df)
    """
    # 初始化主机数据结构 - 按月分组
    system_monthly_data = defaultdict(lambda: defaultdict(lambda: {
        'host': '',
        'UNIT_NAME': '',
        'SYSTEM_CODE': '',
        'SYSTEM_VISIT_TOTAL': 0,
        'SYSTEM_VISIT_TOTAL_BYTES': 0.0,
        "DATABASE_PACKAGES_TOTAL": 0.0,
        "DATABASE_BYTES_TOTAL": 0.0,
        'DATABASE_VISIT_TOTAL': 0,
        'DATABASE_VISIT_TOTAL_BYTES': 0.0,
        'CPU_ACTIVE_MINUTES': 0,
        "HOST_NUM":0,
    }))

    for file_path in json_files:
        with open(file_path, 'r', encoding='utf-8') as f:
            try:
                data = json.load(f)
                
                # 获取当前文件的时间范围
                sample_entry = list(next(iter(data.values())).values())[0] if data else None
                if sample_entry:
                    start_time = datetime.strptime(sample_entry['ARCH_START'], '%Y-%m-%d %H:%M:%S')
                    end_time = datetime.strptime(sample_entry['ARCH_END'], '%Y-%m-%d %H:%M:%S')
                    month_key = end_time.strftime('%Y-%m')
                else:
                    continue  # 跳过空文件
                    
                # 处理系统访问数据
                system_metrics = data.get("系统访问总量、系统数据流量", {})
                for entry in system_metrics.values():
                    system_name = system_dict.get(entry['SYSTEM_CODE'].replace("SJK_",""),"")
                    dept_name = entry['UNIT_NAME']
                    system_key = f"{dept_name}|{system_name}"
                    system_record = system_monthly_data[month_key][system_key]
                    system_record['DATE'] =  month_key
                    system_record['UNIT_NAME'] = dept_name
                    system_record['SYSTEM_CODE'] = entry['SYSTEM_CODE'].replace("SJK_","")
                    system_record['SYSTEM_VISIT_TOTAL'] += entry.get('SYSTEM_VISIT_TOTAL', 0)
                    system_record['SYSTEM_VISIT_TOTAL_BYTES'] += entry.get('SYSTEM_VISIT_TOTAL_BYTES', 0.0)
                    system_record['SYSTEM_NAME'] = system_name
                    system_record['HOST_NUM'] = entry.get('HOST_NUM', 0)
                
                # 处理数据库访问数据
                db_metrics = data.get("数据库访问量、数据库访问流量", {})
                for entry in db_metrics.values():
                    system_name = system_dict.get(entry['SYSTEM_CODE'].replace("SJK_",""),"")
                    dept_name = entry['UNIT_NAME']
                    system_key = f"{dept_name}|{system_name}"
                    system_record = system_monthly_data[month_key][system_key]
                    system_record['DATE'] =  month_key
                    system_record['UNIT_NAME'] = dept_name
                    system_record['SYSTEM_CODE'] = entry['SYSTEM_CODE'].replace("SJK_","")
                    system_record['DATABASE_PACKAGES_TOTAL'] += entry.get('DATABASE_PACKAGES_TOTAL', 0)
                    system_record['DATABASE_BYTES_TOTAL'] += entry.get('DATABASE_BYTES_TOTAL', 0.0)
                    system_record['DATABASE_VISIT_TOTAL'] += entry.get('DATABASE_VISIT_TOTAL', 0)
                    system_record['DATABASE_VISIT_TOTAL_BYTES'] += entry.get('DATABASE_VISIT_TOTAL_BYTES', 0.0)
                    system_record['SYSTEM_NAME'] = system_name
                    system_record['HOST_NUM'] = entry.get('HOST_NUM', 0)
                
                # 处理数据库TCP数据
                db_metrics = data.get("数据库TCP", {})
                for entry in db_metrics.values():
                    # host = entry['host']
                    system_name = system_dict.get(entry['SYSTEM_CODE'].replace("SJK_",""),"")
                    dept_name = entry['UNIT_NAME']

                    # 更新系统数据（按天）
                    system_key = f"{dept_name}|{system_name}"
                    system_record = system_monthly_data[month_key][system_key]
                    system_record['DATE'] =  month_key
                    system_record['UNIT_NAME'] = dept_name
                    system_record['SYSTEM_CODE'] = entry['SYSTEM_CODE'].replace("SJK_","")
                    system_record['DATABASE_PACKAGES_TOTAL'] += entry.get('DATABASE_PACKAGES_TOTAL', 0)
                    system_record['DATABASE_BYTES_TOTAL'] += entry.get('DATABASE_BYTES_TOTAL', 0.0)
                    system_record['DATABASE_VISIT_TOTAL'] += entry.get('DATABASE_VISIT_TOTAL', 0)
                    system_record['DATABASE_VISIT_TOTAL_BYTES'] += entry.get('DATABASE_VISIT_TOTAL_BYTES', 0.0)
                    system_record['SYSTEM_NAME'] = system_name
                    system_record['HOST_NUM'] = entry.get('HOST_NUM', 0)

                # 处理CPU数据并计算活跃时长
                cpu_metrics = data.get("CPU平均值", {})
                for entry in cpu_metrics.values():
                    # host = entry['host']
                    system_name = system_dict.get(entry['SYSTEM_CODE'].replace("SJK_",""),"")
                    dept_name = entry['UNIT_NAME']
                    
                    # 计算时间窗口长度（分钟）
                    start_time = datetime.strptime(entry['ARCH_START'], '%Y-%m-%d %H:%M:%S')
                    end_time = datetime.strptime(entry['ARCH_END'], '%Y-%m-%d %H:%M:%S')
                    time_window = (end_time - start_time).total_seconds() / 60

                    HOST_CPU_AVG = entry.get('HOST_CPU_AVG', 0)
                    if HOST_CPU_AVG == 'Infinity':
                        HOST_CPU_AVG = 0

                    # 更新系统数据
                    system_key = f"{dept_name}|{system_name}"
                    system_record = system_monthly_data[month_key][system_key]
                    system_record['DATE'] =  month_key
                    system_record['UNIT_NAME'] = dept_name
                    system_record['SYSTEM_CODE'] = entry['SYSTEM_CODE'].replace("SJK_","")
                    system_record['SYSTEM_NAME'] = system_name
                    system_record['HOST_NUM'] = entry.get('HOST_NUM', 0)

                    #  CPU平均使用率计算活跃度阈值判断
                    if HOST_CPU_AVG > host_cpu_avg_threashold:
                        system_record['CPU_ACTIVE_MINUTES'] += time_window
            except Exception as e:
                logger.error(f"Error processing file {file_path}: {e}",exc_info=True)
    
    # 转换为DataFrame - 系统报告
    system_records = []

    for month_key, systems in system_monthly_data.items():
        for system_key, data in systems.items():
            if output_system and  data['SYSTEM_CODE'] not in output_system:
                continue
            unit,system_name = system_key.split("|")
            # 数据库访问量
            DATABASE_VISIT_TOTAL = data['DATABASE_VISIT_TOTAL']
            # 数据库访问流量
            DATABASE_VISIT_TOTAL_BYTES = data['DATABASE_VISIT_TOTAL_BYTES']
            # 如果数据库加密，取4层流量
            if DATABASE_VISIT_TOTAL == 0 and DATABASE_VISIT_TOTAL_BYTES == 0:
                DATABASE_VISIT_TOTAL = data['DATABASE_PACKAGES_TOTAL']
                DATABASE_VISIT_TOTAL_BYTES = data['DATABASE_BYTES_TOTAL']
            record = {
                '日期(年月)': month_key,
                '一级单位': data['UNIT_NAME'],
                '系统编号': data['SYSTEM_CODE'],
                '系统名称': data['SYSTEM_NAME'],
                '主机数量(探针安装数量)':data['HOST_NUM'],
                '系统访问总量': data['SYSTEM_VISIT_TOTAL']*ratio,
                '系统数据流量(MB)': round(data['SYSTEM_VISIT_TOTAL_BYTES']*ratio/1024/1024,2),
                '数据库访问量': DATABASE_VISIT_TOTAL*ratio,
                '数据库访问流量(MB)': round(DATABASE_VISIT_TOTAL_BYTES*ratio/1024/1024,2),
                'CPU活跃时长(分钟)': round(data['CPU_ACTIVE_MINUTES'],2) if data['HOST_NUM'] > 0 else 0
            }
            system_records.append(record)

    system_df = pd.DataFrame(system_records)
    
    system_df = system_df.sort_values('日期(年月)', ignore_index=True)
    
    return system_df

def create_folder(folder_path):
    """
    创建文件夹，如果文件夹已存在则不做任何操作\n
    :param folder_path: 文件夹路径\n
    """
    try:
        os.makedirs(folder_path, exist_ok=True)
        logger.info(f"文件夹 {folder_path} 创建成功或已存在。")
    except Exception as e:
        logger.error(f"创建文件夹时发生错误:{str(e)}", exc_info=True)
        raise Exception(f"创建文件夹 {folder_path} 时发生错误:{str(e)}")

class ScoreCalculator:
    """
    系统效能评估计算类
    """

    def __init__(self,system_data:pd.array,system_network_status):
        self.system_data = system_data
        self.system_network_status = system_network_status

    def calculate_score(self):
        svc = round(self.system_visit_score(self.system_network_status,self.system_data.系统访问总量),2)
        svbc = round(self.system_visit_bytes_score(self.system_network_status,self.system_data['系统数据流量(MB)']),2)
        dbv = round(self.database_visit_score(self.system_network_status,self.system_data.数据库访问量),2)
        dbvb = round(self.database_visit_bytes_score(self.system_network_status,self.system_data["数据库访问流量(MB)"]),2)
        cpu = round(self.cpu_active_score(self.system_network_status,self.system_data["CPU活跃时长(分钟)"]),2)
        result = {
                "日期(年月)":self.system_data["日期(年月)"],
                "单位名称":self.system_data["一级单位"],
                "系统名称":self.system_data["系统名称"],
                "系统编号":self.system_data["系统编号"],
                "IP数量":self.system_data["主机数量(探针安装数量)"],
                "系统效能赋分":svc+svbc+dbv+dbvb+cpu,
                "系统访问量赋分":svc,
                "系统访问流量赋分":svbc,
                "数据库访问量赋分":dbv,
                "数据库访问流量赋分":dbvb,
                "虚拟 CPU(VCPU)活跃累计时长赋分":cpu,
                }
        return result

    def system_visit_score(self,network_status,system_visit_total):
        """
        # 系统访问量赋分
        指标说明:以政务信息系统每月系统总访问量为评估指标\n
        权重:30\n
        | 赋分方法互联网侧系统          | 赋分方法政务网侧系统         |
        | ------------------- | ------------------ |
        | 0 分:x<1 万次          | 0 分:x<1,000 次      |
        | 5 分:1 万次≤x<10 万次    | 5 分:1,000 次≤x<1 万次 |
        | 10 分:10 万次≤x<50 万次  | 10 分:1 万次≤x<5 万次   |
        | 20 分:50 万次≤x<200 万次 | 20 分:5 万次≤x<20 万次  |
        | 30 分:x≥200 万次       | 30 分:x≥20 万次       |
        """
        if network_status == "互联网":
            if system_visit_total < 10000:
                return 0
            elif system_visit_total < 100000:
                return system_visit_total*5/10000
            elif system_visit_total < 500000:
                return system_visit_total*10/500000
            elif system_visit_total < 2000000:
                return system_visit_total*20/2000000
            else:
                return 30
        elif network_status == "政务外网":
            if system_visit_total < 1000:
                return 0
            elif system_visit_total < 10000:
                return system_visit_total*5/1000
            elif system_visit_total < 50000:
                return system_visit_total*10/50000
            elif system_visit_total < 200000:
                return system_visit_total*20/200000
            else:
                return 30
        else:
            return 0

    def system_visit_bytes_score(self,network_status,system_visit_total_bytes):
        """
        # 系统访问流量赋分
        指标说明:以政务信息系统每月系统总数据流量为评估指标\n
        权重:15\n
        | 赋分方法互联网侧系统 | 赋分方法政务网侧系统 |
        |----------------------|----------------------|
        | 0 分:x < 500 MB     | 0 分:x < 100 MB     |
        | 3 分:500 MB ≤x<5 GB | 3 分:100 MB ≤x<1000 MB |
        | 6 分:5 GB ≤x<25 GB  | 6 分:1000 MB ≤x<5 GB |
        | 10 分:25 GB ≤x<100 GB | 10 分:5 GB ≤x<20 GB |
        | 15 分:x≥100 G       | 15 分:x≥20 GB       |
        """
        if network_status == "互联网": 
            if system_visit_total_bytes < 500:
                return 0
            elif system_visit_total_bytes < 5120:
                return system_visit_total_bytes*3/5120
            elif system_visit_total_bytes < 25600:
                return system_visit_total_bytes*6/25600
            elif system_visit_total_bytes < 102400:
                return system_visit_total_bytes*10/102400
            else:
                return 15
        elif network_status == "政务外网":
            if system_visit_total_bytes < 100:
                return 0
            elif system_visit_total_bytes < 1000:
                return system_visit_total_bytes*3/1000
            elif system_visit_total_bytes < 5120:
                return system_visit_total_bytes*6/5120
            elif system_visit_total_bytes < 20480:
                return system_visit_total_bytes*10/20480
            else:
                return 15
        else:
            return 0

    def database_visit_score(self,network_status,database_visit_total):
        """
        # 数据库访问量赋分
        指标说明:以政务信息系统每月数据库活跃情况为评估指标\n
        权重:20\n
        | 赋分方法互联网侧系统 | 赋分方法政务网侧系统 |
        |----------------------|----------------------|
        | 0 分:x < 5,000 条   | 0 分:x < 5,000 条   |
        | 5 分:5,000 条≤x<2 万条 | 5 分:5,000 条≤x<2 万条 |
        | 10 分:2 万条≤x<50 万条 | 10 分:2 万条≤x<50 万条 |
        | 15 分:50 万条≤x<100 万条 | 15 分:50 万条≤x<100 万条 |
        | 20 分:x≥100 万条    | 20 分:x≥100 万条    |
        """
        if network_status == "互联网": 
            if database_visit_total < 5000:
                return 0
            elif database_visit_total < 20000:
                return database_visit_total*5/20000
            elif database_visit_total < 50000:
                return database_visit_total*10/50000
            elif database_visit_total < 100000:
                return database_visit_total*15/100000
            else:
                return 20
        elif network_status == "政务外网":
            if database_visit_total < 5000:
                return 0
            elif database_visit_total < 20000:
                return database_visit_total*5/20000
            elif database_visit_total < 50000:
                return database_visit_total*10/50000
            elif database_visit_total < 100000:
                return database_visit_total*15/100000
            else:
                return 20
        else:
            return 0

    def database_visit_bytes_score(self,network_status,database_visit_total_bytes):
        """
        # 数据库访问流量赋分
        指标说明:以政务信息系统每月数据库的总访问流量为评估指标\n
        权重:15\n
        | 赋分方法互联网侧系统 | 赋分方法政务网侧系统 |
        |----------------------|----------------------|
        | 0 分:x < 10 MB      | 0 分:x < 10 MB      |
        | 3 分:10 MB ≤x<100 MB | 3 分:10 MB ≤x<100 MB |
        | 6 分:100 MB ≤x<500 MB | 6 分:100 MB ≤x<500 MB |
        | 10 分:500 MB ≤x<2000 MB | 10 分:500 MB ≤x<2000 MB |
        | 15 分:x≥2000 MB     | 15 分:x≥2000 MB     |
        """
        if network_status == "互联网": 
            if database_visit_total_bytes < 10:
                return 0
            elif database_visit_total_bytes < 100:
                return database_visit_total_bytes*3/100
            elif database_visit_total_bytes < 500:
                return database_visit_total_bytes*6/500
            elif database_visit_total_bytes < 2000:
                return database_visit_total_bytes*10/2000
            else:
                return 15
        elif network_status == "政务外网":
            if database_visit_total_bytes < 10:
                return 0
            elif database_visit_total_bytes < 100:
                return database_visit_total_bytes*3/100
            elif database_visit_total_bytes < 500:
                return database_visit_total_bytes*6/500
            elif database_visit_total_bytes < 2000:
                return database_visit_total_bytes*10/2000
            else:
                return 15
        else:
            return 0

    def cpu_active_score(self,network_status,cpu_active_minutes):
        """
        # CPU活跃时长
        指标说明:以政务信息系统每月vCPU平均使用率超过某一数值的时长为评估依据\n
        权重:15\n
        | 赋分方法互联网侧系统 | 赋分方法政务网侧系统 |
        |----------------------|----------------------|
        | 0 分:x < 1.5 小时   | 0 分:x < 1.5 小时   |
        | 3 分:1.5 小时≤x<3 小时 | 3 分:1.5 小时≤x<3 小时 |
        | 6 分:3 小时≤x<6 小时   | 6 分:3 小时≤x<6 小时   |
        | 10 分:6 小时≤x<9 小时  | 10 分:6 小时≤x<9 小时 |
        | 15 分:x≥9 小时       | 15 分:x≥9 小时       |
        """
        if network_status == "互联网": 
            if cpu_active_minutes < 90:
                return 0
            elif cpu_active_minutes < 180:
                return cpu_active_minutes*3/180
            elif cpu_active_minutes < 360:
                return cpu_active_minutes*6/360
            elif cpu_active_minutes < 540:
                return cpu_active_minutes*10/540
            else:
                return 15
        elif network_status == "政务外网":
            if cpu_active_minutes < 90:
                return 0
            elif cpu_active_minutes < 180:
                return cpu_active_minutes*3/180
            elif cpu_active_minutes < 360:
                return cpu_active_minutes*6/360
            elif cpu_active_minutes < 540:
                return cpu_active_minutes*10/540
            else:
                return 15
        else:
            return 0

def gen_evaluate_report(system_month_report_df):
    """
    赋分报告生成
    """
    # 从zhuji.csv获取系统的接入网络类型
    # 根据接入网络类型及指标值进行赋分
    system_evaluate_report = []
    host_network_dict = get_host_network_dict(rf"D:\202506251920_check_out\zhuji.csv")
    for index,system_row in system_month_report_df.iterrows():
        result_score = ScoreCalculator(system_row,
                                    host_network_dict.get(system_row.一级单位,{}).get(system_row.系统编号,"")).calculate_score()
        system_evaluate_report.append(result_score)
    evaluate_report_df = pd.DataFrame(system_evaluate_report)
    return evaluate_report_df

def generate_excel_report(ratio,csv_file_path,json_dir, output_path,date,host_cpu_avg_threashold,output_system=None):
    """
    生成Excel报告（按小时统计）
    :param json_dir: 包含JSON文件的目录路径
    :param output_file: 输出Excel文件路径
    """
    data_range = date.split("-")
    start_time = int(datetime.strptime(data_range[0], '%Y%m%d').timestamp())
    end_time = int(datetime.strptime(data_range[1], '%Y%m%d').timestamp()) + 86400
    date_list = list(datetime.fromtimestamp(st).strftime("%Y%m%d") for st,et in gen_ts_range(start_ts=start_time,end_ts=end_time,step=86400))
    # 获取所有JSON文件
    json_files = [os.path.join(json_dir, f) 
            for f in os.listdir(json_dir) \
            for date in date_list\
            if f.startswith(date) and f.endswith('.json')]
    
    if not json_files:
        logging.warning(f"在目录 {json_dir} 中没有找到JSON文件")
        return
    logger.info(f"正在处理 {len(json_files)}个文件")
    # 处理文件并生成报告
    system_report = process_json_files(ratio,csv_file_path,json_files,host_cpu_avg_threashold,output_system)

    department_list = system_report['一级单位'].unique()
    file_list = []
    for department in department_list:
        # department_host_report = host_report[host_report['一级单位'] == department].reset_index(drop=True)
        department_system_report = system_report[system_report['一级单位'] == department].reset_index(drop=True)
        # TODO: 生成评价报告
        evaluate_report_df = gen_evaluate_report(department_system_report)
        # 保存到Excel（两个sheet）
        file_path = os.path.join(output_path,f"{department}_{date}.xlsx")
        with pd.ExcelWriter(file_path) as writer:
            # department_host_report.to_excel(writer, sheet_name='主机报告(小时)', index=True,index_label='序号')
            department_system_report.to_excel(writer, sheet_name='系统评估报告', index=True,index_label='序号')
            evaluate_report_df.to_excel(writer, sheet_name='系统赋分报告', index=True,index_label='序号')
        file_list.append(file_path)
    zip_excels(file_list,f"{output_path}.zip")

def zip_excels(file_list, output_zip):
    try:
        with zipfile.ZipFile(output_zip, 'w', zipfile.ZIP_DEFLATED) as zipf:
            for file in file_list:
                if not os.path.isfile(file):
                    raise FileNotFoundError(f"文件不存在: {file}")
                zipf.write(file)
        return True
    except Exception as e:
        logger.error(f"错误: {str(e)}")
        return False

# 使用示例
if __name__ == "__main__":
    try:
        import yaml
        # 命令行参数
        args, parser = parse_args()
        gen_time = time.strftime("%Y%m%d%H%M%S", time.localtime())
        date = args.date
        generate_path = args.generate_path
        # 创建运行审计目录
        audit_path = os.path.join(generate_path, f"audit/{gen_time}")
        # 如果路径不存在则创建
        if not os.path.exists(audit_path):
            os.makedirs(audit_path)
        # 设置日志记录
        setup_logging(log_file=rf"{audit_path}/{gen_time}_result.log", debug=True)
        logger =  logging.getLogger(__name__)
        logger.info(f"开始生成指定{date}效能报告")
        # 读取配置文件
        config_file = args.config_file
        if config_file !="settings.yaml":
            if not os.path.isabs(config_file):
                config_file = os.path.join(os.getcwd(), config_file)
            if not os.path.exists(config_file):
                logger.error(f"指定的配置文件 {config_file} 不存在，请检查路径和文件名是否正确。", exc_info=True)
                exit(1)
            # 如果用户指定了配置文件，则使用用户指定的配置文件
            logger.info(f"使用指定配置文件:{config_file}")
            with open(config_file, 'r', encoding='utf-8') as file:
                config = yaml.safe_load(file)
        else:
            logger.info(f"使用默认配置文件:{config_file}")
            with open(f'{os.path.dirname(__file__)}/{config_file}', 'r', encoding='utf-8') as file:
                config = yaml.safe_load(file)
        # 读取系统编码映射文件
        system_dict = json.loads(open(config["system_code_file"], 'r', encoding='utf-8').read())
        # 生成保存路径
        file_path = os.path.join(generate_path, f"{config['file_name']}/{gen_time}")
        create_folder(file_path)
        generate_excel_report(config["ratio"],config["csv_file_path"],config["arch_data_path"],file_path,date,config["host_cpu_avg_threashold"])
    except Exception as e:
        logger.error(f"程序运行过程中发生错误:{str(e)}\n请检查输入参数和配置文件", exc_info=True)
        exit(1)