import os
import sys
import time
import shutil
import logging
import argparse
import requests
import json
import pandas as pd
from util import load_env
from uniserver import UniserverApiManager
from data_handler import get_uniprobe_node_name_list
import datetime


def setup_logging(log_file=None, debug=False):
    """
    设置日志器\n
    :param log_file: 日志文件路径，如果为None则不写入文件\n
    :param debug: 是否开启调试模式，调试模式下日志级别为DEBUG，否则为INFO\n
    :return: None
    """
    handlers = []
    console_handler = logging.StreamHandler(sys.stderr)
    handlers.append(console_handler)

    if log_file:
        file_handler = logging.FileHandler(log_file)
        handlers.append(file_handler)

    log_format = (
        "%(asctime)s [%(levelname)s] %(name)s: %(message)s"
        if debug
        else "%(levelname)s: %(message)s"
    )

    logging.basicConfig(
        level=logging.DEBUG if debug else logging.INFO,
        format=log_format,
        handlers=handlers,
        # encoding='utf-8'
    )


def parse_args():

    parser = argparse.ArgumentParser()
    # 配置入参关键字
    parser.add_argument(
        "-f",
        "--file_path",
        help="待转换文档路径",
        dest="file_path",
        type=str,
        # required = True
    )

    parser.add_argument(
        "-s",
        "--sheet_name",
        help="待转换文档sheet名称(默认为【所需服务器字段】)",
        dest="sheet_name",
        type=str,
        default="所需服务器字段",
    )

    parser.add_argument(
        "-yf",
        "--yunhong_file_path",
        help="云宏原始数据文档路径",
        dest="yunhong_file_path",
        type=str,
        # required = True
    )

    parser.add_argument(
        "-ys",
        "--yunhong_file_sheet_name",
        help="云宏原始数据文档sheet名称(默认为【关键端口列表】)",
        dest="yunhong_file_sheet_name",
        type=str,
        default="关键端口列表",
    )

    parser.add_argument(
        "-rf",
        "--regular_file_path",
        help="固化数据文档路径",
        dest="regular_file_path",
        type=str,
        # required = True
    )

    parser.add_argument(
        "-rs",
        "--regular_file_sheet_name",
        help="固定数据sheet",
        dest="regular_file_sheet_name",
        type=str,
        default="关键端口列表",
    )

    parser.add_argument(
        "-sf",
        "--system_file_path",
        help="系统名称对应编码文档路径",
        dest="system_file_path",
        type=str,
        # required = True
    )

    parser.add_argument(
        "-ss",
        "--system_file_sheet_name",
        help="系统名称对应编码文档sheet名称",
        dest="system_file_sheet_name",
        type=str,
    )

    parser.add_argument(
        "-mf",
        "--manage_file_path",
        help="项目管理系统文档路径",
        dest="manage_file_path",
        type=str,
        # required = True
    )

    parser.add_argument(
        "-ms",
        "--manage_file_sheet_name",
        help="项目管理系统文档sheet名称",
        dest="manage_file_sheet_name",
        type=str,
    )

    parser.add_argument(
        "-g",
        "--generate_path",
        help="生成结果保存路径(默认当前目录)",
        dest="generate_path",
        type=str,
        default=os.getcwd(),
    )

    parser.add_argument(
        "-cf",
        "--config_file",
        help="指定配置文件(默认为【settings.yaml】)",
        dest="config_file",
        type=str,
        default="settings.yaml",
    )
    parser.add_argument(
        "-bf",
        "--bianmu_file_path",
        help="编目系统文档路径",
        dest="bianmu_file_path",
        type=str,
    )
    parser.add_argument(
        "-bs",
        "--bianmu_file_sheet_name",
        help="编目系统文档sheet名称",
        dest="bianmu_file_sheet_name",
        type=str,
    )

    args = parser.parse_args()

    return args, parser


def get_uniprobe_list():
    url = "http://************:9000/api/sidecars?query=&page=1&per_page=8000&only_active=false&sort=last_seen&order=asc"

    payload = ""
    headers = {
        "Accept": "application/json",
        "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
        "Authorization": "Basic Yzc5NmNjNmMtYzQ5ZC00NTQ2LTliNzctZGZhNGQ3NDA1NTg4OnNlc3Npb24=",
        "Cache-Control": "no-cache",
        "Connection": "keep-alive",
        "Pragma": "no-cache",
        "Referer": "http://************:9000/system/sidecars",
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********",
        "X-Graylog-No-Session-Extension": "true",
        "X-Requested-By": "XMLHttpRequest",
        "X-Requested-With": "XMLHttpRequest",
        "Content-Type": "application/json",
    }

    response = requests.request("GET", url, headers=headers, data=payload)

    if response.status_code != 200:
        raise Exception(f"请求失败，状态码：{response.status_code}")
    else:
        json_data = json.loads(response.text)
        uniprobe_list = [item["node_name"] for item in json_data["sidecars"]]
        return uniprobe_list


def create_folder(folder_path):
    """
    创建文件夹，如果文件夹已存在则不做任何操作\n
    :param folder_path: 文件夹路径\n
    """
    try:
        os.makedirs(folder_path, exist_ok=True)
        logger.info(f"文件夹 {folder_path} 创建成功或已存在。")
    except Exception as e:
        logger.error(f"创建文件夹时发生错误：{str(e)}", exc_info=True)
        raise Exception(f"创建文件夹 {folder_path} 时发生错误：{str(e)}")


def parse_origin_file(origin_file_path, sheet_name, required_columns, skiprows=None):
    """
    通过Pandas解析原始文件内容\n
    :param origin_file_path: 原始Excel文件路径\n
    :param sheet_name: 要解析的sheet名称\n
    :return: DataFrame对象\n
    """
    try:
        logger.info(f"开始解析文件：{origin_file_path}，sheet名称：{sheet_name}")
        # 读取Excel文件，可根据需要调整sheet_name参数
        df = pd.read_excel(
            origin_file_path,
            engine="openpyxl",
            sheet_name=sheet_name,
            skiprows=skiprows,
        )
        df.dropna(how="all", inplace=True)  # 删除全为空值的行
        # 检查是否包含必要的列
        for col in required_columns:
            if col not in df.columns:
                logger.error(
                    f"错误：文档中缺少必要的列：{col}，请检查文档格式。", exc_info=True
                )
                raise ValueError(f"缺少必要的列：{col}")
        return df
    except FileNotFoundError:
        logger.error(
            f"错误：未找到{origin_file_path}文件，请检查路径和文件名是否正确。",
            exc_info=True,
        )
        raise FileNotFoundError(f"未找到文件：{origin_file_path}")
    except Exception as e:
        logger.error(
            f"读取文档{origin_file_path}过程中发生错误：{str(e)}", exc_info=True
        )
        raise Exception(f"读取文档{origin_file_path}时发生错误：{str(e)}")


def gen_checkout_data(df, config):
    """
    生成检查表(根据配置的key_columns生成)\n
    :param df: DataFrame对象\n
    :param config: 配置字典\n
    :return: 处理后的数据对象\n
    """
    try:
        logger.info(f"开始生成{config['filename']}检查表")
        result = {}

        # 从配置获取列名
        key_cols = config["key_columns"]
        value_cols = config["value_columns"]
        separator = config["separator"]

        for index, data in df.iterrows():
            if pd.isna(data.端口) or data.端口 in ["", None]:
                continue
            # 使用配置的分隔符组合key
            key_parts = [
                (
                    str(getattr(data, col, "")).strip()
                    if "端口" not in col
                    else str(int(getattr(data, col, "")))
                )
                for col in key_cols
            ]
            key = separator.join(key_parts)

            # 获取value列的值
            values = [str(getattr(data, col, "")).strip() for col in value_cols]

            if key in result:
                logger.warning(
                    f"{config['filename']}检查表发现重复的键值组合：{key}-{values}，跳过处理"
                )
                continue

            result[key] = values

        return result
    except Exception as e:
        logger.error(f"生成检查表时发生错误：{str(e)}", exc_info=True)
        raise Exception(f"生成检查表时发生错误")


def gen_checkout_ip_port(df):
    """
    生成检查表(主键:ip:端口)核心业务逻辑\n
    :param df: DataFrame对象\n
    :return: 处理后的数据对象\n
    """
    try:
        logger.info("开始生成IP-端口检查表")
        ip_port_result = {}
        for index, data in df.iterrows():
            key = (
                str(getattr(data, "IP地址", "")).strip()
                + "-"
                + str(getattr(data, "端口", "")).strip()
            )
            system_name = str(getattr(data, "系统名称", "")).strip()
            first_level_unit = str(getattr(data, "所属一级单位", "")).strip()
            if key in ip_port_result:
                logger.warning(
                    f"发现重复的IP地址-端口组合：{key}-系统名称:{system_name},所属一级单位:{first_level_unit}，跳过处理"
                )
                continue
            ip_port_result[key] = [system_name, first_level_unit]
        return ip_port_result
    except Exception as e:
        logger.error(f"生成检查表时发生错误：{str(e)}", exc_info=True)
        raise Exception(f"生成ip-端口检查表时发生错误")


def save_to_csv(data, output_file_path, config):
    """
    将数据保存为CSV文件\n
    :param data: 数据对象\n
    :param output_file_path: 输出CSV文件路径\n
    :param config: 输出配置\n
    """
    try:
        with open(output_file_path, "w", encoding=config["encoding"]) as f:
            for key, values in data.items():
                values_str = config["field_separator"].join(f'"{v}"' for v in values)
                f.write(
                    f'"{key}"{config["field_separator"]}{values_str}{config["record_separator"]}\n'
                )
        logger.info(f"数据已成功保存到 {output_file_path}")
    except Exception as e:
        logger.error(f"保存CSV文件时发生错误：{str(e)}", exc_info=True)
        raise


if __name__ == "__main__":
    try:
        # 配置文件读取
        load_env(f"{os.path.dirname(__file__)}/.env")
        import yaml

        # 命令行参数
        args, parser = parse_args()
        gen_time = time.strftime("%Y%m%d%H%M%S", time.localtime())

        # 指定要转换的Excel文件路径
        file_path = args.file_path
        sheet_name = args.sheet_name
        system_file_path = args.system_file_path
        system_file_sheet_name = args.system_file_sheet_name
        yunhong_file_path = args.yunhong_file_path
        yunhong_file_sheet_name = args.yunhong_file_sheet_name
        manage_file_path = args.manage_file_path
        manage_file_sheet_name = args.manage_file_sheet_name
        generate_path = args.generate_path
        today = datetime.datetime.now().strftime("%m%d")
        generate_path = os.path.join(generate_path, f"zhuji_check_out_{today}")
        regular_file_path = args.regular_file_path
        regular_file_sheet_name = args.regular_file_sheet_name
        # 数据编目文件读取
        bianmu_file_path = args.bianmu_file_path
        bianmu_file_sheet_name = args.bianmu_file_sheet_name

        # 创建运行审计目录
        audit_path = os.path.join(generate_path, f"audit/{gen_time}")

        # 如果路径不存在则创建
        if not os.path.exists(audit_path):
            os.makedirs(audit_path)
        # 设置日志记录
        setup_logging(log_file=rf"{audit_path}/{gen_time}_result.log", debug=True)
        logger = logging.getLogger(__name__)
        # 读取配置文件
        config_file = args.config_file
        if config_file != "settings.yaml":
            if not os.path.isabs(config_file):
                config_file = os.path.join(os.getcwd(), config_file)
            if not os.path.exists(config_file):
                logger.error(
                    f"指定的配置文件 {config_file} 不存在，请检查路径和文件名是否正确。",
                    exc_info=True,
                )
                exit(1)
            # 如果用户指定了配置文件，则使用用户指定的配置文件
            logger.info(f"使用指定配置文件：{config_file}")
            with open(config_file, "r", encoding="utf-8") as file:
                config = yaml.safe_load(file)
        else:
            logger.info(f"使用默认配置文件：{config_file}")
            with open(
                f"{os.path.dirname(__file__)}/{config_file}", "r", encoding="utf-8"
            ) as file:
                config = yaml.safe_load(file)
        # 将运行文件复制入审计目录，以便复现排查问题
        # shutil.copyfile(file_path, f"{audit_path}/{os.path.basename(file_path)}")
        # logger.info(f"开始转换文件：{file_path}，sheet名称：{sheet_name}，生成结果保存路径：{generate_path}，转换时间：{gen_time}")

        # 创建运行结果保存文件夹
        create_folder(generate_path)

        # 解析原始文件
        # 云宏源文件
        # yunhong_df = parse_origin_file(yunhong_file_path, yunhong_file_sheet_name, [])
        # origin_df = parse_origin_file(file_path, sheet_name, config['required_columns'])

        # 系统编码表
        system_df = parse_origin_file(
            system_file_path,
            system_file_sheet_name,
            required_columns=[
                "信息系统名称",
                "一级单位",
                "系统名称简称",
                "系统编号",
            ],  # 新增系统名称简称列检查
            skiprows=1,
        )

        # 项目管理应用类台账表
        manage_df = parse_origin_file(manage_file_path, manage_file_sheet_name, [])

        # 固化数据表
        regular_df = parse_origin_file(regular_file_path, regular_file_sheet_name, [])

        # 数据编目表
        bianmu_df = parse_origin_file(
            bianmu_file_path,
            bianmu_file_sheet_name,
            required_columns=["一级单位", "系统名称", "系统编号", "是否完成编目"],
            skiprows=5,
        )

        # 处理接入网络类型
        network_condition_dict = {}
        for index, row in manage_df.iterrows():
            if pd.isna(row["系统编号"]) or pd.isna(row["接入网络类型"]):
                continue
            if not row["系统编号"].startswith("GZXT"):
                continue
            # system_network_list = row['接入网络类型'].split(",")
            # system_network = system_network_list[0]
            # if system_network not in ["互联网", "政务外网"] and "业务专网" not in system_network:
            #     continue
            # if "业务专网" in system_network:
            #     system_network = "政务外网"
            network_condition_dict[row["系统编号"]] = row["接入网络类型"]
        logger.info(f"接入网络类型处理完成，共处理{len(network_condition_dict)}条数据")


        # 处理数据编目表
        biammu_dict = {}
        for index, row in bianmu_df.iterrows():
            system_code = row["系统编号"]
            bianmu_nums = row.get("是否完成编目", 0)
            if pd.isna(system_code) or pd.isna(bianmu_nums):
                continue
            biammu_dict[system_code] = bianmu_nums
        logger.info(f"数据编目表处理完成，共处理{len(biammu_dict)}条数据")

        # 定义结果集字典
        check_data = {}
        with UniserverApiManager() as uniserver_api:
            ip_list = get_uniprobe_node_name_list(uniserver_api)
        logger.debug(f"对{len(ip_list)}个ip进行匹配")
        special_ip = {
            # 服务端主机
            "************": {
                "system_code": "GZXT-JS-N-999-999999",
                "system_unit": "效能管理平台",
                "system_shortname": "效能管理平台",
            },
            "************": {
                "system_code": "GZXT-JS-N-999-999999",
                "system_unit": "效能管理平台",
                "system_shortname": "效能管理平台",
            },
            "************": {
                "system_code": "GZXT-JS-N-999-999999",
                "system_unit": "效能管理平台",
                "system_shortname": "效能管理平台",
            },
            "************": {
                "system_code": "GZXT-JS-N-999-999999",
                "system_unit": "效能管理平台",
                "system_shortname": "效能管理平台",
            },
            "************": {
                "system_code": "GZXT-JS-N-999-999999",
                "system_unit": "效能管理平台",
                "system_shortname": "效能管理平台",
            },
            "172.28.50.17": {
                "system_code": "GZXT-JS-N-999-999999",
                "system_unit": "效能管理平台",
                "system_shortname": "效能管理平台",
            },
            # 2025.6 新增
            "10.194.110.3": {
                "system_code": "GZXT-JS-N-037-037214",
                "system_unit": "市卫生健康委",
                "system_shortname": "生育服务办事",
            },
            "10.194.101.132": {
                "system_code": "GZXT-JS-N-032-000352",
                "system_unit": "市交通运输局",
                "system_shortname": "道路停车",
            },
            "10.201.50.3": {
                "system_code": "GZXT-JS-N-029-000462",
                "system_unit": "市规划和自然资源局",
                "system_shortname": "内控一体化",
            },
            "10.202.5.36": {
                "system_code": "GZXT-JS-N-029-000912",
                "system_unit": "市规划和自然资源局",
                "system_shortname": "基础设施",
            },
            "10.201.35.10": {
                "system_code": "GZXT-JS-N-031-002208",
                "system_unit": "市住房城乡建设局",
                "system_shortname": "房屋管理",
            },
            # 2025.7.1 新增
            "10.201.35.12": {
                "system_code": "GZXT-JS-N-031-002729",
                "system_unit": "市住房城乡建设局",
                "system_shortname": "玻璃幕墙监测",
            },
            "10.194.69.13": {
                "system_code": "GZXT-JS-N-031-002734",
                "system_unit": "市住房城乡建设局",
                "system_shortname": "房屋应用建设",
            },
            "10.194.69.14": {
                "system_code": "GZXT-JS-N-031-002734",
                "system_unit": "市住房城乡建设局",
                "system_shortname": "房屋应用建设",
            },
            # 2025.7.11 新增
            "10.201.35.150": {
                "system_code": "GZXT-JS-N-031-002208",
                "system_unit": "市住房城乡建设局",
                "system_shortname": "房屋管理",
            },
            "10.201.35.151": {
                "system_code": "GZXT-JS-N-031-002612",
                "system_unit": "市住房城乡建设局",
                "system_shortname": "存量房网上",
            },
            "10.201.35.112": {
                "system_code": "GZXT-JS-N-031-002728",
                "system_unit": "市住房城乡建设局",
                "system_shortname": "房地产管理",
            },
            "10.201.35.113": {
                "system_code": "GZXT-JS-N-031-002612",
                "system_unit": "市住房城乡建设局",
                "system_shortname": "存量房网上",
            },
            "10.201.101.2": {
                "system_code": "GZXT-JS-N-031-031685",
                "system_unit": "市住房城乡建设局",
                "system_shortname": "房屋数据库应用",
            },
            "10.201.30.50": {
                "system_code": "GZXT-JS-N-039-002611",
                "system_unit": "市应急管理局",
                "system_shortname": "工矿商贸",
            },
            "10.201.36.3": {
                "system_code": "GZXT-JS-N-031-000283",
                "system_unit": "市住房城乡建设局",
                "system_shortname": "事务中心综合管理",
            },
            # 2025.7.14 新增
            "10.201.94.4": {
                "system_code": "GZXT-JS-N-030-030709",
                "system_unit": "市生态环境局",
                "system_shortname": "噪声环境自动监测",
            },
            "10.201.96.3": {
                "system_code": "GZXT-JS-W-030-000784",
                "system_unit": "市生态环境局",
                "system_shortname": "固体废物管理",
            },
            # 2025.7.18 新增
            "10.194.69.45": {
                "system_code": "GZXT-JS-N-031-000667",
                "system_unit": "市住房城乡建设局",
                "system_shortname": "施工质量安全",
            },
            "10.201.81.100": {
                "system_code": "GZXT-JS-N-031-031683",
                "system_unit": "市住房城乡建设局",
                "system_shortname": "应急抢险信息管理",
            },
            # 2025.7.28 新增
            "10.201.62.6": {
                "system_code": "GZXT-JS-N-042-000844",
                "system_unit": "市市场监管局",
                "system_shortname": "综合执法",
            },
            "10.201.62.7": {
                "system_code": "GZXT-JS-N-042-000843",
                "system_unit": "市市场监管局",
                "system_shortname": "双随机一公开",
            },
            "10.197.159.18": {
                "system_code": "GZXT-JS-N-039-002527",
                "system_unit": "市应急管理局",
                "system_shortname": "行政执法电子监察",
            },
            # 2025.7.30 新增
            "10.194.253.171": {
                "system_code": "GZXT-JS-W-051-000381",
                "system_unit": "市政务服务和数据管理局",
                "system_shortname": "一窗综合",
            },
            # 2025.8.5 新增
            "10.201.41.20": {
                "system_code": "GZXT-JS-N-047-047282",
                "system_unit": "市城市管理综合执法局",
                "system_shortname": "智慧环卫",
            },
            "10.201.41.18": {
                "system_code": "GZXT-JS-N-047-047278",
                "system_unit": "市城市管理综合执法局",
                "system_shortname": "数据治理与分析",
            },
            # 2025.8.6 新增
            "10.194.92.116": {
                "system_code": "GZXT-JS-N-039-002686",
                "system_unit": "市应急管理局",
                "system_shortname": "数据备份管理",
            },
            # 2025.8.8 新增
            "10.202.6.1": {
                "system_code": "GZXT-JS-N-029-000912",
                "system_unit": "市规划和自然资源局",
                "system_shortname": "基础设施",
            },
            "10.202.6.2": {
                "system_code": "GZXT-JS-N-029-000912",
                "system_unit": "市规划和自然资源局",
                "system_shortname": "基础设施",
            },
            "10.202.6.3": {
                "system_code": "GZXT-JS-N-029-000912",
                "system_unit": "市规划和自然资源局",
                "system_shortname": "基础设施",
            },
            "10.202.6.4": {
                "system_code": "GZXT-JS-N-029-000912",
                "system_unit": "市规划和自然资源局",
                "system_shortname": "基础设施",
            },
            "10.202.6.5": {
                "system_code": "GZXT-JS-N-029-000912",
                "system_unit": "市规划和自然资源局",
                "system_shortname": "基础设施",
            },
            "10.202.6.6": {
                "system_code": "GZXT-JS-N-029-000912",
                "system_unit": "市规划和自然资源局",
                "system_shortname": "基础设施",
            },
            "10.202.6.10": {
                "system_code": "GZXT-JS-N-029-000912",
                "system_unit": "市规划和自然资源局",
                "system_shortname": "基础设施",
            },
            "10.202.6.11": {
                "system_code": "GZXT-JS-N-029-000912",
                "system_unit": "市规划和自然资源局",
                "system_shortname": "基础设施",
            },
            "10.202.6.12": {
                "system_code": "GZXT-JS-N-029-000912",
                "system_unit": "市规划和自然资源局",
                "system_shortname": "基础设施",
            },
            "10.202.6.13": {
                "system_code": "GZXT-JS-N-029-000912",
                "system_unit": "市规划和自然资源局",
                "system_shortname": "基础设施",
            },
            "10.202.6.14": {
                "system_code": "GZXT-JS-N-029-000912",
                "system_unit": "市规划和自然资源局",
                "system_shortname": "基础设施",
            },
            "10.202.6.15": {
                "system_code": "GZXT-JS-N-029-000912",
                "system_unit": "市规划和自然资源局",
                "system_shortname": "基础设施",
            },
            "10.202.6.16": {
                "system_code": "GZXT-JS-N-029-000912",
                "system_unit": "市规划和自然资源局",
                "system_shortname": "基础设施",
            },
            "10.202.6.17": {
                "system_code": "GZXT-JS-N-029-000912",
                "system_unit": "市规划和自然资源局",
                "system_shortname": "基础设施",
            },
            "10.202.6.18": {
                "system_code": "GZXT-JS-N-029-000912",
                "system_unit": "市规划和自然资源局",
                "system_shortname": "基础设施",
            },
            "10.202.6.19": {
                "system_code": "GZXT-JS-N-029-000912",
                "system_unit": "市规划和自然资源局",
                "system_shortname": "基础设施",
            },
            "***********": {
                "system_code": "GZXT-JS-N-029-000912",
                "system_unit": "市规划和自然资源局",
                "system_shortname": "基础设施",
            },
            # 2025.8.12 新增
            "*************": {
                "system_code": "GZXT-JS-N-031-031680",
                "system_unit": "市住房城乡建设局",
                "system_shortname": "建设工程",
            },
        }
        no_system_ip_list = []
        for ip_host in ip_list:
            ip = ip_host.split("-")[0]

            # 先判断是否是特殊ip
            if ip in special_ip:
                check_data[ip_host] = [
                    special_ip[ip]["system_code"],
                    special_ip[ip]["system_unit"],
                    network_condition_dict.get(special_ip[ip]["system_code"], ""),
                    special_ip[ip]["system_shortname"],
                    biammu_dict.get(special_ip[ip]["system_code"], 0),
                ]
                continue

            # 判断数据是否重复
            if ip_host in check_data:
                logger.warning(f"{ip_host}重复，请检查数据，跳过处理")
                continue

            # 先从固化数据匹配
            # ip_data = regular_df[(regular_df["私网IP"] == ip) | (regular_df["IP地址"] == ip)]
            # if not ip_data.empty:
            #     check_data[ip_host] = [ip_data.iloc[0].系统编号,ip_data.iloc[0].所属一级单位,network_condition_dict.get(ip_data.iloc[0].系统编号,"")]
            #     continue
            ip_match = (regular_df["私网IP"] == ip) | (regular_df["IP地址"] == ip)
            if ip_match.any():
                # 从固化数据里获取匹配的 ip 数据
                ip_data = regular_df[ip_match]
                # 获取系统编号
                system_code = ip_data.iloc[0].系统编号
                # 获取系统名称
                system_name = ip_data.iloc[0].系统名称
                # 获取一级单位
                system_unit = ip_data.iloc[0].所属一级单位

                # 从system_df中查找对应的系统名称简称
                short_name_row = system_df[system_df["系统编号"] == system_code]
                system_short_name = (
                    short_name_row.iloc[0]["系统名称简称"]
                    if not short_name_row.empty
                    else ""
                )
                check_data[ip_host] = [
                    system_code,
                    system_unit,
                    network_condition_dict.get(system_code, ""),
                    system_short_name,
                    biammu_dict.get(system_code, 0),
                ]
                continue
            # check_data[ip_host] = [system_code,system_unit,network_condition_dict.get(system_code,"")]

            # check_data[ip_host] = [
            #     system_code,
            #     system_unit,
            #     network_condition_dict.get(system_code, ""),
            #     system_short_name
            # ]

            # 如果固化数据中没有匹配到，则直接标记并跳过
            logger.warning(
                f"{ip_host}没有在固化数据中找到对应系统，请检查数据，跳过处理"
            )
            no_system_ip_list.append(ip)
            continue
        # 保存处理后的数据
        output_path = os.path.join(generate_path, config["output_config"]["filename"])

        save_to_csv(check_data, output_path, config["output_config"])
        logger.info(
            f"共有{len(no_system_ip_list)}个ip找不到业务系统{no_system_ip_list}"
        )
        # no_system_list = []
        # for ip in no_system_ip_list:
        #     # 定位到ip的行
        #     ip_data = yunhong_df[yunhong_df["IP地址"] == ip]
        #     if ip_data.empty:
        #         logger.warning(f"源数据表没有与{ip}匹配的数据")
        #         no_system_list.append(["","",ip,"","无匹配项"])
        #         continue
        #     unit = ip_data.所属一级单位.values.tolist()[0]
        #     system_name = ip_data.系统名称.values.tolist()[0]
        #     ip_addr = ip_data.IP地址.values.tolist()[0]
        #     no_system_list.append([unit,system_name,ip,ip_addr,"非参评系统"])
        # no_system_df = pd.DataFrame(columns=["一级单位","系统名称","上线IP","填写主机IP","类型"],data=no_system_list)
        # output_path = os.path.join(generate_path, "没有系统的ip.xlsx")
        # no_system_df.to_excel(output_path,index=False)
        logger.info(f"转换完成，运行日志存放于{audit_path}")
    except Exception as e:
        logger.error(
            f"程序运行过程中发生错误：{str(e)}\n请到{audit_path}审查日志", exc_info=True
        )
        exit(1)
