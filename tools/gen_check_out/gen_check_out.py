import os
import sys
import time
import shutil
import logging
import argparse
import pandas as pd

def setup_logging(log_file=None, debug=False):
    """
    设置日志器\n
    :param log_file: 日志文件路径，如果为None则不写入文件\n
    :param debug: 是否开启调试模式，调试模式下日志级别为DEBUG，否则为INFO\n
    :return: None
    """
    handlers = []
    console_handler = logging.StreamHandler(sys.stderr)
    handlers.append(console_handler)

    if log_file:
        file_handler = logging.FileHandler(log_file)
        handlers.append(file_handler)

    log_format = "%(asctime)s [%(levelname)s] %(name)s: %(message)s" if debug else "%(levelname)s: %(message)s"
    
    logging.basicConfig(
        level=logging.DEBUG if debug else logging.INFO,
        format=log_format,
        handlers=handlers,
        #encoding='utf-8'
    )

def parse_args():

    parser = argparse.ArgumentParser()
    # 配置入参关键字
    parser.add_argument(

        "-f",

        "--file_path",

        help = "待转换文档路径",

        dest = "file_path",

        type = str,

        required = True

    )

    parser.add_argument(

        "-s",

        "--sheet_name",

        help = "待转换文档sheet名称(默认为【所需服务器字段】)",

        dest = "sheet_name",

        type = str,

        default = "所需服务器字段"

    )

    parser.add_argument(

        "-g",

        "--generate_path",

        help = "生成结果保存路径(默认当前目录)",

        dest = "generate_path",

        type = str,

        default = os.getcwd()

    )
    
    parser.add_argument(

        "-cf",

        "--config_file",

        help="指定配置文件(默认为【settings.yaml】)",

        dest="config_file",

        type=str,

        default='settings.yaml'

    )
    args = parser.parse_args()

    return args, parser


def create_folder(folder_path):
    """
    创建文件夹，如果文件夹已存在则不做任何操作\n
    :param folder_path: 文件夹路径\n
    """
    try:
        os.makedirs(folder_path, exist_ok=True)
        logger.info(f"文件夹 {folder_path} 创建成功或已存在。")
    except Exception as e:
        logger.error(f"创建文件夹时发生错误：{str(e)}", exc_info=True)
        raise Exception(f"创建文件夹 {folder_path} 时发生错误：{str(e)}")


def parse_origin_file(origin_file_path,sheet_name,required_columns):
    """
    通过Pandas解析原始文件内容\n
    :param origin_file_path: 原始Excel文件路径\n
    :param sheet_name: 要解析的sheet名称\n
    :return: DataFrame对象\n
    """
    try:
        logger.info(f"开始解析文件：{origin_file_path}，sheet名称：{sheet_name}")
        # 读取Excel文件，可根据需要调整sheet_name参数
        df = pd.read_excel(origin_file_path, engine='openpyxl', sheet_name=sheet_name)
        df.dropna(how='all', inplace=True)  # 删除全为空值的行
        # 检查是否包含必要的列
        for col in required_columns:
            if col not in df.columns:
                logger.error(f"错误：文档中缺少必要的列：{col}，请检查文档格式。", exc_info=True)
                raise ValueError(f"缺少必要的列：{col}")
        return df
    except FileNotFoundError:
        logger.error(f"错误：未找到{origin_file_path}文件，请检查路径和文件名是否正确。",exc_info=True)
        raise FileNotFoundError(f"未找到文件：{origin_file_path}")
    except Exception as e:
        logger.error(f"读取文档{origin_file_path}过程中发生错误：{str(e)}",exc_info=True)
        raise Exception(f"读取文档{origin_file_path}时发生错误：{str(e)}")

def gen_checkout_data(df, config):
    """
    生成检查表(根据配置的key_columns生成)\n
    :param df: DataFrame对象\n
    :param config: 配置字典\n
    :return: 处理后的数据对象\n
    """
    try:
        logger.info(f"开始生成{config['filename']}检查表")
        result = {}
        
        # 从配置获取列名
        key_cols = config['key_columns']
        value_cols = config['value_columns']
        separator = config['separator']
        
        for index, data in df.iterrows():
            # 使用配置的分隔符组合key
            key_parts = [str(getattr(data, col, "")).strip() for col in key_cols]
            key = separator.join(key_parts)
            
            # 获取value列的值
            values = [str(getattr(data, col, "")).strip() for col in value_cols]
            
            if key in result:
                logger.warning(f"{config['filename']}检查表发现重复的键值组合：{key}-{values}，跳过处理")
                continue
                
            result[key] = values
            
        return result
    except Exception as e:
        logger.error(f"生成检查表时发生错误：{str(e)}", exc_info=True)
        raise Exception(f"生成检查表时发生错误")

def gen_checkout_ip_port(df):
    """
    生成检查表(主键:ip:端口)核心业务逻辑\n
    :param df: DataFrame对象\n
    :return: 处理后的数据对象\n
    """
    try:
        logger.info("开始生成IP-端口检查表")
        ip_port_result = {}
        for index,data in df.iterrows():
            key = str(getattr(data,"IP地址","")).strip() + '-' + str(getattr(data,"端口","")).strip()
            system_name = str(getattr(data,"系统名称","")).strip()
            first_level_unit = str(getattr(data,"所属一级单位","")).strip()
            if key in ip_port_result:
                logger.warning(f"发现重复的IP地址-端口组合：{key}-系统名称:{system_name},所属一级单位:{first_level_unit}，跳过处理")
                continue
            ip_port_result[key] = [system_name,first_level_unit]
        return ip_port_result
    except Exception as e:
        logger.error(f"生成检查表时发生错误：{str(e)}", exc_info=True)
        raise Exception(f"生成ip-端口检查表时发生错误")

def save_to_csv(data, output_file_path, config):
    """
    将数据保存为CSV文件\n
    :param data: 数据对象\n
    :param output_file_path: 输出CSV文件路径\n
    :param config: 输出配置\n
    """
    try:
        with open(output_file_path, 'w', encoding=config['encoding']) as f:
            for key, values in data.items():
                values_str = config['field_separator'].join(f'"{v}"' for v in values)
                f.write(f'"{key}"{config["field_separator"]}{values_str}{config["record_separator"]}\n')
        logger.info(f"数据已成功保存到 {output_file_path}")
    except Exception as e:
        logger.error(f"保存CSV文件时发生错误：{str(e)}", exc_info=True)
        raise


if __name__ == "__main__":
    try:
        import yaml
        # 命令行参数
        args, parser = parse_args()
        gen_time = time.strftime("%Y%m%d%H%M%S", time.localtime())
        # 指定要转换的Excel文件路径
        file_path = args.file_path
        sheet_name = args.sheet_name
        generate_path = args.generate_path
        # 创建运行审计目录
        audit_path = os.path.join(generate_path, f"audit/{gen_time}")
        # 如果路径不存在则创建
        if not os.path.exists(audit_path):
            os.makedirs(audit_path)
        # 设置日志记录
        setup_logging(log_file=rf"{audit_path}/{gen_time}_result.log", debug=True)
        logger =  logging.getLogger(__name__)
        # 读取配置文件
        config_file = args.config_file
        if config_file !="settings.yaml":
            if not os.path.isabs(config_file):
                config_file = os.path.join(os.getcwd(), config_file)
            if not os.path.exists(config_file):
                logger.error(f"指定的配置文件 {config_file} 不存在，请检查路径和文件名是否正确。", exc_info=True)
                exit(1)
            # 如果用户指定了配置文件，则使用用户指定的配置文件
            logger.info(f"使用指定配置文件：{config_file}")
            with open(config_file, 'r', encoding='utf-8') as file:
                config = yaml.safe_load(file)
        else:
            logger.info(f"使用默认配置文件：{config_file}")
            with open(f'{os.path.dirname(__file__)}/{config_file}', 'r', encoding='utf-8') as file:
                config = yaml.safe_load(file)
        # 将运行文件复制入审计目录，以便复现排查问题
        shutil.copyfile(file_path, f"{audit_path}/{os.path.basename(file_path)}")
        logger.info(f"开始转换文件：{file_path}，sheet名称：{sheet_name}，生成结果保存路径：{generate_path}，转换时间：{gen_time}")
        # 创建运行结果保存文件夹
        create_folder(generate_path)
        # 解析原始文件
        df = parse_origin_file(file_path, sheet_name, config['required_columns'])
        # 遍历配置生成数据及csv
        for config_key,output_config in config['output_config'].items():
            # 生成检查表数据
            check_data = gen_checkout_data(df, output_config)

            # 生成保存路径
            file_path = os.path.join(generate_path, output_config['filename'])
            
            # 保存处理后的数据
            save_to_csv(check_data, file_path, output_config)

        logger.info(f"转换完成，运行日志存放于{audit_path}")
    except Exception as e:
        logger.error(f"程序运行过程中发生错误：{str(e)}\n请到{audit_path}审查日志",exc_info=True)
        exit(1)
