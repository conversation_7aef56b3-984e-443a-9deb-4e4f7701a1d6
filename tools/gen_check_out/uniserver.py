import time
import json
import base64
from util import general_requests
from log import logger
import pandas as pd
import os
from io import StringIO

class UniserverApi:
    """
    uniserver仪表盘请求类，集成仪表盘web的多个接口请求\n
    # login
    """
    def __init__(self, **kwargs):
        self.LOGSTASH_PORT = int(os.getenv("LOGSTASH_PORT",8881))
        self.LOGSTASH_ADDR = os.getenv("LOGSTASH_ADDR","")
        self.UNISERVER_WEB_PORT = int(os.getenv("UNISERVER_WEB_PORT",9000))
        self.UNISERVER_DATA_PORT = int(os.getenv("UNISERVER_DATA_PORT",12201))
        self.UNISERVER_ADDR = os.getenv("UNISERVER_ADDR","")
        self.UNISERVER_ACC = os.getenv("UNISERVER_ACC","")
        self.UNISERVER_SECRET = os.getenv("UNISERVER_SECRET","")
        self.UNISERVER_WEB_PROTOCOL = os.getenv("UNISERVER_WEB_PROTOCOL","http")
        self.FILE_ADDR = os.getenv("FILE_ADDR","")
        for k,v in kwargs.items():
            setattr(self, k, v)
        self.headers = {
        "Accept": "application/json",
        "Accept-Language": "zh-CN,zh;q=0.9",
        "Content-Type": "application/json",
        "X-Requested-By": "XMLHttpRequest",
        "X-Requested-With": "XMLHttpRequest"
        }

    def login(self):
        """
        - 使用实例属性账号密码调用登录接口，获取session_id
        - 将session_id更新到实例属性的请求头中
        """
        try:
            # 模拟登录获取session
            token_url = f"{self.UNISERVER_WEB_PROTOCOL}://{self.UNISERVER_ADDR}:{self.UNISERVER_WEB_PORT}/api/system/sessions"
            logger.info(f'get token from {token_url} ...')
            token_data = {
                "username": self.UNISERVER_ACC,
                "password": self.UNISERVER_SECRET,
                "host": f"{self.UNISERVER_ADDR}:{self.UNISERVER_WEB_PORT}"
            }
            response_content = general_requests(token_url, 'post', self.headers, json=token_data)
            logger.info(f'token response {response_content} ...')
            session_id = json.loads(response_content).get('session_id', None)
            self.SESSION_ID = session_id
            # 根据session_id生成token令牌
            uniserver_token = base64.b64encode(f'{session_id}:session'.encode('utf-8')).decode('utf-8')
            self.headers["Authorization"] = f"Basic {uniserver_token}"
            logger.info(f'token is: {uniserver_token} ...')
        except Exception as e:
            logger.error(f"login error {str(e)}",exc_info=True)

    def logout(self):
        """
        - 调用登出接口，注销token
        """
        try:
            token_url = f"{self.UNISERVER_WEB_PROTOCOL}://{self.UNISERVER_ADDR}:{self.UNISERVER_WEB_PORT}/api/system/sessions/{self.SESSION_ID}"
            logger.info(f'logout token from {token_url} ...')
            response_content = general_requests(token_url, 'delete', self.headers)
            logger.info(f'logout response {response_content} ...')
        except Exception as e:
            logger.error(f"logout error {str(e)}", exc_info=True)

    def get_dashboard_groups(self) -> list[dict[str]]:
        """
        获取仪表盘分组\n
        e.g\n
        [{\n
        "groupName":"",#仪表盘分组名称\n
        "_id":"",#仪表盘分组id\n
        "dashboardsId":[]#仪表盘分组中的仪表板id\n
        },{...}...]

        :return: 仪表盘分组列表

        """
        try:
            # 查询仪表盘页面
            dashboard_group_list_url = f"{self.UNISERVER_WEB_PROTOCOL}://{self.UNISERVER_ADDR}:{self.UNISERVER_WEB_PORT}/api/views/DashboardsGroup/list"
            response_content = general_requests(dashboard_group_list_url, 'get', self.headers)
            # 只有一个分组时返回404，同时分组详情不需要入参
            dashboard_group_list = json.loads(response_content)
            if len(dashboard_group_list['data']) == 0:
                raise Exception('没有查询到仪表板分组')
            return  dashboard_group_list['data']
        except Exception as e:
            logger.error(f"get dashboard group error {str(e)}",exc_info=True)
            return []

    def get_dashboards(self,group_id) -> list[dict[str]]:
        """
        获取仪表盘分组详情\n
        e.g\n
        [{\n
        "id":"",#仪表盘地址id用/dashboards/id跳转访问\n
        "search_id":"", #用于请求仪表盘详情的id\n
        "title":"",#仪表盘名称\n
        "type":"",#仪表盘类型？\n
        },{...},...]
        :param group_id: 仪表盘分组id
        :return: 仪表盘分组详情
        """
        try:
            dashboard_group_url = f"{self.UNISERVER_WEB_PROTOCOL}://{self.UNISERVER_ADDR}:{self.UNISERVER_WEB_PORT}/api/dashboards?query=&page=1&per_page=100&sort=title&order=asc"
            if group_id:
                dashboard_group_url += f"&dashboardGroupId={group_id}"
            response_content = general_requests(dashboard_group_url, 'get', self.headers)
            dashboard_group = json.loads(response_content)
            if dashboard_group['total'] == 0:
                raise Exception(f'仪表盘分组id{group_id}没有仪表板分组')
            if len(dashboard_group['views']) == 0:
                raise Exception(f'仪表盘分组id{group_id}没有查询到仪表板分组')
            return dashboard_group['views']
        except Exception as e:
            logger.error(f"get dashboard group error {str(e)}",exc_info=True)
            return []

    def get_tab_widgets(self,dashboard_id) -> dict:
        """
        获取仪表盘模块及小组件内容\n
        {
        tab模块的id:{
        "titles":tab模块名称,
        "widget":{小组件id:小组件名称}
        }
        }
        :param dashboard_id: get_dashboards返回的仪表盘请求id
        :return:
        """
        try:
            field_url = f"{self.UNISERVER_WEB_PROTOCOL}://{self.UNISERVER_ADDR}:{self.UNISERVER_WEB_PORT}/api/views/{dashboard_id}"
            response_content = general_requests(field_url,'get', headers=self.headers)
            tab_widgets = json.loads(response_content)
            if 'state' not in tab_widgets:
                raise Exception(f'{dashboard_id}没有查询到仪表盘模块及小组件内容')
            return tab_widgets
        except Exception as e:
            logger.error(f"get field config error {str(e)}",exc_info=True)
            return {}

    def execute_search(self,execute_id,param=None):
        """
        执行小部件查询\n

        覆盖时间请求参数\n
        param = {"global_override": {"timerange": {
                "type": "absolute",
                "from": datetime.fromtimestamp(start_ts).isoformat() + "Z",
                "to": datetime.fromtimestamp(end_ts).isoformat() + "Z"
            }}}
        :param execute_id: 仪表盘id或小组件id
        :param param: 额外请求参数
        :return:
        """
        try:
            execute_url = f"{self.UNISERVER_WEB_PROTOCOL}://{self.UNISERVER_ADDR}:{self.UNISERVER_WEB_PORT}/api/views/search/{execute_id}/execute"
            execute_data = {
                "parameter_bindings": {}
            }
            if param:
                execute_data.update(param)
            logger.info(execute_data)

            # 查询仪表板
            response_content = general_requests(execute_url,'post', headers=self.headers, json=execute_data)
            # 解析查询结果
            query_dashboard_result = json.loads(response_content)
            if "results" not in query_dashboard_result or len(query_dashboard_result['results']) == 0:
                raise Exception(f'no query results in {response_content}')
            return query_dashboard_result['results']
        except Exception as e:
            logger.warning(f'get dashboard error {str(e)}',exc_info=True)
            return []

    def get_zhuji_csv(self,csv_file_path=None):
        """
        执行下载zhuji.csv,返回dataframe\n
        :return: dataframe
        """
        for i in range(1, 10):
            try:
                if csv_file_path:
                    df = pd.read_csv(csv_file_path)
                else:
                    url = f'{self.UNISERVER_WEB_PROTOCOL}://{self.FILE_ADDR}/zhuji.csv'
                    response = general_requests(url,'get',headers={})
                    # 直接读取 CSV 数据到 DataFrame
                    df = pd.read_csv(StringIO(response), encoding='utf-8')
                return df
            except Exception as e:
                logger.error(f"get zhuji.csv Error: {e}",exc_info=True)
                continue
        return None
    
    def get_uniprobe_list(self,page=1,page_size=1000,only_active=False):
        """
        获取uniprobe列表\n
        :param page: 页码
        :param page_size: 每页数量
        :param only_active: 只获取活跃的探针
        :return: uniprobe列表
        """
        try:
            if only_active:
                filter_active = "true"
            else:
                filter_active = "false"
            execute_url = f"{self.UNISERVER_WEB_PROTOCOL}://{self.UNISERVER_ADDR}:{self.UNISERVER_WEB_PORT}/api/sidecars?query=&page={page}&per_page={page_size}&only_active={filter_active}&sort=last_seen&order=asc"
            # 探针列表
            response_content = general_requests(execute_url,'get', headers=self.headers, data="")
            return json.loads(response_content)
        except Exception as e:
            logger.error(f"get uniprobe list error {str(e)}",exc_info=True)
            return []


    def udp_send_arch_merge_result_to_logstash(self,upd_connection,arch_merge_result:str):
        try:
            if not self.LOGSTASH_ADDR or not self.LOGSTASH_PORT:
                return
            upd_connection.sendto(arch_merge_result.encode('utf-8'), (self.LOGSTASH_ADDR, self.LOGSTASH_PORT))
            time.sleep(0.05)
        except Exception as e:
            logger.error(f'udp send arch merge result to logstash error {str(e)}',exc_info=True)

    def udp_send_arch_merge_result(self,upd_connection,arch_merge_result:str):
        try:
            upd_connection.sendto(arch_merge_result.encode('utf-8'), (self.UNISERVER_ADDR, self.UNISERVER_DATA_PORT))
            time.sleep(0.05)
        except Exception as e:
            logger.error(f'udp send arch merge result error {str(e)}',exc_info=True)

    def http_send_arch_merge_result(self,arch_merge_result:str):
        try:
            gelf_url = f'{self.UNISERVER_WEB_PROTOCOL}://{self.UNISERVER_ADDR}:{self.UNISERVER_DATA_PORT}/gelf'
            general_requests(gelf_url,'post', data=arch_merge_result, headers={'Content-Type': 'application/json'})
        except Exception as e:
            logger.error(f'http send arch merge result error {str(e)}',exc_info=True)

class UniserverApiManager(UniserverApi):

    def __init__(self,**kwargs):
        super().__init__(**kwargs)

    def __enter__(self):
        self.login()
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        self.logout()


if __name__ == '__main__':
    from util import load_env
    # 配置文件读取
    load_env(f'{os.path.dirname(__file__)}/.env')
    with UniserverApiManager() as UAM:
        uniprobe_list = UAM.get_uniprobe_list()
        uniprobe_list = [item['node_name'].split("-")[0] for item in uniprobe_list['sidecars']]
        print(uniprobe_list)