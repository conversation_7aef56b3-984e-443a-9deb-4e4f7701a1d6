from uniserver import UniserverApiManager
from log import logger

def get_uniprobe_node_name_list(uniserver_obj:UniserverApiManager):
    uniprobe_ip_list = []
    for i in range(1, 10):
        try:
            uniprobe_data = uniserver_obj.get_uniprobe_list(page_size=10000)
            return [item['node_name'] for item in uniprobe_data['sidecars']]
        except Exception as e:
            logger.error(f"get uniprobe_ip_lis Error: {e}",exc_info=True)
            continue
    return uniprobe_ip_list