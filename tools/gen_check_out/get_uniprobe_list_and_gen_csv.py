import requests
import base64
import pandas as pd
import argparse
import os
import datetime


"""
获取token
@param base_url: 基础URL
@type base_url: str
@param username: 用户名
@type username: str
@param password: 密码
@type password: str
@param headers: 请求头
@type headers: dict
@return: token
@rtype: str

"""


def get_token(base_url, username, password, headers):
    """获取访问token"""
    # 这里需要根据实际情况实现token获取逻辑
    # 假设通过某个登录接口获取token
    # 以下是示例代码，需要根据实际API进行修改
    login_url = f"{base_url}/api/system/sessions"
    credentials = {
        "username": f"{username}",  # 替换为实际用户名
        "password": f"{password}",  # 替换为实际密码
        "host": f"{base_url}",
    }

    try:
        response = requests.post(login_url, headers=headers, json=credentials)
        response.raise_for_status()
        # 打印完整响应，检查返回结构
        print("Login response:", response.text)
        session_id = response.json().get("session_id")
        if not session_id:
            print("未在响应中找到session_id")
            return None
        token = base64.b64encode(f"{session_id}:session".encode("utf-8")).decode(
            "utf-8"
        )
        # print("session_id is :", session_id)
        print("token is :", token)
        return token
    except Exception as e:
        print(f"获取 session_id 失败: {e}")
        # 打印错误响应内容
        if "response" in locals():
            print("Error response:", response.text)
        return None


"""
获取uniprobe列表
@param token: token
@type token: str
@param base_url: 基础URL
@type base_url: str
@param headers: 请求头
@type headers: dict
@return: uniprobe列表
@rtype: list

"""


def get_uniprobe_list(token, base_url, headers):
    """使用token获取uniprobe列表"""
    if not token:
        print("token为空，无法获取uniprobe列表")
        return []

    api_path = "/api/sidecars?query=&page=1&per_page=10000&only_active=false&sort=last_seen&order=asc"
    url = f"{base_url}{api_path}"
    # 复制全局headers并添加认证信息
    request_headers = headers.copy()
    # 尝试不同的认证格式
    request_headers["Authorization"] = f"Basic {token}"
    # print("url is :", url)
    # print("headers is :", request_headers)

    try:
        response = requests.get(url, headers=request_headers)
        # print("Response status code:", response.status_code)
        # print("Response headers:", response.headers)
        # print("Response content:", response.text)
        response.raise_for_status()
        return response.json()
    except Exception as e:
        print(f"获取uniprobe列表失败: {e}")
        return []


"""
从uniprobe列表中提取IP地址
@param uniprobe_list: uniprobe列表
@type uniprobe_list: list
@return: IP地址列表
@rtype: list

"""


def extract_ip_addresses(uniprobe_list):
    """从uniprobe列表中提取IP地址"""
    ip_addresses = set()

    for item in uniprobe_list["sidecars"]:
        ip_host = item.get("node_name")
        # 根据"-"分隔node_name并取第一位作为IP
        parts = ip_host.split("-")
        if parts:
            ip = parts[0]
            ip_addresses.add(ip)
    return ip_addresses


"""
保存结果到文件（CSV或Excel）
@param results: 结果列表
@type results: list
@param output_path: 输出文件路径
@type output_path: str
@param output_filename: 输出文件名（包含后缀）
@type output_filename: str
@param columns: 列名列表
@type columns: list
@param delimiter: 分隔符（仅CSV使用）
@type delimiter: str

"""


def save_results_to_file(results, output_path, output_filename, columns, delimiter="@"):
    """根据文件名后缀保存结果到CSV或Excel文件"""
    # 创建结果DataFrame
    result_df = pd.DataFrame(results, columns=columns)

    # 删除空行
    result_df.dropna(how="all", inplace=True)

    # 确保输出目录存在
    if not os.path.exists(output_path):
        os.makedirs(output_path)

    # 构建输出文件路径
    today = datetime.datetime.now().strftime("%m%d")
    output_path = os.path.join(output_path, today, output_filename)
    os.makedirs(os.path.dirname(output_path), exist_ok=True)

    # 获取文件扩展名
    file_ext = os.path.splitext(output_filename)[1].lower()

    if file_ext == ".csv":
        # 保存为CSV
        result_df.to_csv(
            output_path,
            index=False,
            encoding="utf-8-sig",
            sep=",",
            quoting=1,  # 1表示QUOTE_ALL，所有字段都加引号
            quotechar='"',
            header=None,
        )

        # 读取 CSV 文件，为每一行末尾添加行记录分隔符
        with open(output_path, "r", encoding="utf-8-sig") as file:
            lines = file.readlines()

        # 再写入修改后的内容
        with open(output_path, "w", encoding="utf-8-sig") as file:
            for line in lines:
                file.write(line.rstrip() + f",{delimiter}\n")

    elif file_ext in [".xlsx", ".xls"]:
        # 保存为Excel
        try:
            # 添加engine参数确保使用openpyxl
            result_df.to_excel(output_path, index=False, engine="openpyxl")
        except ImportError:
            print("保存为Excel失败: 请安装openpyxl库")
            # 保存为CSV
            output_path = output_path.replace(file_ext, ".csv")
            result_df.to_csv(
                output_path,
                index=False,
                encoding="utf-8-sig",
                sep=",",
                quoting=1,
                quotechar='"',
                header=None,
                engine="openpyxl",
            )
        except Exception as e:
            print(f"保存为Excel时发生错误: {str(e)}")
            # 保存为CSV作为备选
            output_path = output_path.replace(file_ext, ".csv")
            result_df.to_csv(
                output_path,
                index=False,
                encoding="utf-8-sig",
                sep=",",
                quoting=1,
                quotechar='"',
                header=None,
                engine="openpyxl",
            )
            with open(output_path, "r", encoding="utf-8-sig") as file:
                lines = file.readlines()
            with open(output_path, "w", encoding="utf-8-sig") as file:
                for line in lines:
                    file.write(line.rstrip() + f",{delimiter}\n")
    else:
        print(f"不支持的文件格式: {file_ext}，默认保存为CSV")
        # 默认保存为CSV
        output_path = os.path.splitext(output_path)[0] + ".csv"
        result_df.to_csv(
            output_path,
            index=False,
            encoding="utf-8-sig",
            sep=",",
            quoting=1,
            quotechar='"',
            header=None,
        )
        with open(output_path, "r", encoding="utf-8-sig") as file:
            lines = file.readlines()
        with open(output_path, "w", encoding="utf-8-sig") as file:
            for line in lines:
                file.write(line.rstrip() + f",{delimiter}\n")

    print(f"{output_filename} 匹配结果已保存到 {output_path}")
    print(f"{output_filename} 匹配结果记录数: {len(result_df)}\n")


"""
处理数据并生成主机CSV文件
@param regular_df: 固化数据DataFrame
@type regular_df: pd.DataFrame
@param manage_df: 管理数据DataFrame
@type manage_df: pd.DataFrame
@param system_df: 系统数据DataFrame
@type system_df: pd.DataFrame
@param uniprobe_list: uniprobe列表
@type uniprobe_list: list
@param output_path: 输出文件路径
@type output_path: str
@param output_filename: 输出文件名
@type output_filename: str

"""


def process_and_generate_zhuji_csv(
    system_df, manage_df, regular_df, uniprobe_list, output_path, output_filename
):
    """处理数据并生成主机CSV文件"""
    # 检查system_df是否存在
    if system_df is None:
        print("错误: 传入的系统DataFrame为空")
        return {}
    # 检查manage_df是否存在
    if manage_df is None:
        print("错误: 传入的管理DataFrame为空")
        return {}
    # 检查Excel文件是否存在
    if regular_df is None:
        print("错误: 传入的固化DataFrame为空")
        return {}
    # 获取uniprobe列表
    if not uniprobe_list:
        print("探针列表为空")
        return {}

    # 打印探针列表记录数
    print(f"已上线探针数量: {len(uniprobe_list['sidecars'])}")

    # 匹配IP并构建结果
    results = []
    # 存储未匹配到系统的IP
    no_match_ip_list = []
    # 创建IP到系统信息的映射字典
    ip_system_map = {}
    # 创建接入网络类型的映射字典
    network_condition_dict = {}
    # 特殊IP处理映射字典
    special_ip = {
        # 服务端主机
        "************": {
            "system_code": "GZXT-JS-N-999-999999",
            "system_unit": "效能管理平台",
            "system_shortname": "效能管理平台",
        },
        "************": {
            "system_code": "GZXT-JS-N-999-999999",
            "system_unit": "效能管理平台",
            "system_shortname": "效能管理平台",
        },
        "************": {
            "system_code": "GZXT-JS-N-999-999999",
            "system_unit": "效能管理平台",
            "system_shortname": "效能管理平台",
        },
        "************": {
            "system_code": "GZXT-JS-N-999-999999",
            "system_unit": "效能管理平台",
            "system_shortname": "效能管理平台",
        },
        "************": {
            "system_code": "GZXT-JS-N-999-999999",
            "system_unit": "效能管理平台",
            "system_shortname": "效能管理平台",
        },
        "172.28.50.17": {
            "system_code": "GZXT-JS-N-999-999999",
            "system_unit": "效能管理平台",
            "system_shortname": "效能管理平台",
        },
        # 2025.6 新增
        "10.194.110.3": {
            "system_code": "GZXT-JS-N-037-037214",
            "system_unit": "市卫生健康委",
            "system_shortname": "生育服务办事",
        },
        "10.194.101.132": {
            "system_code": "GZXT-JS-N-032-000352",
            "system_unit": "市交通运输局",
            "system_shortname": "道路停车",
        },
        "10.201.50.3": {
            "system_code": "GZXT-JS-N-029-000462",
            "system_unit": "市规划和自然资源局",
            "system_shortname": "内控一体化",
        },
        "10.202.5.36": {
            "system_code": "GZXT-JS-N-029-000912",
            "system_unit": "市规划和自然资源局",
            "system_shortname": "基础设施",
        },
        "10.201.35.10": {
            "system_code": "GZXT-JS-N-031-002208",
            "system_unit": "市住房城乡建设局",
            "system_shortname": "房屋管理",
        },
        # 2025.7.1 新增
        "10.201.35.12": {
            "system_code": "GZXT-JS-N-031-002729",
            "system_unit": "市住房城乡建设局",
            "system_shortname": "玻璃幕墙监测",
        },
        "10.194.69.13": {
            "system_code": "GZXT-JS-N-031-002734",
            "system_unit": "市住房城乡建设局",
            "system_shortname": "房屋应用建设",
        },
        "10.194.69.14": {
            "system_code": "GZXT-JS-N-031-002734",
            "system_unit": "市住房城乡建设局",
            "system_shortname": "房屋应用建设",
        },
        # 2025.7.11 新增
        "10.201.35.150": {
            "system_code": "GZXT-JS-N-031-002208",
            "system_unit": "市住房城乡建设局",
            "system_shortname": "房屋管理",
        },
        "10.201.35.151": {
            "system_code": "GZXT-JS-N-031-002612",
            "system_unit": "市住房城乡建设局",
            "system_shortname": "存量房网上",
        },
        "10.201.35.112": {
            "system_code": "GZXT-JS-N-031-002728",
            "system_unit": "市住房城乡建设局",
            "system_shortname": "房地产管理",
        },
        "10.201.35.113": {
            "system_code": "GZXT-JS-N-031-002612",
            "system_unit": "市住房城乡建设局",
            "system_shortname": "存量房网上",
        },
        "10.201.101.2": {
            "system_code": "GZXT-JS-N-031-031685",
            "system_unit": "市住房城乡建设局",
            "system_shortname": "房屋数据库应用",
        },
        "10.201.30.50": {
            "system_code": "GZXT-JS-N-039-002611",
            "system_unit": "市应急管理局",
            "system_shortname": "工矿商贸",
        },
        "10.201.36.3": {
            "system_code": "GZXT-JS-N-031-000283",
            "system_unit": "市住房城乡建设局",
            "system_shortname": "事务中心综合管理",
        },
        # 2025.7.14 新增
        "10.201.94.4": {
            "system_code": "GZXT-JS-N-030-030709",
            "system_unit": "市生态环境局",
            "system_shortname": "噪声环境自动监测",
        },
        "10.201.96.3": {
            "system_code": "GZXT-JS-W-030-000784",
            "system_unit": "市生态环境局",
            "system_shortname": "固体废物管理",
        },
        # 2025.7.18 新增
        "10.194.69.45": {
            "system_code": "GZXT-JS-N-031-000667",
            "system_unit": "市住房城乡建设局",
            "system_shortname": "施工质量安全",
        },
        "10.201.81.100": {
            "system_code": "GZXT-JS-N-031-031683",
            "system_unit": "市住房城乡建设局",
            "system_shortname": "应急抢险信息管理",
        },
        # 2025.7.28 新增
        "10.201.62.6": {
            "system_code": "GZXT-JS-N-042-000844",
            "system_unit": "市市场监管局",
            "system_shortname": "综合执法",
        },
        "10.201.62.7": {
            "system_code": "GZXT-JS-N-042-000843",
            "system_unit": "市市场监管局",
            "system_shortname": "双随机一公开",
        },
        "10.197.159.18": {
            "system_code": "GZXT-JS-N-039-002527",
            "system_unit": "市应急管理局",
            "system_shortname": "行政执法电子监察",
        },
        # 2025.7.30 新增
        "10.194.253.171": {
            "system_code": "GZXT-JS-W-051-000381",
            "system_unit": "市政务服务和数据管理局",
            "system_shortname": "一窗综合",
        },
        # 2025.8.5 新增
        "10.201.41.20": {
            "system_code": "GZXT-JS-N-047-047282",
            "system_unit": "市城市管理综合执法局",
            "system_shortname": "智慧环卫",
        },
        "10.201.41.18": {
            "system_code": "GZXT-JS-N-047-047278",
            "system_unit": "市城市管理综合执法局",
            "system_shortname": "数据治理与分析",
        },
        # 2025.8.6 新增
        "10.194.92.116": {
            "system_code": "GZXT-JS-N-039-002686",
            "system_unit": "市应急管理局",
            "system_shortname": "数据备份管理",
        },
        # 2025.8.8 新增
        "10.202.6.1": {
            "system_code": "GZXT-JS-N-029-000912",
            "system_unit": "市规划和自然资源局",
            "system_shortname": "基础设施",
        },
        "10.202.6.2": {
            "system_code": "GZXT-JS-N-029-000912",
            "system_unit": "市规划和自然资源局",
            "system_shortname": "基础设施",
        },
        "10.202.6.3": {
            "system_code": "GZXT-JS-N-029-000912",
            "system_unit": "市规划和自然资源局",
            "system_shortname": "基础设施",
        },
        "10.202.6.4": {
            "system_code": "GZXT-JS-N-029-000912",
            "system_unit": "市规划和自然资源局",
            "system_shortname": "基础设施",
        },
        "10.202.6.5": {
            "system_code": "GZXT-JS-N-029-000912",
            "system_unit": "市规划和自然资源局",
            "system_shortname": "基础设施",
        },
        "10.202.6.6": {
            "system_code": "GZXT-JS-N-029-000912",
            "system_unit": "市规划和自然资源局",
            "system_shortname": "基础设施",
        },
        "10.202.6.10": {
            "system_code": "GZXT-JS-N-029-000912",
            "system_unit": "市规划和自然资源局",
            "system_shortname": "基础设施",
        },
        "10.202.6.11": {
            "system_code": "GZXT-JS-N-029-000912",
            "system_unit": "市规划和自然资源局",
            "system_shortname": "基础设施",
        },
        "10.202.6.12": {
            "system_code": "GZXT-JS-N-029-000912",
            "system_unit": "市规划和自然资源局",
            "system_shortname": "基础设施",
        },
        "10.202.6.13": {
            "system_code": "GZXT-JS-N-029-000912",
            "system_unit": "市规划和自然资源局",
            "system_shortname": "基础设施",
        },
        "10.202.6.14": {
            "system_code": "GZXT-JS-N-029-000912",
            "system_unit": "市规划和自然资源局",
            "system_shortname": "基础设施",
        },
        "10.202.6.15": {
            "system_code": "GZXT-JS-N-029-000912",
            "system_unit": "市规划和自然资源局",
            "system_shortname": "基础设施",
        },
        "10.202.6.16": {
            "system_code": "GZXT-JS-N-029-000912",
            "system_unit": "市规划和自然资源局",
            "system_shortname": "基础设施",
        },
        "10.202.6.17": {
            "system_code": "GZXT-JS-N-029-000912",
            "system_unit": "市规划和自然资源局",
            "system_shortname": "基础设施",
        },
        "10.202.6.18": {
            "system_code": "GZXT-JS-N-029-000912",
            "system_unit": "市规划和自然资源局",
            "system_shortname": "基础设施",
        },
        "10.202.6.19": {
            "system_code": "GZXT-JS-N-029-000912",
            "system_unit": "市规划和自然资源局",
            "system_shortname": "基础设施",
        },
        "10.202.6.20": {
            "system_code": "GZXT-JS-N-029-000912",
            "system_unit": "市规划和自然资源局",
            "system_shortname": "基础设施",
        },
        # 2025.8.12 新增
        "10.201.35.101": {
            "system_code": "GZXT-JS-N-031-031680",
            "system_unit": "市住房城乡建设局",
            "system_shortname": "建设工程",
        },
    }

    # 构建接入网络类型映射字典
    for index, row in manage_df.iterrows():
        if pd.isna(row["系统编号"]) or pd.isna(row["接入网络类型"]):
            continue
        if not str(row["系统编号"]).startswith("GZXT"):
            continue
        network_condition_dict[str(row["系统编号"])] = str(row["接入网络类型"])

    for item in uniprobe_list["sidecars"]:
        ip_host = item.get("node_name")
        ip = ip_host.split("-")[0]
        matched = False

        # 检查特殊IP
        if ip in special_ip:
            matched = True
            system_info = special_ip[ip]
            system_name = system_info["system_shortname"]
            system_code = system_info["system_code"]
            system_unit = system_info["system_unit"]
            network_condition = network_condition_dict.get(system_code, "")

            # 从system_df中查找对应的系统名称简称
            short_name_row = system_df[system_df["系统编号"] == system_code]
            system_short_name = (
                short_name_row.iloc[0]["系统名称简称"]
                if not short_name_row.empty
                else system_name
            )

            results.append(
                [
                    ip_host,
                    system_short_name,  # 使用系统简称
                    system_unit,
                    network_condition,
                    system_short_name,  # 系统简称
                ]
            )
            ip_system_map[ip] = {
                "所属一级单位": system_unit,
                "系统编号": system_code,
                "系统名称": system_name,
            }
            continue

        # 常规IP处理逻辑
        ip_match = (regular_df["私网IP"] == ip) | (regular_df["IP地址"] == ip)
        if ip_match.any():
            matched = True
            # 从固化数据里获取匹配的 ip 数据
            ip_data = regular_df[ip_match]
            # 获取系统名称
            system_name = ip_data.iloc[0].get("系统名称", "")
            # 获取系统编号
            system_code = ip_data.iloc[0].get("系统编号", "")
            # 获取一级单位
            system_unit = ip_data.iloc[0].get("所属一级单位", "")
            # 获取接入网络类型
            network_condition = network_condition_dict.get(system_code, "")
            # 从system_df中查找对应的系统名称简称
            short_name_row = system_df[system_df["系统编号"] == system_code]
            system_short_name = (
                short_name_row.iloc[0]["系统名称简称"]
                if not short_name_row.empty
                else system_name
            )

            results.append(
                [
                    ip_host,
                    system_code,
                    system_unit,
                    network_condition,
                    system_short_name,  # 系统简称
                ]
            )
            # 将系统信息添加到映射字典
            ip_system_map[ip] = {
                "所属一级单位": system_unit,
                "系统编号": system_code,
                "系统名称": system_name,
            }
            continue

        # 没有匹配到，记录未匹配的IP
        if not matched:
            no_match_ip_list.append(ip)

    # 打印未匹配到系统的IP个数
    print(f"未匹配到系统的探针个数: {len(no_match_ip_list)}")

    # 将未匹配到系统的IP输出到CSV
    if no_match_ip_list:
        # 创建结果列表（转换为列表的列表格式以适应save_results_to_file函数）
        no_match_results = [[ip] for ip in no_match_ip_list]

        # 定义列名
        no_match_columns = ["未匹配到系统的IP"]

        # 调用函数保存结果
        save_results_to_file(
            no_match_results,
            output_path,
            "no_match_system/no_match_system.csv",
            no_match_columns,
            delimiter="",
        )

    # 调用函数保存结果
    zhuji_columns = ["IP地址", "系统名称", "所属一级单位", "接入网络类型", "系统简称"]

    save_results_to_file(
        results, output_path, output_filename, zhuji_columns, delimiter="@"
    )

    # 返回IP与系统信息的映射字典
    return ip_system_map


"""
处理数据并生成系统CSV文件
@param regular_df: 固化数据DataFrame
@type regular_df: pd.DataFrame
@param uniprobe_list: uniprobe列表
@type uniprobe_list: list
@param output_path: 输出文件路径
@type output_path: str
@param output_filename: 输出文件名
@type output_filename: str

"""


def process_and_generate_xitong_csv(
    regular_df, uniprobe_list, output_path, output_filename
):
    """处理数据并生成系统CSV文件"""
    # 检查Excel文件是否存在
    if regular_df is None:
        print("错误: 传入的固化DataFrame为空")
        return

    # 获取uniprobe列表
    if not uniprobe_list:
        print("探针列表为空")
        return

    # 确保Excel中有必要的列
    required_columns = [
        "IP地址",
        "私网IP",
        "系统名称",
        "系统编号",
        "所属一级单位",
        "主机用途",
        "端口",
    ]
    missing_columns = [
        col for col in required_columns if col not in regular_df.columns]
    if missing_columns:
        print(f"错误: Excel文件中缺少必要的列: {', '.join(missing_columns)}")
        return

    # 提取uniprobe_list中的所有IP地址，存储到集合中以便快速查找
    uniprobe_ip_list = extract_ip_addresses(uniprobe_list)
    print(f"从uniprobe列表中提取到 {len(uniprobe_ip_list)} 个唯一IP地址")

    # 先固化一部分数据
    results = {
        "*************:30000": ["GZXT-JS-N-027-027275", "市财政局"],
        "*************:3306": ["SJK_GZXT-JS-N-043-000528", "市体育局"],
        "************:27017": ["SJK_GZXT-JS-W-038-000509", "市退役军人事务局"],
        "************:5236": ["SJK_GZXT-JS-W-038-000509", "市退役军人事务局"],
        "************:5236": ["SJK_GZXT-JS-W-038-000509", "市退役军人事务局"],
        "*************:5236": ["SJK_GZXT-JS-N-028-028193", "市人力资源社会保障局"],
        "************:5237": ["SJK_GZXT-JS-N-038-000513", "市退役军人事务局"],
        "************:5238": ["SJK_GZXT-JS-N-038-000513", "市退役军人事务局"],
        "************:5239": ["SJK_GZXT-JS-N-038-000513", "市退役军人事务局"],
        "************:5240": ["SJK_GZXT-JS-N-038-000513", "市退役军人事务局"],
        "************:5241": ["SJK_GZXT-JS-N-038-000513", "市退役军人事务局"],
        "************:5242": ["SJK_GZXT-JS-N-038-000513", "市退役军人事务局"],
        "10.194.180.238:1521": ["SJK_GZXT-JS-N-025-025933", "市民政局"],
        "10.194.180.239:1521": ["SJK_GZXT-JS-N-025-025933", "市民政局"],
        "10.194.181.61:5236": ["SJK_GZXT-JS-N-025-000289", "市民政局"],
        "10.194.181.62:5236": ["SJK_GZXT-JS-N-025-000289", "市民政局"],
        "10.197.4.35:15025": ["SJK_GZXT-JS-N-032-000353 ", "市交通运输局"],
        "10.194.103.128:3306": ["SJK_GZXT-JS-N-032-000023", "市交通运输局"],
        "10.194.103.129:3306": ["SJK_GZXT-JS-N-032-000023", "市交通运输局"],
        "10.194.100.65:3306": ["SJK_GZXT-JS-N-032-000025", "市交通运输局"],
        "10.201.74.4:5236": ["SJK_GZXT-JS-N-032-003117", "市交通运输局"],
        "10.201.74.2:5236": ["SJK_GZXT-JS-N-032-003117", "市交通运输局"],
        "10.201.26.13:5236": ["SJK_GZXT-JS-N-044-004418", "市统计局"],
        "10.201.26.13:6001": ["SJK_GZXT-JS-N-044-004418", "市统计局"],
        "10.201.26.13:6002": ["SJK_GZXT-JS-N-044-004418", "市统计局"],
        "10.201.26.13:6003": ["SJK_GZXT-JS-N-044-004418", "市统计局"],
        "10.196.131.39:5238": ["SJK_GZXT-JS-W-028-028147", "市人力资源社会保障局"],
        "10.196.131.40:5238": ["SJK_GZXT-JS-W-028-028147", "市人力资源社会保障局"],
        "10.205.17.100:5236": ["SJK_GZXT-JS-N-037-037208", "市卫生健康委"],
        "10.196.192.64:5432": ["SJK_GZXT-JS-N-032-003257", "市交通运输局"],
        "172.16.33.118:1521": ["SJK_GZXT-JS-N-032-000361", "市交通运输局"],
        # 2025.7.21 更新
        "10.197.125.27:5432": ["SJK_GZXT-JS-N-032-003036", "市交通运输局"],
        "10.197.125.28:5432": ["SJK_GZXT-JS-N-032-003036", "市交通运输局"],
        # 2025.7.22 更新
        "*************:6661": ["SJK_GZXT-JS-N-032-000027", "市交通运输局"],
        "*************:1521": ["SJK_GZXT-JS-N-042-002405", "市市场监管局"],
        # 2025.8.12 更新
        "*************:5236": ["SJK_GZXT-JS-N-047-003009", "市城市管理综合执法局"],
        "*************:5236": ["SJK_GZXT-JS-N-047-003009", "市城市管理综合执法局"],
    }

    # 遍历regular_df中的每一行
    for index, row in regular_df.iterrows():
        # 获取当前行的IP地址(私网IP和IP地址)
        public_ip = row.get("IP地址")
        private_ip = row.get("私网IP")
        ip = public_ip
        # 获取端口
        port = row.get("端口", "")
        # 获取系统名称
        system_name = row.get("系统名称")
        # 获取系统编号
        system_code = row.get("系统编号")
        # 获取一级单位
        system_unit = row.get("所属一级单位")
        # 获取主机用途
        host_usage = row.get("主机用途").lower()

        # 端口不存在或为nan，跳过
        if not port or pd.isna(port) or str(port).lower() == "nan":
            continue

        # 处理端口号
        try:
            # 先转为浮点数再转为整数，去除小数部分
            port = int(float(port))
        except ValueError:
            # 如果转换失败则保持原端口值
            pass

        # 检查私网IP和IP地址哪个在uniprobe_ip_list集合中
        # 当私网IP为空的时候，直接使用IP地址
        ip = ""
        if pd.notna(private_ip) and str(private_ip).strip() and str(private_ip).lower() != "nan":
            ip = private_ip.strip()
        elif pd.notna(public_ip) and str(public_ip).strip() and str(public_ip).lower() != "nan":
            ip = public_ip.strip()

        # 如果IP为空或不在uniprobe列表中，则跳过
        if not ip or ip not in uniprobe_ip_list:
            print(f"IP {ip} 不在uniprobe列表中")
            continue

        # 构建实例名称
        ip_port = f"{ip}:{port}" if ip and port else ""

        # 如果已经处理过该实例，跳过
        if ip_port in results:
            print(f"实例名称 {ip_port} 已处理过")
            continue

        # 根据主机用途添加前缀
        if host_usage == "数据库服务器":
            # 数据库服务器，系统编号前加 SJK_
            system_code = f"SJK_{system_code}"
        elif host_usage == "web服务器":
            # Web服务器原样输出，不需要额外处理
            system_code = f"{system_code}"
        else:
            # 其他类型服务器，不做处理
            continue

        # 存储到字典中，直接使用列表格式存储
        results[ip_port] = [str(system_code), str(system_unit)]

    # 将字典的值转换为列表格式，以便与save_results_to_file兼容
    results_list = [
        [ip_port, system_code, system_unit]
        for ip_port, (system_code, system_unit) in results.items()
    ]

    # 调用函数保存结果
    xitong_columns = ["实例名称", "系统编号", "所属一级单位"]
    save_results_to_file(
        results_list, output_path, output_filename, xitong_columns, delimiter="@"
    )


"""
处理uniprobe列表并生成包含系统信息的CSV文件
@param uniprobe_list: uniprobe列表
@type uniprobe_list: list
@param ip_system_map: IP地址到系统信息的映射字典
@type ip_system_map: dict
@param output_path: 输出文件路径
@type output_path: str
@param output_filename: 输出文件名
@type output_filename: str

"""


def process_and_generate_uniprobe_list(
    uniprobe_list, ip_system_map, output_path, output_filename
):
    """处理uniprobe列表并生成包含系统信息的CSV文件"""
    if not uniprobe_list:
        print("探针列表为空")
        return

    results = []
    # 定义CSV列名
    uniprobe_columns = [
        "IP_主机名",
        "IP地址",
        "是否活跃",
        "所属一级单位",
        "系统编号",
        "系统名称",
        "操作系统",
        "服务端IP地址",
        "最后一次露面时间",
        "探针版本",
        "创建时间",
    ]

    for item in uniprobe_list["sidecars"]:
        ip_host = item.get("node_name", "")
        ip = ip_host.split("-")[0] if ip_host else ""
        active = "是" if item.get("active", False) else "否"
        node_details = item.get("node_details", {})
        operating_system = node_details.get("operating_system", "")
        server_ip = node_details.get("ip", "")
        last_seen = item.get("last_seen", "")
        sidecar_version = item.get("sidecar_version", "")
        create_time = item.get("create_time", "")

        # 从映射字典中获取系统信息
        system_info = ip_system_map.get(ip, {})
        system_name = system_info.get("系统名称", "")
        system_unit = system_info.get("所属一级单位", "")
        system_code = system_info.get("系统编号", "")

        # 添加到结果列表
        results.append(
            [
                ip_host,
                ip,
                active,
                system_unit,
                system_code,
                system_name,
                operating_system,
                server_ip,
                last_seen,
                sidecar_version,
                create_time,
            ]
        )

    # 调用函数保存结果
    save_results_to_file(
        results, output_path, output_filename, uniprobe_columns, delimiter="\n"
    )


"""
通用数据文件读取函数
@param file_path: 文件路径
@type file_path: str
@param **kwargs: 传递给read_excel或read_csv的参数
@return: 读取的数据
@rtype: pd.DataFrame
"""


def read_data_file(file_path, **kwargs):
    """根据文件扩展名读取Excel或CSV文件"""
    try:
        # 检查文件是否存在
        if not os.path.exists(file_path):
            print(f"文件不存在: {file_path}")
            return None

        # 检查文件大小是否为0
        if os.path.getsize(file_path) == 0:
            print(f"文件大小为0: {file_path}")
            return None

        # 获取文件扩展名
        file_ext = os.path.splitext(file_path)[1].lower()

        # 检查文件扩展名是否为Excel或CSV
        if file_ext in [".xlsx", ".xls"]:
            return pd.read_excel(file_path, **kwargs)
        elif file_ext == ".csv":
            return pd.read_csv(file_path, **kwargs)
        else:
            print(f"不支持的文件格式: {file_ext}")
            return None
    except Exception as e:
        print(f"读取文件 {file_path} 失败: {e}")
        return None


"""
主函数
"""


def main():

    # 配置信息
    base_url = "http://************:9000/"
    username = "admin"
    password = "Aidynamic@2025"
    headers = {
        "Accept": "application/json",
        "Accept-Language": "zh-CN,zh;q=0.9",
        "Content-Type": "application/json",
        "X-Requested-By": "XMLHttpRequest",
        "X-Requested-With": "XMLHttpRequest",
    }

    # 解析命令行参数
    parser = argparse.ArgumentParser(description="获取uniprobe列表并与Excel数据匹配")
    parser.add_argument(
        "-i",
        "--input",
        required=True,
        dest="regular_path",
        help="固化数据文件路径",
        default="D:\\Working\\ZhangCe\\项目材料\\广州市政数局\\系统效能评估\\生产环境探针测试&核对\\固化csv\\数据固化\\固化数据.xlsx",
    )
    parser.add_argument(
        "-o",
        "--output",
        type=str,
        dest="output_path",
        help="生成的CSV路径",
        default="D:\\Working\\ZhangCe\\项目材料\\广州市政数局\\系统效能评估\\运维知识库\\Python生成csv\\result\\2025\\CSV Files",
    )
    parser.add_argument(
        "-sf",
        "--system_file",
        required=True,
        dest="system_file_path",
        help="系统编码表文件路径",
        default="D:\\Working\\ZhangCe\\项目材料\\广州市政数局\\系统效能评估\\运维知识库\\Python生成csv\\2025年评估.xlsx",
    )
    parser.add_argument(
        "-mf",
        "--manage_file",
        required=True,
        dest="manage_file_path",
        help="项目管理应用类台账表文件路径",
        default="D:\\Working\\ZhangCe\\项目材料\\广州市政数局\\系统效能评估\\运维知识库\\Python生成csv\\应用类系统导出(2025-06-26+18_06_18).xlsx",
    )
    parser.add_argument(
        "-ss",
        "--system_sheet",
        type=str,
        dest="system_sheet_name",
        help="系统编码表sheet名称",
        default="非参评系统下线登记",
    )
    parser.add_argument(
        "-ms",
        "--manage_sheet",
        type=str,
        dest="manage_sheet_name",
        help="项目管理应用类台账表sheet名称",
        default="基本信息",
    )

    args = parser.parse_args()
    regular_path = args.regular_path
    output_path = args.output_path
    system_file_path = args.system_file_path
    manage_file_path = args.manage_file_path
    system_sheet_name = args.system_sheet_name
    manage_sheet_name = args.manage_sheet_name

    # 获取token
    token = get_token(base_url, username, password, headers)

    if not token:
        print("获取登录token失败，程序退出")
        return

    # 获取uniprobe列表
    uniprobe_list = get_uniprobe_list(token, base_url, headers)

    if not uniprobe_list:
        return

    # 读取固化数据
    try:
        regular_df = read_data_file(regular_path, engine="openpyxl")
        print(f"成功读取固化数据，共有 {len(regular_df)} 条记录")
    except Exception as e:
        print(f"读取固化数据失败: {e}")
        return

    # 读取系统编码表
    try:
        system_df = read_data_file(
            system_file_path,
            sheet_name=system_sheet_name,
            skiprows=1,
            engine="openpyxl",
        )
        print(f"成功读取系统编码表，共有 {len(system_df)} 条记录")
    except Exception as e:
        print(f"读取系统编码表失败: {e}")
        return

    # 读取项目管理应用类台账表
    try:
        manage_df = read_data_file(
            manage_file_path, sheet_name=manage_sheet_name, engine="openpyxl"
        )
        print(f"成功读取项目管理表，共有 {len(manage_df)} 条记录")
    except Exception as e:
        print(f"读取项目管理表失败: {e}")
        return

    # 确保Excel中有'IP地址'和'私网IP'列
    if "IP地址" not in regular_df.columns and "私网IP" not in regular_df.columns:
        print("错误: 固化数据文件中没有找到'IP地址'或'私网IP'列")
        return

    # 转换为字符串类型
    regular_df["IP地址"] = regular_df["IP地址"].astype(str)
    regular_df["私网IP"] = regular_df["私网IP"].astype(str)
    regular_df["端口"] = regular_df["端口"].astype(str)
    regular_df["主机用途"] = regular_df["主机用途"].astype(str)

    # 调用生成主机CSV的函数
    ip_system_map = process_and_generate_zhuji_csv(
        system_df,
        manage_df,
        regular_df,
        uniprobe_list,
        output_path,
        "zhuji.csv",
    )

    # 调用生成系统CSV的函数
    process_and_generate_xitong_csv(
        regular_df, uniprobe_list, output_path, "xitong.csv"
    )

    # 调用生产uniprobe列表CSV的函数并匹配系统
    process_and_generate_uniprobe_list(
        uniprobe_list, ip_system_map, output_path, "uniprobe_list.xlsx"
    )


if __name__ == "__main__":
    main()
