import os
import sys
import time
import shutil
import logging
import argparse
import requests
import json
import pandas as pd
from util import load_env
from uniserver import UniserverApiManager
from data_handler import get_uniprobe_node_name_list
import datetime


def setup_logging(log_file=None, debug=False):
    """
    设置日志器\n
    :param log_file: 日志文件路径，如果为None则不写入文件\n
    :param debug: 是否开启调试模式，调试模式下日志级别为DEBUG，否则为INFO\n
    :return: None
    """
    handlers = []
    console_handler = logging.StreamHandler(sys.stderr)
    handlers.append(console_handler)

    if log_file:
        file_handler = logging.FileHandler(log_file)
        handlers.append(file_handler)

    log_format = "%(asctime)s [%(levelname)s] %(name)s: %(message)s" if debug else "%(levelname)s: %(message)s"

    logging.basicConfig(
        level=logging.DEBUG if debug else logging.INFO,
        format=log_format,
        handlers=handlers,
        # encoding='utf-8'
    )


def parse_args():

    parser = argparse.ArgumentParser()
    # 配置入参关键字
    parser.add_argument(

        "-f",

        "--file_path",

        help="待转换文档路径",

        dest="file_path",

        type=str,

        required=True

    )

    parser.add_argument(

        "-s",

        "--sheet_name",

        help="待转换文档sheet名称(默认为【所需服务器字段】)",

        dest="sheet_name",

        type=str,

        default="所需服务器字段"

    )

    parser.add_argument(

        "-sf",

        "--system_file_path",

        help="系统名称对应编码文档路径",

        dest="system_file_path",

        type=str,

        required=True

    )

    parser.add_argument(

        "-ss",

        "--system_file_sheet_name",

        help="系统名称对应编码文档sheet名称",

        dest="system_file_sheet_name",

        type=str,

    )

    parser.add_argument(

        "-rf",

        "--regular_file_path",

        help="固化文档路径",

        dest="regular_file_path",

        type=str,

        required=True

    )

    parser.add_argument(

        "-rs",

        "--regular_file_sheet_name",

        help="固化文档sheet名称",

        dest="regular_file_sheet_name",

        type=str,

    )

    parser.add_argument(

        "-g",

        "--generate_path",

        help="生成结果保存路径(默认当前目录)",

        dest="generate_path",

        type=str,

        default=os.getcwd()

    )

    parser.add_argument(

        "-cf",

        "--config_file",

        help="指定配置文件(默认为【settings.yaml】)",

        dest="config_file",

        type=str,

        default='settings.yaml'

    )
    args = parser.parse_args()

    return args, parser


def get_uniprobe_list():
    url = "http://************:9000/api/sidecars?query=&page=1&per_page=10000&only_active=false&sort=last_seen&order=asc"

    payload = ""
    headers = {
        'Accept': 'application/json',
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6',
        'Authorization': 'Basic Yzc5NmNjNmMtYzQ5ZC00NTQ2LTliNzctZGZhNGQ3NDA1NTg4OnNlc3Npb24=',
        'Cache-Control': 'no-cache',
        'Connection': 'keep-alive',
        'Pragma': 'no-cache',
        'Referer': 'http://************:9000/system/sidecars',
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
        'X-Graylog-No-Session-Extension': 'true',
        'X-Requested-By': 'XMLHttpRequest',
        'X-Requested-With': 'XMLHttpRequest',
        'Content-Type': 'application/json'
    }

    response = requests.request("GET", url, headers=headers, data=payload)

    if response.status_code != 200:
        raise Exception(f"请求失败，状态码：{response.status_code}")
    else:
        json_data = json.loads(response.text)
        uniprobe_list = [item['node_name'].split(
            "-")[0] for item in json_data['sidecars']]
        return uniprobe_list


def create_folder(folder_path):
    """
    创建文件夹，如果文件夹已存在则不做任何操作\n
    :param folder_path: 文件夹路径\n
    """
    try:
        os.makedirs(folder_path, exist_ok=True)
        logger.info(f"文件夹 {folder_path} 创建成功或已存在。")
    except Exception as e:
        logger.error(f"创建文件夹时发生错误：{str(e)}", exc_info=True)
        raise Exception(f"创建文件夹 {folder_path} 时发生错误：{str(e)}")


def parse_origin_file(origin_file_path, sheet_name, required_columns, skiprows=None):
    """
    通过Pandas解析原始文件内容\n
    :param origin_file_path: 原始Excel文件路径\n
    :param sheet_name: 要解析的sheet名称\n
    :return: DataFrame对象\n
    """
    try:
        logger.info(f"开始解析文件：{origin_file_path}，sheet名称：{sheet_name}")
        # 读取Excel文件，可根据需要调整sheet_name参数
        df = pd.read_excel(origin_file_path, engine='openpyxl',
                           sheet_name=sheet_name, skiprows=skiprows)
        df.dropna(how='all', inplace=True)  # 删除全为空值的行
        # 检查是否包含必要的列
        for col in required_columns:
            if col not in df.columns:
                logger.error(f"错误：文档中缺少必要的列：{col}，请检查文档格式。", exc_info=True)
                raise ValueError(f"缺少必要的列：{col}")
        return df
    except FileNotFoundError:
        logger.error(
            f"错误：未找到{origin_file_path}文件，请检查路径和文件名是否正确。", exc_info=True)
        raise FileNotFoundError(f"未找到文件：{origin_file_path}")
    except Exception as e:
        logger.error(f"读取文档{origin_file_path}过程中发生错误：{str(e)}", exc_info=True)
        raise Exception(f"读取文档{origin_file_path}时发生错误：{str(e)}")


def gen_checkout_data(df, config):
    """
    生成检查表(根据配置的key_columns生成)\n
    :param df: DataFrame对象\n
    :param config: 配置字典\n
    :return: 处理后的数据对象\n
    """
    try:
        logger.info(f"开始生成{config['filename']}检查表")
        result = {}

        # 从配置获取列名
        key_cols = config['key_columns']
        value_cols = config['value_columns']
        separator = config['separator']

        for index, data in df.iterrows():
            if pd.isna(data.端口) or data.端口 in ["", None]:
                continue
            # 使用配置的分隔符组合key
            key_parts = [str(getattr(data, col, "")).strip() if "端口" not in col else str(
                int(getattr(data, col, ""))) for col in key_cols]
            key = separator.join(key_parts)

            # 获取value列的值
            values = [str(getattr(data, col, "")).strip()
                      for col in value_cols]

            if key in result:
                logger.warning(
                    f"{config['filename']}检查表发现重复的键值组合：{key}-{values}，跳过处理")
                continue

            result[key] = values

        return result
    except Exception as e:
        logger.error(f"生成检查表时发生错误：{str(e)}", exc_info=True)
        raise Exception(f"生成检查表时发生错误")


def gen_checkout_ip_port(df):
    """
    生成检查表(主键:ip:端口)核心业务逻辑\n
    :param df: DataFrame对象\n
    :return: 处理后的数据对象\n
    """
    try:
        logger.info("开始生成IP-端口检查表")
        ip_port_result = {}
        for index, data in df.iterrows():
            key = str(getattr(data, "IP地址", "")).strip() + \
                '-' + str(getattr(data, "端口", "")).strip()
            system_name = str(getattr(data, "系统名称", "")).strip()
            first_level_unit = str(getattr(data, "所属一级单位", "")).strip()
            if key in ip_port_result:
                logger.warning(
                    f"发现重复的IP地址-端口组合：{key}-系统名称:{system_name},所属一级单位:{first_level_unit}，跳过处理")
                continue
            ip_port_result[key] = [system_name, first_level_unit]
        return ip_port_result
    except Exception as e:
        logger.error(f"生成检查表时发生错误：{str(e)}", exc_info=True)
        raise Exception(f"生成ip-端口检查表时发生错误")


def save_to_csv(data, output_file_path, config):
    """
    将数据保存为CSV文件\n
    :param data: 数据对象\n
    :param output_file_path: 输出CSV文件路径\n
    :param config: 输出配置\n
    """
    try:
        with open(output_file_path, 'w', encoding=config['encoding']) as f:
            for key, values in data.items():
                values_str = config['field_separator'].join(
                    f'"{v}"' for v in values)
                f.write(
                    f'"{key}"{config["field_separator"]}{values_str}{config["record_separator"]}\n')
        logger.info(f"数据已成功保存到 {output_file_path}")
    except Exception as e:
        logger.error(f"保存CSV文件时发生错误：{str(e)}", exc_info=True)
        raise


if __name__ == "__main__":
    try:
        # 配置文件读取
        load_env(f'{os.path.dirname(__file__)}/.env')
        import yaml
        # 命令行参数
        args, parser = parse_args()
        gen_time = time.strftime("%Y%m%d%H%M%S", time.localtime())
        # 指定要转换的Excel文件路径
        file_path = args.file_path
        sheet_name = args.sheet_name
        system_file_path = args.system_file_path
        system_file_sheet_name = args.system_file_sheet_name
        regular_file_path = args.regular_file_path
        regular_file_sheet_name = args.regular_file_sheet_name
        generate_path = args.generate_path
        today = datetime.datetime.now().strftime('%m%d')
        generate_path = os.path.join(
            generate_path, f"xitong_check_out_{today}")
        # 创建运行审计目录
        audit_path = os.path.join(generate_path, f"audit/{gen_time}")
        # 如果路径不存在则创建
        if not os.path.exists(audit_path):
            os.makedirs(audit_path)
        # 设置日志记录
        setup_logging(
            log_file=rf"{audit_path}/{gen_time}_result.log", debug=True)
        logger = logging.getLogger(__name__)
        # 读取配置文件
        config_file = args.config_file
        if config_file != "settings.yaml":
            if not os.path.isabs(config_file):
                config_file = os.path.join(os.getcwd(), config_file)
            if not os.path.exists(config_file):
                logger.error(
                    f"指定的配置文件 {config_file} 不存在，请检查路径和文件名是否正确。", exc_info=True)
                exit(1)
            # 如果用户指定了配置文件，则使用用户指定的配置文件
            logger.info(f"使用指定配置文件：{config_file}")
            with open(config_file, 'r', encoding='utf-8') as file:
                config = yaml.safe_load(file)
        else:
            logger.info(f"使用默认配置文件：{config_file}")
            with open(f'{os.path.dirname(__file__)}/{config_file}', 'r', encoding='utf-8') as file:
                config = yaml.safe_load(file)
        # 将运行文件复制入审计目录，以便复现排查问题
        # shutil.copyfile(file_path, f"{audit_path}/{os.path.basename(file_path)}")
        # logger.info(f"开始转换文件：{file_path}，sheet名称：{sheet_name}，生成结果保存路径：{generate_path}，转换时间：{gen_time}")
        # 创建运行结果保存文件夹
        create_folder(generate_path)
        # 解析原始文件
        # 云宏源文件
        # origin_df = parse_origin_file(file_path, sheet_name, config['required_columns'])
        # 系统编码表
        # system_df = parse_origin_file(system_file_path, system_file_sheet_name, [], skiprows=1)
        regular_df = parse_origin_file(
            regular_file_path, regular_file_sheet_name, [])
        check_data = {
            # 数据库实例
            "*************:30000": ["GZXT-JS-N-027-027275", "市财政局"],
            "*************:3306": ["SJK_GZXT-JS-N-043-000528", "市体育局"],
            "************:27017": ["SJK_GZXT-JS-W-038-000509", "市退役军人事务局"],
            "************:5236": ["SJK_GZXT-JS-W-038-000509", "市退役军人事务局"],
            "************:5236": ["SJK_GZXT-JS-W-038-000509", "市退役军人事务局"],
            "*************:5236": ["SJK_GZXT-JS-N-028-028193", "市人力资源社会保障局"],
            "************:5237": ["SJK_GZXT-JS-N-038-000513", "市退役军人事务局"],
            "************:5238": ["SJK_GZXT-JS-N-038-000513", "市退役军人事务局"],
            "************:5239": ["SJK_GZXT-JS-N-038-000513", "市退役军人事务局"],
            "************:5240": ["SJK_GZXT-JS-N-038-000513", "市退役军人事务局"],
            "************:5241": ["SJK_GZXT-JS-N-038-000513", "市退役军人事务局"],
            "************:5242": ["SJK_GZXT-JS-N-038-000513", "市退役军人事务局"],
            "10.194.180.238:1521": ["SJK_GZXT-JS-N-025-025933", "市民政局"],
            "10.194.180.239:1521": ["SJK_GZXT-JS-N-025-025933", "市民政局"],
            "10.194.181.61:5236": ["SJK_GZXT-JS-N-025-000289", "市民政局"],
            "10.194.181.62:5236": ["SJK_GZXT-JS-N-025-000289", "市民政局"],
            "10.197.4.35:15025": ["SJK_GZXT-JS-N-032-000353 ", "市交通运输局"],
            "10.194.103.128:3306": ["SJK_GZXT-JS-N-032-000023", "市交通运输局"],
            "10.194.103.129:3306": ["SJK_GZXT-JS-N-032-000023", "市交通运输局"],
            "10.194.100.65:3306": ["SJK_GZXT-JS-N-032-000025", "市交通运输局"],
            "10.201.74.4:5236": ["SJK_GZXT-JS-N-032-003117", "市交通运输局"],
            "10.201.74.2:5236": ["SJK_GZXT-JS-N-032-003117", "市交通运输局"],
            "10.201.26.13:5236": ["SJK_GZXT-JS-N-044-004418", "市统计局"],
            "10.201.26.13:6001": ["SJK_GZXT-JS-N-044-004418", "市统计局"],
            "10.201.26.13:6002": ["SJK_GZXT-JS-N-044-004418", "市统计局"],
            "10.201.26.13:6003": ["SJK_GZXT-JS-N-044-004418", "市统计局"],
            "10.196.131.39:5238": ["SJK_GZXT-JS-W-028-028147", "市人力资源社会保障局"],
            "10.196.131.40:5238": ["SJK_GZXT-JS-W-028-028147", "市人力资源社会保障局"],
            "10.205.17.100:5236": ["SJK_GZXT-JS-N-037-037208", "市卫生健康委"],
            "10.196.192.64:5432": ["SJK_GZXT-JS-N-032-003257", "市交通运输局"],
            "172.16.33.118:1521": ["SJK_GZXT-JS-N-032-000361", "市交通运输局"],
            # 2025.7.21 更新
            "10.197.125.27:5432": ["SJK_GZXT-JS-N-032-003036", "市交通运输局"],
            "10.197.125.28:5432": ["SJK_GZXT-JS-N-032-003036", "市交通运输局"],
            # 2025.7.22 更新
            "*************:6661": ["SJK_GZXT-JS-N-032-000027", "市交通运输局"],
            "*************:1521": ["SJK_GZXT-JS-N-042-002405", "市市场监管局"],
            # 2025.8.12 更新
            "*************:5236": ["SJK_GZXT-JS-N-047-003009", "市城市管理综合执法局"],
            "*************:5236": ["SJK_GZXT-JS-N-047-003009", "市城市管理综合执法局"],

        }
        # 获取 uniprobe 列表，只保留 "-" 前的 ip
        with UniserverApiManager() as uniserver_api:
            uniprobe_ip_list = get_uniprobe_node_name_list(uniserver_api)
        uniprobe_ip_list = [ip.split("-")[0] for ip in uniprobe_ip_list]
        # system_list = list(set(origin_df["系统名称"].values.tolist()))
        for index, data in regular_df.iterrows():
            # 先将固化数据写入
            if pd.isna(data.端口) or pd.isna(data.主机用途) or pd.isna(data.IP地址) or "," in str(data.IP地址):
                print(f"固化数据跳过{index}行数据")
                continue
            if not isinstance(data.端口, (int, float)) and "、" in str(data.端口):
                print(f"固化数据跳过{index}行数据")
                continue
            # 新增端口值验证
            port_value = data.端口
            if not isinstance(port_value, (int, float)):
                try:
                    port_value = int(port_value)
                except ValueError:
                    print(f"端口值 {port_value} 不是有效数字，跳过{index}行数据")
                    continue
            # 判断匹配逻辑
            ip_to_check = None
            if pd.isna(data.私网IP):
                # 只匹配 IP地址
                ip_to_check = str(data.IP地址).strip()
            else:
                # 两个都存在，优先匹配私网IP
                if str(data.私网IP).strip() in uniprobe_ip_list:
                    ip_to_check = str(data.私网IP).strip()
                elif str(data.IP地址).strip() in uniprobe_ip_list:
                    ip_to_check = str(data.IP地址).strip()
                else:
                    ip_to_check = str(data.IP地址).strip()
            if not ip_to_check:
                logger.debug(
                    f"未命中 uniprobe_ip_list，跳过{index}行数据，IP候选: {[str(data.IP地址).strip(), str(data.私网IP).strip() if not pd.isna(data.私网IP) else None]}")
                continue
            ip_port = f"{ip_to_check}:{int(data.端口)}"
            if str(data.主机用途).lower() in ["web服务器", "数据库服务器"]:
                system_code = data.系统编号
                system_unit = data.所属一级单位
                check_data[ip_port] = [
                    "SJK_" + system_code if data.主机用途 == "数据库服务器" else system_code,
                    system_unit
                ]
        # for system in system_list:
        #     # 逐个系统匹配web服务器和数据库服务器的端口
        #     system_data_df = origin_df[origin_df["系统名称"] == system]
        #     for index,data in system_data_df.iterrows():
        #         if pd.isna(data.端口) or pd.isna(data.主机用途) or pd.isna(data.IP地址) or "," in data.IP地址:
        #             continue
        #         if not isinstance(data.端口, int) and not isinstance(data.端口, float) and "、" in data.端口:
        #             continue
        #         ip = data.IP地址 if pd.isna(data.私网IP) else data.私网IP
        #         ip_port = f"{ip}:{int(data.端口)}"
        #         system_name = data.系统名称
        #         system_unit = data.所属一级单位
        #         if data.主机用途 in ["Web服务器","数据库服务器"]:
        #             if ip_port in check_data:
        #                 logger.warning(f"发现重复的IP地址-端口组合：{ip_port}，系统名称:{system}，跳过处理")
        #                 continue
        #             system_data = system_df[(system_df["信息系统名称"] == system_name) & (system_df["一级单位"] == system_unit)]
        #             if len(system_data) > 1:
        #                 logger.warning(f"{ip_port}-{system_name}-{system_unit}对应多个系统编码，默认使用第一个")
        #                 # continue
        #             if len(system_data) == 0:
        #                 logger.warning(f"{ip_port}-{system_name}-{system_unit}没有对应系统编码，请检查数据，跳过处理")
        #                 continue
        #             system_code = system_data.系统编号.values.tolist()[0]
        #             system_unit = system_data.一级单位.values.tolist()[0]
        #             check_data[ip_port] = ["SJK_"+system_code if data.主机用途 == "数据库服务器" else system_code,system_unit]
        output_path = os.path.join(
            generate_path, config["output_config"]['filename'])
        # 保存处理后的数据
        save_to_csv(check_data, output_path, config["output_config"])
        logger.info(f"转换完成，运行日志存放于{audit_path}")
    except Exception as e:
        logger.error(
            f"程序运行过程中发生错误：{str(e)}\n请到{audit_path}审查日志", exc_info=True)
        exit(1)
