import requests
import base64
import pandas as pd
import argparse
import os
import datetime

# 配置信息
# base_url = "http://**************:9000/"
# base_url = "http://************:9000/"
base_url = "http://**************:9000"

username = "admin"
password = "Aidynamic@2025"
headers = {
    "Accept": "application/json",
    "Accept-Language": "zh-CN,zh;q=0.9",
    "Content-Type": "application/json",
    "X-Requested-By": "XMLHttpRequest",
    "X-Requested-With": "XMLHttpRequest",
}

# 网络字典：IP与网络类型的映射关系
network_dict = {
    "**************": "互联网",
    "***************": "政务内网",
    "**************": "政务外网"
}

"""
获取token
@return: token
@rtype: str

"""


def get_token():
    """获取访问token"""
    # 这里需要根据实际情况实现token获取逻辑
    # 假设通过某个登录接口获取token
    # 以下是示例代码，需要根据实际API进行修改
    login_url = f"{base_url}/api/system/sessions"
    credentials = {
        "username": f"{username}",  # 替换为实际用户名
        "password": f"{password}",  # 替换为实际密码
        "host": f"{base_url}",
    }

    try:
        response = requests.post(login_url, headers=headers, json=credentials)
        response.raise_for_status()
        # 打印完整响应，检查返回结构
        print("Login response:", response.text)
        session_id = response.json().get("session_id")
        if not session_id:
            print("未在响应中找到session_id")
            return None
        token = base64.b64encode(f"{session_id}:session".encode("utf-8")).decode(
            "utf-8"
        )
        # print("session_id is :", session_id)
        print("token is :", token)
        return token
    except Exception as e:
        print(f"获取 session_id 失败: {e}")
        # 打印错误响应内容
        if "response" in locals():
            print("Error response:", response.text)
        return None


"""
获取uniprobe列表
@param token: token
@type token: str
@return: uniprobe列表
@rtype: list

"""


def get_uniprobe_list(token):
    """使用token获取uniprobe列表"""
    if not token:
        print("token为空，无法获取uniprobe列表")
        return []

    api_path = "/api/sidecars?query=&page=1&per_page=10000&only_active=false&sort=last_seen&order=asc"
    url = f"{base_url}{api_path}"
    # 复制全局headers并添加认证信息
    request_headers = headers.copy()
    # 尝试不同的认证格式
    request_headers["Authorization"] = f"Basic {token}"
    # print("url is :", url)
    # print("headers is :", request_headers)

    try:
        response = requests.get(url, headers=request_headers)
        # print("Response status code:", response.status_code)
        # print("Response headers:", response.headers)
        # print("Response content:", response.text)
        response.raise_for_status()
        return response.json()
    except Exception as e:
        print(f"获取uniprobe列表失败: {e}")
        return []


"""
从uniprobe列表中提取IP地址
@param uniprobe_list: uniprobe列表
@type uniprobe_list: list
@return: IP地址列表
@rtype: list

"""


def extract_ip_addresses(uniprobe_list):
    """从uniprobe列表中提取IP地址"""
    ip_addresses = []

    for item in uniprobe_list["sidecars"]:
        ip_host = item.get("node_name")
        # 根据"-"分隔node_name并取第一位作为IP
        parts = ip_host.split("-")
        if parts:
            ip = parts[0]
            ip_addresses.append(ip)
    return ip_addresses


"""
保存结果到CSV文件
@param results: 结果列表
@type results: list
@param output_path: 输出文件路径
@type output_path: str
@param output_filename: 输出文件名
@type output_filename: str
@param columns: 列名列表
@type columns: list
@param delimiter: 分隔符
@type delimiter: str

"""


def save_results_to_csv(results, output_path, output_filename, columns, delimiter="@"):
    """将结果保存为CSV文件，并在每行末尾添加@"""
    # 创建结果DataFrame
    result_df = pd.DataFrame(
        results, columns=columns
    )

    # 确保输出目录存在
    if not os.path.exists(output_path):
        os.makedirs(output_path)

    # 构建输出文件路径
    today = datetime.datetime.now().strftime('%m%d%H%M%S')
    output_path = os.path.join(output_path, today, output_filename)
    os.makedirs(os.path.dirname(output_path), exist_ok=True)

    # 保存为CSV，设置引号和分隔符
    result_df.to_csv(
        output_path,
        index=False,
        encoding="utf-8-sig",
        sep=",",
        quoting=1,  # 1表示QUOTE_ALL，所有字段都加引号
        quotechar='"',
        header=None,
    )

    # 读取 CSV 文件，为每一行末尾添加行记录分隔符
    # 先读取文件内容
    with open(output_path, "r", encoding="utf-8-sig") as file:
        lines = file.readlines()

    # 再写入修改后的内容
    with open(output_path, "w", encoding="utf-8-sig") as file:
        for line in lines:
            file.write(line.rstrip() + f",{delimiter}\n")

    print(f"匹配结果已保存到 {output_path}")


"""
处理数据并生成主机CSV文件
@param regular_path: 固化数据文件路径
@type regular_path: str
@param output_path: 输出文件路径
@type output_path: str
@param output_filename: 输出文件名
@type output_filename: str
@param token: 认证token
@type token: str
@param uniprobe_list: uniprobe列表
@type uniprobe_list: list

"""


def process_and_generate_zhuji_csv(
    regular_df, output_path, output_filename, token, uniprobe_list
):
    """处理数据并生成主机CSV文件"""
    # 检查Excel文件是否存在
    if regular_df is None:
        print("错误: 传入的固化DataFrame为空")
        return

    # 获取传入的token
    if not token:
        print("token为空，无法获取探针列表")
        return

    # 获取uniprobe列表
    if not uniprobe_list:
        print("探针列表为空")
        return

    # 提取IP地址
    # ip_addresses = extract_ip_addresses(uniprobe_list)

    # 排序
    # sort_ips = sorted(ip_addresses)

    # 打印探针列表记录数
    print(f"已上线探针数量: {len(uniprobe_list['sidecars'])}")

    # 匹配IP并构建结果
    results = []
    # 存储未匹配到系统的IP
    no_match_ip_list = []

    for item in uniprobe_list["sidecars"]:
        ip_host = item.get("node_name")
        ip = ip_host.split("-")[0]
        server_ip = item.get("node_details", {}).get("ip", "")
        matched = False  # 新增：标记是否匹配到系统

        ip_match = (regular_df["私网IP"] == ip) | (regular_df["IP地址"] == ip)
        if ip_match.any():
            matched = True
            # 从固化数据里获取匹配的 ip 数据
            ip_data = regular_df[ip_match]
            # 获取系统名称
            system_name = ip_data.iloc[0].get("系统名称", "")
            # 获取系统编号
            system_code = ip_data.iloc[0].get("系统编号", "")
            # 获取一级单位
            system_unit = ip_data.iloc[0].get("所属一级单位", "")
            # 获取网络类型
            network_type = network_dict.get(server_ip, "")
            results.append(
                [ip_host, system_code, system_unit, network_type, system_name,0]
            )
            continue

        # 获取网络类型
        network_type = network_dict.get(server_ip, "")

        # 没有匹配到，记录未匹配的IP
        if not matched:
            no_match_ip_list.append(ip)

    # 打印未匹配到系统的IP个数
    print(f"未匹配到系统的探针个数: {len(no_match_ip_list)}")

    # 将未匹配到系统的IP输出到CSV
    if no_match_ip_list:
        # 创建no_match_system目录
        no_match_dir = os.path.join(output_path, "no_match_system")
        os.makedirs(no_match_dir, exist_ok=True)
        no_match_output_path = os.path.join(
            no_match_dir, "no_match_system.csv")
        with open(no_match_output_path, "w", encoding="utf-8-sig") as f:
            for ip in no_match_ip_list:
                f.write(f"{ip}\n")
        print(f"未匹配到系统的探针已保存到 {no_match_output_path}")

    # 调用函数保存结果
    zhuji_columns = ["IP地址", "所属一级单位", "系统编号", "接入网络类型","编目数量", "系统名称"]
    save_results_to_csv(
        results, output_path, output_filename, zhuji_columns, delimiter="@"
    )

"""
处理数据并生成系统CSV文件
@param regular_path: 固化数据文件路径
@type regular_path: str
@param output_path: 输出文件路径
@type output_path: str
@param output_filename: 输出文件名
@type output_filename: str
@param token: 认证token
@type token: str
@param uniprobe_list: uniprobe列表
@type uniprobe_list: list


"""


def process_and_generate_xitong_csv(
        regular_df, output_path, output_filename, token, uniprobe_list
):
    """处理数据并生成系统CSV文件"""
    # 检查Excel文件是否存在
    if regular_df is None:
        print("错误: 传入的固化DataFrame为空")
        return

    # 确保Excel中有必要的列
    required_columns = [
        "IP地址",
        "私网IP",
        "端口",
        "系统名称",
        "系统编号",
        "所属一级单位",
        "主机用途",
    ]
    missing_columns = [
        col for col in required_columns if col not in regular_df.columns
    ]
    if missing_columns:
        print(f"错误: Excel文件中缺少必要的列: {', '.join(missing_columns)}")
        return

    # 构建探针IP集合（从node_name中提取的IP）
    probe_ip_set = set()
    for item in uniprobe_list["sidecars"]:
        ip_host = item.get("node_name")
        if ip_host:
            ip = ip_host.split("-")[0]
            probe_ip_set.add(ip)

    # 遍历Excel每一行
    results = []
    seen_records = set()  # 用于跟踪已处理的记录，避免重复

    for idx, row in regular_df.iterrows():
        ip_public = str(row["IP地址"]) if pd.notna(row["IP地址"]) else ""
        ip_private = str(row["私网IP"]) if pd.notna(row["私网IP"]) else ""
        port = row["端口"]
        system_name = str(row["系统名称"]) if pd.notna(row["系统名称"]) else ""
        system_code = str(row["系统编号"]) if pd.notna(row["系统编号"]) else ""
        system_unit = str(row["所属一级单位"]) if pd.notna(row["所属一级单位"]) else ""
        host_usage = str(row["主机用途"]) if pd.notna(row["主机用途"]) else ""

        # 检查端口是否有效
        if pd.isna(port) or str(port).lower() == "nan" or port == "":
            continue  # 跳过无效端口行

        # 确定使用的IP地址
        use_ip = None
        if ip_public in probe_ip_set:
            use_ip = ip_public
        elif ip_private in probe_ip_set:
            use_ip = ip_private

        # 如果IP不在探针列表中，跳过
        if not use_ip:
            continue

        # 处理端口格式（转换为整数）
        try:
            port = int(float(port))
        except (ValueError, TypeError):
            port = str(port).strip()  # 保留原始值

        # 构建实例名称
        ip_port = f"{use_ip}:{port}" if use_ip and port else ""

        # 根据主机用途添加系统编号前缀

        if host_usage == "数据库服务器":
            system_code = f"SJK_{system_code}"
        elif host_usage == "Web服务器":
            system_code = f"{system_code}"
        else:
            system_code = f"QT_{system_code}"
        # 创建记录元组
        record = (ip_port, system_name, system_code, system_unit)

        # 检查记录是否已存在
        if record in seen_records:
            print(f"跳过重复记录: {record}")
            continue

        # 添加记录并标记为已处理
        results.append(list(record))
        seen_records.add(record)

    # 打印处理统计
    print(f"成功处理 {len(results)} 条系统记录")
    print(f"跳过 {len(regular_df) - len(results)} 条重复记录")
    # 调用函数保存结果
    xitong_columns = ["实例名称", "系统名称", "所属一级单位", "系统编号"]
    save_results_to_csv(
        results, output_path, output_filename, xitong_columns, delimiter="@"
    )

"""
主函数
"""


def main():
    # 解析命令行参数
    parser = argparse.ArgumentParser(description="获取uniprobe列表并与Excel数据匹配")
    parser.add_argument(
        "--input",
        required=False,
        dest="regular_path",
        help="固化数据文件路径",
        default="..\\[白云政数局]数据固化\\固化数据_BY.xlsx",
    )
    parser.add_argument(
        "--output",
        type=str,
        dest="output_path",
        help='生成的CSV路径',
        default="..\\[白云政数局]数据固化\\result\\2025\\ZhujiCSV\\",
    )
    args = parser.parse_args()
    regular_path = args.regular_path
    output_path = args.output_path

    # 获取token
    token = get_token()
    if not token:
        print("获取登录token失败，程序退出")
        return

    # 获取uniprobe列表
    uniprobe_list = get_uniprobe_list(token)
    if not uniprobe_list:
        return

    # 读取固化数据
    try:
        regular_df = pd.read_excel(regular_path)
        print(f"成功读取固化数据，共有 {len(regular_df)} 条记录")
    except Exception as e:
        print(f"读取固化数据失败: {e}")
        return

    # 确保Excel中有'IP地址'和'私网IP'列
    if "IP地址" not in regular_df.columns and "私网IP" not in regular_df.columns:
        print("错误: 固化数据文件中没有找到'IP地址'或'私网IP'列")
        return

    # 转换为字符串类型
    regular_df["IP地址"] = regular_df["IP地址"].astype(str)
    regular_df["私网IP"] = regular_df["私网IP"].astype(str)
    regular_df["端口"] = regular_df["端口"].astype(str)

    # 删除空行
    regular_df.dropna(how="all", inplace=True)

    # 调用处理函数时传递token
    process_and_generate_zhuji_csv(
        regular_df, output_path, "zhuji.csv", token, uniprobe_list
    )
    process_and_generate_xitong_csv(
        regular_df, output_path, "xitong.csv", token, uniprobe_list
    )


if __name__ == "__main__":
    main()
