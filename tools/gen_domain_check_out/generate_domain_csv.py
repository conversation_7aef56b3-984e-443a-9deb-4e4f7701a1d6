import pandas as pd
import argparse
import os
import csv
import datetime


def generate_domain_csv(input_excel, output_csv):
    """
    从合并后的Excel文件生成指定格式的CSV文件，并按访问域名去重

    参数:
    input_excel: 合并后的Excel文件路径
    output_csv: 生成的CSV文件路径
    """
    try:
        # 读取Excel文件
        df = pd.read_excel(input_excel, engine="openpyxl")
        print(f"成功读取Excel文件: {input_excel}")
        print(f"原始数据列: {', '.join(df.columns)}")
        print(f"原始记录数: {len(df)}")
    except Exception as e:
        print(f"读取Excel文件时出错: {str(e)}")
        return

    # 检查必要的列是否存在
    required_columns = ["访问域名", "系统编号", "一级单位"]
    missing_columns = [col for col in required_columns if col not in df.columns]
    if missing_columns:
        print(f"错误: 缺少必要的列: {', '.join(missing_columns)}")
        return

    # 创建新的数据框，包含所需的列
    # 第三列使用'一级单位'作为单位名称
    new_df = pd.DataFrame(
        {
            "访问域名": df["访问域名"],
            "系统编号": df["系统编号"],
            "单位名称": df["一级单位"]
        }
    )

    # 按访问域名去重，保留第一条记录
    original_count = len(new_df)
    new_df = new_df.drop_duplicates(subset=["访问域名"], keep="first")
    deduplicated_count = len(new_df)
    print(f"去重前记录数: {original_count}")
    print(f"去重后记录数: {deduplicated_count}")
    print(f"去除重复记录数: {original_count - deduplicated_count}")

    try:
        # 保存为CSV文件，设置引号和分隔符
        # 使用csv.QUOTE_ALL确保所有字段都被双引号包围
        new_df.to_csv(
            output_csv,
            index=False,
            sep=",",
            quoting=csv.QUOTE_ALL,
            encoding="utf-8-sig",
            header=False,
        )
        print(f"CSV文件已成功生成: {output_csv}")
    except Exception as e:
        print(f"保存CSV文件时出错: {str(e)}")
        return


def main():
    # 设置命令行参数
    parser = argparse.ArgumentParser(
        description="从合并后的Excel文件生成指定格式的CSV文件"
    )
    parser.add_argument(
        "--input",
        type=str,
        required=True,
        default="D:\\Working\\ZhangCe\\项目材料\\广州市政数局\\系统效能评估\\生产环境探针测试&核对\\固化csv\\数据固化（K8S）\\固话数据_K8S.xlsx",
        help="合并后的Excel文件路径",
    )
    parser.add_argument(
        "--output",
        type=str,
        default="D:\\Working\\ZhangCe\\项目材料\\广州市政数局\\系统效能评估\\运维知识库\\Python生成csv\\result\\2025\\DomainCSV\\",
        help='生成的CSV路径，默认在当前目录下创建以当前日期命名的文件夹',
    )

    args = parser.parse_args()
    input_path = args.input
    output_path = args.output

    # 检查输入文件是否存在
    if not os.path.exists(input_path):
        print(f"错误: 输入文件不存在: {input_path}")
        return

    today = datetime.datetime.now().strftime('%m%d')
    output_path = os.path.join(output_path, today, "domain.csv")
    os.makedirs(os.path.dirname(output_path), exist_ok=True)


    # 调用生成函数
    generate_domain_csv(input_path, output_path)

    # 读取生成的CSV文件并添加分隔符
    with open(output_path, 'r', encoding='utf-8-sig') as file:
        lines = file.readlines()

    # 写入修改后的内容
    with open(output_path, 'w', encoding='utf-8-sig') as file:
        for line in lines:
            file.write(line.rstrip() + ',@\n')


if __name__ == "__main__":
    main()
