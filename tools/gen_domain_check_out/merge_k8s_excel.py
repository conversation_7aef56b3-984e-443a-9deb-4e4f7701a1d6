import os
import pandas as pd
import argparse
from openpyxl import load_workbook


def merge_k8s_excel_files(input_dir, output_file):
    """
    合并指定目录下所有以"K8S信创"开头的.xlsx和.xls文件

    参数:
    input_dir: 要搜索Excel文件的目录
    output_file: 合并后的输出文件路径
    """
    # 定义目标列名顺序P
    target_columns = [
        "一级单位",
        "部门中心",
        "系统编号",
        "系统名称",
        "访问域名",
        "访问IP",
        "端口",
        "备注",
    ]

    # 存储所有数据的数据框列表
    dfs = []

    # 遍历目录下的所有文件
    for filename in os.listdir(input_dir):
        # 检查文件名是否符合条件
        if filename.startswith("K8S信创") and filename.endswith((".xlsx", ".xls")):
            file_path = os.path.join(input_dir, filename)
            print(f"正在处理文件: {file_path}")

            try:
                # 读取Excel文件
                if filename.endswith(".xlsx"):
                    df = pd.read_excel(file_path, engine="openpyxl")
                else:  # .xls文件
                    df = pd.read_excel(file_path, engine="xlrd")

                # 检查列是否匹配
                missing_columns = [
                    col for col in target_columns if col not in df.columns
                ]
                if missing_columns:
                    print(
                        f"警告: 文件 {filename} 缺少以下列: {', '.join(missing_columns)}"
                    )
                    print("将跳过此文件")
                    continue

                # 确保列的顺序正确
                df = df[target_columns]

                # 添加到数据框列表
                dfs.append(df)

            except Exception as e:
                print(f"处理文件 {filename} 时出错: {str(e)}")
                continue

    if not dfs:
        print("没有找到符合条件的Excel文件或所有文件都有错误")
        return

    # 合并所有数据框
    merged_df = pd.concat(dfs, ignore_index=True)

    # 保存合并后的结果
    try:
        merged_df.to_excel(output_file, index=False, engine="openpyxl")
        print(f"合并完成！已保存到: {output_file}")
        print(f"总记录数: {len(merged_df)}")
    except Exception as e:
        print(f"保存合并文件时出错: {str(e)}")


def main():
    # 设置命令行参数
    parser = argparse.ArgumentParser(description="合并指定目录下的K8S信创Excel文件")
    # parser.add_argument('--input_dir', type=str, default='.',
    #                     help='要搜索Excel文件的目录，默认为当前目录')
    parser.add_argument(
        "--input_dir",
        type=str,
        default="D:\\Working\\ZhangCe\\项目材料\\广州市政数局\\系统效能评估\\生产环境探针测试&核对\\固化csv\\数据固化（K8S）",
        help="要搜索Excel文件的目录，默认为当前目录",
    )
    # parser.add_argument('--output', type=str, default='合并后的K8S信创文件.xlsx',
    #                     help='合并后的输出文件名，默认为"合并后的K8S信创文件.xlsx"')
    parser.add_argument(
        "--output",
        type=str,
        default="D:\\Working\\ZhangCe\\项目材料\\广州市政数局\\系统效能评估\\生产环境探针测试&核对\\固化csv\\数据固化（K8S）\\固化数据_K8S.xlsx",
        help='合并后的输出文件名，默认为"合并后的K8S信创文件.xlsx"',
    )

    args = parser.parse_args()

    # 调用合并函数
    merge_k8s_excel_files(args.input_dir, args.output)


if __name__ == "__main__":
    main()
