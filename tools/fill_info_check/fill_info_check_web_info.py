import os
import sys
import time
import shutil
import logging
import argparse
import pandas as pd
import ipaddress

class ArchDataKEY:
    """
    归档数据键类，用于合并映射键
    """
    def __init__(self,**group_dict):
        for k,v in group_dict.items():
            setattr(self,k,v)

    def __hash__(self):
        # 多同类键值不同顺序的组合字段重排，形成哈希去重
        return hash('|'.join(reversed(list(self.__dict__.values()))))

    def __eq__(self, other):
        other_values = other.__dict__.values()
        self_values = self.__dict__.values()
        if len(other_values) != len(self_values):
            return False
        if len(list(set(other_values) - set(self_values))) > 0:
            # 值不一样
            return False
        return True

    def __str__(self):
        return f"{'|'.join([str(v) for k, v in self.__dict__.items()])}"

def is_valid_ip(ip_str):
    try:
        ipaddress.ip_address(ip_str)
        return True
    except ValueError:
        return False

import re

def extract_ip_ports(text):

    # 定义正则表达式匹配IPv4地址和端口
    pattern = r'(\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3})[:：](\d{1,5})'
    matches = re.findall(pattern, text)
    
    results = []
    for ip, port in matches:
        # 验证IP地址每个部分是否合法（0-255）
        ip_parts = ip.split('.')
        valid_ip = True
        for part in ip_parts:
            if not 0 <= int(part) <= 255:
                valid_ip = False
                break
        
        # 验证端口是否合法（0-65535）
        valid_port = 0 <= int(port) <= 65535
        
        # 如果IP和端口都合法，则组合成"ip:端口"格式
        if valid_ip and valid_port:
            # 保留原始文本中的冒号（通过重新查找原始格式）
            original_format = re.search(re.escape(ip) + r'[:：]' + re.escape(port), text)
            if original_format:
                results.append(original_format.group(0))
    if len(results) == 0:
        # 如果IPv4地址和端口没有匹配出结果，则匹配https://IPv4地址或http://IPv4地址
        pattern = r'(https?://)?(\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3})'
        matches = re.findall(pattern, text)
        
        for protocol, ip in matches:
            ip_parts = ip.split('.')
            valid_ip = True
            for part in ip_parts:
                if not 0 <= int(part) <= 255:
                    valid_ip = False
                    break
            if valid_ip and protocol:
                if protocol == "http://":
                    # http时端口默认为80
                    results.append(f"{ip}:{80}")
                if protocol == "https://":
                    # http时端口默认为80
                    results.append(f"{ip}:{443}")

    return results

def setup_logging(log_file=None, debug=False):
    """
    设置日志器\n
    :param log_file: 日志文件路径，如果为None则不写入文件\n
    :param debug: 是否开启调试模式，调试模式下日志级别为DEBUG，否则为INFO\n
    :return: None
    """
    handlers = []
    console_handler = logging.StreamHandler(sys.stderr)
    handlers.append(console_handler)

    if log_file:
        file_handler = logging.FileHandler(log_file)
        handlers.append(file_handler)

    log_format = "%(asctime)s [%(levelname)s] %(name)s: %(message)s" if debug else "%(levelname)s: %(message)s"
    
    logging.basicConfig(
        level=logging.DEBUG if debug else logging.INFO,
        format=log_format,
        handlers=handlers,
        #encoding='utf-8'
    )

def create_folder(folder_path):
    """
    创建文件夹，如果文件夹已存在则不做任何操作\n
    :param folder_path: 文件夹路径\n
    """
    try:
        os.makedirs(folder_path, exist_ok=True)
        logger.info(f"文件夹 {folder_path} 创建成功或已存在。")
    except Exception as e:
        logger.error(f"创建文件夹时发生错误：{str(e)}", exc_info=True)
        raise Exception(f"创建文件夹 {folder_path} 时发生错误：{str(e)}")

def parse_args():

    parser = argparse.ArgumentParser()
    # 配置入参关键字
    parser.add_argument(

        "-yf",

        "--yunhong_file_path",

        help = "云宏文档路径",

        dest = "yunhong_file_path",

        type = str,

        required = True

    )

    parser.add_argument(

        "-ys",

        "--yunhong_sheet_name",

        help = "云宏文档sheet名称(默认为【系统清单（638）】)",

        dest = "yunhong_sheet_name",

        type = str,

        default = "系统清单（638）"

    )

    parser.add_argument(

        "-xf",

        "--xiangmu_file_path",

        help = "项目管理系统文档路径",

        dest = "xiangmu_file_path",

        type = str,

        required = True

    )

    parser.add_argument(

        "-xs",

        "--xiangmu_sheet_name",

        help = "项目管理系统文档sheet名称(默认为【筛选仅参选系统】)",

        dest = "xiangmu_sheet_name",

        type = str,

        default = "筛选仅参选系统"

    )

    parser.add_argument(

        "-g",

        "--generate_path",

        help = "生成结果保存路径(默认当前目录)",

        dest = "generate_path",

        type = str,

        default = os.getcwd()

    )
    
    parser.add_argument(

        "-cf",

        "--config_file",

        help="指定配置文件(默认为【settings.yaml】)",

        dest="config_file",

        type=str,

        default='settings.yaml'

    )
    args = parser.parse_args()

    return args, parser

def parse_origin_file(origin_file_path,sheet_name,required_columns):
    """
    通过Pandas解析原始文件内容\n
    :param origin_file_path: 原始Excel文件路径\n
    :param sheet_name: 要解析的sheet名称\n
    :return: DataFrame对象\n
    """
    try:
        logger.info(f"开始解析文件：{origin_file_path}，sheet名称：{sheet_name}")
        # 读取Excel文件，可根据需要调整sheet_name参数
        df = pd.read_excel(origin_file_path, engine='openpyxl', sheet_name=sheet_name)
        df.dropna(how='all', inplace=True)  # 删除全为空值的行
        # 检查是否包含必要的列
        for col in required_columns:
            if col not in df.columns:
                logger.error(f"错误：{os.path.basename(origin_file_path)}文档中缺少必要的列：{col}，请检查文档格式。", exc_info=True)
                raise ValueError(f"缺少必要的列：{col}")
        return df
    except FileNotFoundError:
        logger.error(f"错误：未找到{origin_file_path}文件，请检查路径和文件名是否正确。",exc_info=True)
        raise FileNotFoundError(f"未找到文件：{origin_file_path}")
    except Exception as e:
        logger.error(f"读取文档{origin_file_path}过程中发生错误：{str(e)}",exc_info=True)
        raise Exception(f"读取文档{origin_file_path}时发生错误：{str(e)}")

def get_no_web_system(df) -> list:
    """
    从云宏表筛出没有web服务器的系统名单
    :param df: DataFrame对象
    """
    logger.info("开始从云宏表筛出没有web服务器的系统名单")
    # [{"所属一级单位":"","系统填报单位":"","系统名称":""}]
    no_web_system = {"没有web服务器的系统名单":[]}
    # 筛出所有web服务器对应的系统
    web_system_list = df[(df["主机用途"] == "Web服务器") | (df["主机用途"].isna())]
    web_system = list(set(list(web_system_list["系统名称"].values.tolist())))
    # 反筛得出没有web服务器的系统
    no_web_system["没有web服务器的系统名单"] = df[~df["系统名称"].isin(web_system)].values.tolist()
    no_web_system["没有web服务器的系统名单"].insert(0,list(df.head()))
    return no_web_system

def get_no_web_system_info(no_web_system,xiangmu_df) -> list:
    """
    从项目管理系统匹配没有web服务器的系统的范围吧地址信息
    :param no_web_system: 没有web服务器的系统名单
    :param xiangmu_df: 项目管理系统数据
    """
    logger.info("开始匹配没有web服务器的系统的访问地址")
    # 还原没有web服务器的系统名单df
    no_web_system_df = pd.DataFrame(no_web_system["没有web服务器的系统名单"][1:],columns=no_web_system["没有web服务器的系统名单"][0])
    # 从项目管理系统表匹配没有web服务器的系统的信息
    xiangmu_no_web_system_df = xiangmu_df[xiangmu_df["信息系统名称"].isin(list(no_web_system_df["系统名称"].values.tolist()))]
    no_web_system["项目管理系统匹配没有web服务器的系统名单的结果"] = xiangmu_no_web_system_df.values.tolist()
    no_web_system["项目管理系统匹配没有web服务器的系统名单的结果"].insert(0,list(xiangmu_no_web_system_df.head()))
    no_web_system["项目管理系统匹配没有web服务器的系统名单填写不及格名单"] = []
    no_web_system["项目管理系统匹配没有web服务器的系统名单填写及格名单"] = []
    for index, row in xiangmu_no_web_system_df.iterrows():
        # 从【系统访问入口】字段中提取IP地址和端口
        entrance = str(row.系统访问入口)
        extract_res = extract_ip_ports(entrance)
        if len(extract_res) == 0:
            no_web_system["项目管理系统匹配没有web服务器的系统名单填写不及格名单"].append(row.values.tolist())
        if len(extract_res) == 1:
            row.系统访问入口 = extract_res[0]
            no_web_system["项目管理系统匹配没有web服务器的系统名单填写及格名单"].append(row.values.tolist())
        elif len(extract_res) > 1:
            for res in extract_res:
                copy_row = row.copy()
                copy_row.系统访问入口 = res
                no_web_system["项目管理系统匹配没有web服务器的系统名单填写及格名单"].append(copy_row.values.tolist())
    no_web_system["项目管理系统匹配没有web服务器的系统名单填写不及格名单"].insert(0,list(xiangmu_no_web_system_df.head()))
    no_web_system["项目管理系统匹配没有web服务器的系统名单填写及格名单"].insert(0,list(xiangmu_no_web_system_df.head()))
    return no_web_system


def gen_checkout_data(df):
    """
    生成检查结果数据\n
    :param df: DataFrame对象，包含解析后的数据\n
    :return: 检查结果字典，包含总体情况、端口不及格名单和IP地址不及格名单\n
    """
    logger.info("开始生成检查结果数据")
    checkout_data = {"填写不及格名单":[],
                    "填写及格名单":[]}

    for index, row in df.iterrows():
        # 从【系统访问入口】字段中提取IP地址和端口
        entrance = str(row.系统访问入口)
        extract_res = extract_ip_ports(entrance)
        if len(extract_res) == 0:
            checkout_data["填写不及格名单"].append(row)
        if len(extract_res) == 1:
            row.系统访问入口 = extract_res[0]
            checkout_data["填写及格名单"].append(row)
        elif len(extract_res) > 1:
            for res in extract_res:
                copy_row = row.copy()
                copy_row.系统访问入口 = res
                checkout_data["填写及格名单"].append(copy_row)
    checkout_data["填写不及格名单"].insert(0,list(df.head()))
    checkout_data["填写及格名单"].insert(0,list(df.head()))
    return checkout_data
def save_checkout_data(checkout_data, output_path):
    """
    保存检查结果到指定路径\n
    :param df: DataFrame对象，包含检查结果\n
    :param output_path: 输出文件路径\n
    """
    try:

        with pd.ExcelWriter(output_path, mode='w', engine='openpyxl') as writer:
            for sheet_name, data_list in checkout_data.items():
                # 提取表头（第一行）和数据（剩余行）
                columns = data_list[0]
                data_rows = data_list[1:]
                
                df = pd.DataFrame(data_rows, columns=columns)
                df.to_excel(writer, sheet_name=sheet_name, index=False)

        logger.info(f"检查结果已保存到 {output_path}")
    except Exception as e:
        logger.error(f"保存检查结果时发生错误：{str(e)}", exc_info=True)
        raise Exception(f"保存检查结果时发生错误：{str(e)}")

if __name__ == "__main__":
    try:
        import yaml
        # 命令行参数
        args, parser = parse_args()
        gen_time = time.strftime("%Y%m%d%H%M%S", time.localtime())
        yunhong_file_path = args.yunhong_file_path
        yunhong_sheet_name = args.yunhong_sheet_name
        xiangmu_file_path = args.xiangmu_file_path
        xiangmu_sheet_name = args.xiangmu_sheet_name
        generate_path = args.generate_path
        # 创建运行审计目录
        audit_path = os.path.join(generate_path, f"audit/{gen_time}")
        # 如果路径不存在则创建
        if not os.path.exists(audit_path):
            os.makedirs(audit_path)
        # 设置日志记录
        setup_logging(log_file=rf"{audit_path}/{gen_time}_result.log", debug=True)
        logger =  logging.getLogger(__name__)
        # # 读取配置文件
        config_file = args.config_file
        if config_file !="settings.yaml":
            if not os.path.isabs(config_file):
                config_file = os.path.join(os.getcwd(), config_file)
            if not os.path.exists(config_file):
                logger.error(f"指定的配置文件 {config_file} 不存在，请检查路径和文件名是否正确。", exc_info=True)
                exit(1)
            # 如果用户指定了配置文件，则使用用户指定的配置文件
            logger.info(f"使用指定配置文件：{config_file}")
            with open(config_file, 'r', encoding='utf-8') as file:
                config = yaml.safe_load(file)
        else:
            logger.info(f"使用默认配置文件：{config_file}")
            with open(f'{os.path.dirname(__file__)}/{config_file}', 'r', encoding='utf-8') as file:
                config = yaml.safe_load(file)
        # 将运行文件复制入审计目录，以便复现排查问题
        # shutil.copyfile(file_path, f"{audit_path}/{os.path.basename(file_path)}")
        # 创建运行结果保存文件夹
        create_folder(generate_path)
        # 解析原始文件
        yunhong_df = parse_origin_file(yunhong_file_path, yunhong_sheet_name, config['yunhong_required_columns'])
        xiangmu_df = parse_origin_file(xiangmu_file_path, xiangmu_sheet_name, config['xiangmu_required_columns'])
        # 生成检查结果
        no_web_system_data = get_no_web_system(yunhong_df)
        no_web_system_info = get_no_web_system_info(no_web_system_data,xiangmu_df)

        # 保存检查结果
        save_checkout_data(no_web_system_info,rf"{generate_path}/检查结果{gen_time}.xlsx")
        
    except Exception as e:
        logger.error(f"程序运行过程中发生错误：{str(e)}\n请检查输入参数和配置文件", exc_info=True)
        exit(1)