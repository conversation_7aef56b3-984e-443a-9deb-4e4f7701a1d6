import os
import sys
import time
import shutil
import logging
import argparse
import pandas as pd
import ipaddress

class ArchDataKEY:
    """
    归档数据键类，用于合并映射键
    """
    def __init__(self,**group_dict):
        for k,v in group_dict.items():
            setattr(self,k,v)

    def __hash__(self):
        # 多同类键值不同顺序的组合字段重排，形成哈希去重
        return hash('|'.join(reversed(list(self.__dict__.values()))))

    def __eq__(self, other):
        other_values = other.__dict__.values()
        self_values = self.__dict__.values()
        if len(other_values) != len(self_values):
            return False
        if len(list(set(other_values) - set(self_values))) > 0:
            # 值不一样
            return False
        return True

    def __str__(self):
        return f"{'|'.join([str(v) for k, v in self.__dict__.items()])}"

def is_valid_ip(ip_str):
    try:
        ipaddress.ip_address(ip_str)
        return True
    except ValueError:
        return False

def setup_logging(log_file=None, debug=False):
    """
    设置日志器\n
    :param log_file: 日志文件路径，如果为None则不写入文件\n
    :param debug: 是否开启调试模式，调试模式下日志级别为DEBUG，否则为INFO\n
    :return: None
    """
    handlers = []
    console_handler = logging.StreamHandler(sys.stderr)
    handlers.append(console_handler)

    if log_file:
        file_handler = logging.FileHandler(log_file)
        handlers.append(file_handler)

    log_format = "%(asctime)s [%(levelname)s] %(name)s: %(message)s" if debug else "%(levelname)s: %(message)s"
    
    logging.basicConfig(
        level=logging.DEBUG if debug else logging.INFO,
        format=log_format,
        handlers=handlers,
        #encoding='utf-8'
    )

def create_folder(folder_path):
    """
    创建文件夹，如果文件夹已存在则不做任何操作\n
    :param folder_path: 文件夹路径\n
    """
    try:
        os.makedirs(folder_path, exist_ok=True)
        logger.info(f"文件夹 {folder_path} 创建成功或已存在。")
    except Exception as e:
        logger.error(f"创建文件夹时发生错误：{str(e)}", exc_info=True)
        raise Exception(f"创建文件夹 {folder_path} 时发生错误：{str(e)}")

def parse_args():

    parser = argparse.ArgumentParser()
    # 配置入参关键字
    parser.add_argument(

        "-f",

        "--file_path",

        help = "待转换文档路径",

        dest = "file_path",

        type = str,

        required = True

    )

    parser.add_argument(

        "-sf",

        "--system_file_path",

        help = "系统信息文档路径",

        dest = "system_file_path",

        type = str,

        required = True

    )

    parser.add_argument(

        "-ss",

        "--system_file_sheet",

        help = "系统信息文档sheet名称",

        dest = "system_file_sheet",

        type = str,

        required = True

    )

    parser.add_argument(

        "-s",

        "--sheet_name",

        help = "待转换文档sheet名称(默认为【关键端口列表】)",

        dest = "sheet_name",

        type = str,

        default = "关键端口列表"

    )

    parser.add_argument(

        "-g",

        "--generate_path",

        help = "生成结果保存路径(默认当前目录)",

        dest = "generate_path",

        type = str,

        default = os.getcwd()

    )
    
    parser.add_argument(

        "-cf",

        "--config_file",

        help="指定配置文件(默认为【settings.yaml】)",

        dest="config_file",

        type=str,

        default='settings.yaml'

    )
    args = parser.parse_args()

    return args, parser

def parse_origin_file(origin_file_path,sheet_name,required_columns,skiprows=None):
    """
    通过Pandas解析原始文件内容\n
    :param origin_file_path: 原始Excel文件路径\n
    :param sheet_name: 要解析的sheet名称\n
    :return: DataFrame对象\n
    """
    try:
        logger.info(f"开始解析文件：{origin_file_path}，sheet名称：{sheet_name}")
        # 读取Excel文件，可根据需要调整sheet_name参数
        df = pd.read_excel(origin_file_path, engine='openpyxl', sheet_name=sheet_name,skiprows=skiprows)
        df.dropna(how='all', inplace=True)  # 删除全为空值的行
        # 检查是否包含必要的列
        for col in required_columns:
            if col not in df.columns:
                logger.error(f"错误：文档中缺少必要的列：{col}，请检查文档格式。", exc_info=True)
                raise ValueError(f"缺少必要的列：{col}")
        return df
    except FileNotFoundError:
        logger.error(f"错误：未找到{origin_file_path}文件，请检查路径和文件名是否正确。",exc_info=True)
        raise FileNotFoundError(f"未找到文件：{origin_file_path}")
    except Exception as e:
        logger.error(f"读取文档{origin_file_path}过程中发生错误：{str(e)}",exc_info=True)
        raise Exception(f"读取文档{origin_file_path}时发生错误：{str(e)}")

def gen_checkout_data(df,system_list):
    """
    生成检查结果数据\n
    :param df: DataFrame对象，包含解析后的数据\n
    :return: 检查结果字典，包含总体情况、端口不及格名单和IP地址不及格名单\n
    """
    logger.info("开始生成检查结果数据")
    checkout_data = {"总体情况":{},
                    "主机名ip情况":[],
                    "端口不及格名单":[list(df.head())],
                    "IP地址不及格名单":[list(df.head())],
                    "主机名ip端口类型组合重复名单":[],
                    "已填报名单":[list(df.head())],
                    "已填端口但主机用途为空明细":[list(df.head())],
                    "已填端口但端口用途为空明细":[list(df.head())]}

    for index, row in df.iterrows():
        if row.系统名称 not in system_list:
            continue
        # 如果端口、端口类型、主机用途为空，则跳过检查
        if (pd.isna(row.端口) or row.端口 == "") and (row.端口用途 == "" or pd.isna(row.端口用途)) and (row.主机用途 == "" or pd.isna(row.主机用途)):
            # logger.warning(f" {str(system)}_{row.IP地址} 的端口为空，跳过检查。")
            continue  
        is_wrong = False
        system = ArchDataKEY(所属一级单位=row.所属一级单位,
                            系统填报单位=row.系统填报单位,系统名称=row.系统名称)
        if system not in checkout_data["总体情况"]:
            checkout_data["总体情况"][system] = {
                "已填": 0,
                "出错数": 0,
            }

        # 检查端口
        try:
            port = row.端口
            if port < 0 or port > 65535:
                checkout_data["端口不及格名单"].append(row)
                if not is_wrong:
                    is_wrong = True
                    checkout_data["总体情况"][system]["出错数"] += 1
            # 端口填写通过时，检查主机用途
            if not (pd.isna(port) or port == "" or port < 0 or port > 65535) and (pd.isna(row.主机用途) or row.主机用途 == ""):
                checkout_data["已填端口但主机用途为空明细"].append(row)
                if not is_wrong:
                    is_wrong = True
                    checkout_data["总体情况"][system]["出错数"] += 1
            # 端口填写通过时，检查端口用途
            if not (pd.isna(port) or port == "" or port < 0 or port > 65535) and (pd.isna(row.端口用途) or row.端口用途 == ""):
                checkout_data["已填端口但端口用途为空明细"].append(row)
                if not is_wrong:
                    is_wrong = True
                    checkout_data["总体情况"][system]["出错数"] += 1
        except Exception as e:
            logger.error(f"处理行 {index} 时发生错误：{str(e)}", exc_info=True)
            if not is_wrong:
                is_wrong = True
                checkout_data["总体情况"][system]["出错数"] += 1
        
        # 检查IP地址
        ip = row.IP地址
        if pd.isna(ip) or ip == "" or (not is_valid_ip(ip) and "," not in ip):
            checkout_data["IP地址不及格名单"].append(row)
            if not is_wrong:
                is_wrong = True
                checkout_data["总体情况"][system]["出错数"] += 1
        
        # 所有检查正常，则将所有数据添加到已填报名单中
        if not is_wrong:
            checkout_data["已填报名单"].append(row)
            checkout_data["总体情况"][system]["已填"] += 1

    # 合并总体情况
    total = []
    for k,v in checkout_data["总体情况"].items():
        val = str(k).split("|")
        val.extend(list(v.values()))
        total.append(val)
    checkout_data["总体情况"] = total
    checkout_data["总体情况"].insert(0,["所属一级单位","系统填报单位","系统名称","已填","出错数"])
    checkout_data.pop("主机名ip情况")
    return checkout_data
def save_checkout_data(checkout_data, output_path):
    """
    保存检查结果到指定路径\n
    :param df: DataFrame对象，包含检查结果\n
    :param output_path: 输出文件路径\n
    """
    try:
        unit_df = pd.DataFrame(checkout_data["已填报名单"][1:], columns=checkout_data["已填报名单"][0])
        # 从已填报名单中删除主机名、ip重复名单
        checkout_data["主机名ip端口类型组合重复名单"] = unit_df[unit_df.duplicated(subset=["主机名称", "IP地址","端口","端口类型"], keep=False)].values.tolist()
        checkout_data["主机名ip端口类型组合重复名单"].insert(0,list(unit_df.head()))

        with pd.ExcelWriter(output_path, mode='w', engine='openpyxl') as writer:
            for sheet_name, data_list in checkout_data.items():
                # 提取表头（第一行）和数据（剩余行）
                columns = data_list[0]
                data_rows = data_list[1:]
                
                df = pd.DataFrame(data_rows, columns=columns)
                df.to_excel(writer, sheet_name=sheet_name, index=False)

        # 按所属一级单位拆分excel
        department_list = unit_df.所属一级单位.unique()
        for department in department_list:
            df1 = unit_df[unit_df['所属一级单位'] == department]
            df1.to_excel(f"{os.path.dirname(output_path)}\{department}.xlsx", index=False,sheet_name=department)

        logger.info(f"检查结果已保存到 {output_path}")
    except Exception as e:
        logger.error(f"保存检查结果时发生错误：{str(e)}", exc_info=True)
        raise Exception(f"保存检查结果时发生错误：{str(e)}")

if __name__ == "__main__":
    try:
        import yaml
        # 命令行参数
        args, parser = parse_args()
        gen_time = time.strftime("%Y%m%d%H%M%S", time.localtime())
        file_path = args.file_path
        system_file_path = args.system_file_path
        system_file_sheet = args.system_file_sheet
        sheet_name = args.sheet_name
        generate_path = args.generate_path
        # 创建运行审计目录
        audit_path = os.path.join(generate_path, f"audit/{gen_time}")
        # 如果路径不存在则创建
        if not os.path.exists(audit_path):
            os.makedirs(audit_path)
        # 设置日志记录
        setup_logging(log_file=rf"{audit_path}/{gen_time}_result.log", debug=True)
        logger =  logging.getLogger(__name__)
        # 读取配置文件
        config_file = args.config_file
        if config_file !="settings.yaml":
            if not os.path.isabs(config_file):
                config_file = os.path.join(os.getcwd(), config_file)
            if not os.path.exists(config_file):
                logger.error(f"指定的配置文件 {config_file} 不存在，请检查路径和文件名是否正确。", exc_info=True)
                exit(1)
            # 如果用户指定了配置文件，则使用用户指定的配置文件
            logger.info(f"使用指定配置文件：{config_file}")
            with open(config_file, 'r', encoding='utf-8') as file:
                config = yaml.safe_load(file)
        else:
            logger.info(f"使用默认配置文件：{config_file}")
            with open(f'{os.path.dirname(__file__)}/{config_file}', 'r', encoding='utf-8') as file:
                config = yaml.safe_load(file)
        # 将运行文件复制入审计目录，以便复现排查问题
        shutil.copyfile(file_path, f"{audit_path}/{os.path.basename(file_path)}")
        # 创建运行结果保存文件夹
        create_folder(generate_path)
        # 解析原始文件
        df = parse_origin_file(file_path, sheet_name, config['required_columns'])
        system_df = parse_origin_file(system_file_path, system_file_sheet, config['system_file_columns'],skiprows=2)
        # 生成检查结果
        checkout_data = gen_checkout_data(df,system_df["新信息系统名称"].values.tolist())
        # 保存检查结果
        save_checkout_data(checkout_data,rf"{generate_path}/检查结果{gen_time}.xlsx")
        
    except Exception as e:
        logger.error(f"程序运行过程中发生错误：{str(e)}\n请检查输入参数和配置文件", exc_info=True)
        exit(1)