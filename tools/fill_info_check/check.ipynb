{"cells": [{"cell_type": "code", "execution_count": 1, "id": "4f558e0a", "metadata": {}, "outputs": [], "source": ["import sys\n", "import pandas as pd\n", "import logging"]}, {"cell_type": "code", "execution_count": 2, "id": "9212e56d", "metadata": {}, "outputs": [], "source": ["def setup_logging(log_file=None, debug=False):\n", "    \"\"\"\n", "    设置日志器\\n\n", "    :param log_file: 日志文件路径，如果为None则不写入文件\\n\n", "    :param debug: 是否开启调试模式，调试模式下日志级别为DEBUG，否则为INFO\\n\n", "    :return: None\n", "    \"\"\"\n", "    handlers = []\n", "    console_handler = logging.StreamHandler(sys.stderr)\n", "    handlers.append(console_handler)\n", "\n", "    if log_file:\n", "        file_handler = logging.FileHandler(log_file)\n", "        handlers.append(file_handler)\n", "\n", "    log_format = \"%(asctime)s [%(levelname)s] %(name)s: %(message)s\" if debug else \"%(levelname)s: %(message)s\"\n", "    \n", "    logging.basicConfig(\n", "        level=logging.DEBUG if debug else logging.INFO,\n", "        format=log_format,\n", "        handlers=handlers,\n", "        #encoding='utf-8'\n", "    )\n", "\n", "setup_logging(debug=True)\n", "logger =  logging.getLogger(__name__)"]}, {"cell_type": "code", "execution_count": 3, "id": "cb0cfce2", "metadata": {}, "outputs": [], "source": ["origin_file = r\"D:/内网匹配结果202506271735.xlsx\"\n", "origin_sheet_name = \"Sheet1\"\n", "system_list_file = r\"D:/原始数据/0619/0619/20250619效能评估系统清单-云宏/20250619效能评估系统清单-云宏_add.xlsx\"\n", "system_sheet_name = \"结果-20250619\"\n", "manage_system_file = r\"D:/原始数据/应用类系统导出(2025-06-26+18_06_18).xlsx\"\n", "manage_sheet_name = \"筛选仅参选系统\"\n", "online_system_file = r\"D:/downloads/edge下载/2025年评估.xlsx\"\n", "online_sheet_name = \"非参评系统下线登记\"\n", "# network_order_file = r\"D:/原始数据/补充-安全策略工单_2025060902/安全策略工单_2025060902全量.xlsx\"\n", "# network_order_sheet_name = \"temp_orders_20250609\"\n", "network_order_file = r\"D:/原始数据/补充-安全策略工单_2025060902/网络策略全量.xlsx\"\n", "network_order_sheet_name = \"Sheet1\"\n"]}, {"cell_type": "code", "execution_count": 4, "id": "ca7b2d4a", "metadata": {}, "outputs": [], "source": ["def parse_origin_file(origin_file_path,sheet_name,required_columns,skiprows=None):\n", "    \"\"\"\n", "    通过Pandas解析原始文件内容\\n\n", "    :param origin_file_path: 原始Excel文件路径\\n\n", "    :param sheet_name: 要解析的sheet名称\\n\n", "    :return: DataFrame对象\\n\n", "    \"\"\"\n", "    try:\n", "        logger.info(f\"开始解析文件：{origin_file_path}，sheet名称：{sheet_name}\")\n", "        # 读取Excel文件，可根据需要调整sheet_name参数\n", "        df = pd.read_excel(origin_file_path, engine='openpyxl', sheet_name=sheet_name,skiprows=skiprows)\n", "        df.dropna(how='all', inplace=True)  # 删除全为空值的行\n", "        # 检查是否包含必要的列\n", "        for col in required_columns:\n", "            if col not in df.columns:\n", "                logger.error(f\"错误：文档中缺少必要的列：{col}，请检查文档格式。\", exc_info=True)\n", "                raise ValueError(f\"缺少必要的列：{col}\")\n", "        return df\n", "    except FileNotFoundError:\n", "        logger.error(f\"错误：未找到{origin_file_path}文件，请检查路径和文件名是否正确。\",exc_info=True)\n", "        raise FileNotFoundError(f\"未找到文件：{origin_file_path}\")\n", "    except Exception as e:\n", "        logger.error(f\"读取文档{origin_file_path}过程中发生错误：{str(e)}\",exc_info=True)\n", "        raise Exception(f\"读取文档{origin_file_path}时发生错误：{str(e)}\")"]}, {"cell_type": "code", "execution_count": 5, "id": "2660acb4", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-06-27 17:39:50,718 [INFO] __main__: 开始解析文件：D:/内网匹配结果202506271735.xlsx，sheet名称：Sheet1\n", "2025-06-27 17:39:57,770 [INFO] __main__: 开始解析文件：D:/原始数据/0619/0619/20250619效能评估系统清单-云宏/20250619效能评估系统清单-云宏_add.xlsx，sheet名称：结果-20250619\n", "2025-06-27 17:39:57,871 [INFO] __main__: 开始解析文件：D:/downloads/edge下载/2025年评估.xlsx，sheet名称：非参评系统下线登记\n", "2025-06-27 17:39:57,937 [INFO] __main__: 开始解析文件：D:/原始数据/补充-安全策略工单_2025060902/网络策略全量.xlsx，sheet名称：Sheet1\n"]}], "source": ["origin_df = parse_origin_file(origin_file,origin_sheet_name,[])\n", "system_list_df  = parse_origin_file(system_list_file,system_sheet_name,[],skiprows=2)\n", "# manage_system_df = parse_origin_file(manage_system_file,manage_sheet_name,[])\n", "online_system_df = parse_origin_file(online_system_file,online_sheet_name,[],skiprows=1)\n", "network_order_df = parse_origin_file(network_order_file,network_order_sheet_name,[])"]}, {"cell_type": "code", "execution_count": 6, "id": "c3daf9c6", "metadata": {}, "outputs": [], "source": ["# import json\n", "# system_dict = {}\n", "# for index,system in system_list_df.iterrows():\n", "#     system_dict[system.信息系统编码] = system.新信息系统名称\n", "# json_str = json.dumps(system_dict, ensure_ascii=False, indent=4)\n", "# with open(\"D:/系统编码映射.json\", 'w', encoding='utf-8') as f:\n", "#     f.write(json_str)"]}, {"cell_type": "code", "execution_count": 7, "id": "66ac33f3", "metadata": {}, "outputs": [], "source": ["import re\n", "def extract_ip_ports(text):\n", "\n", "    # 定义正则表达式匹配IPv4地址和端口\n", "    pattern = r'(\\d{1,3}\\.\\d{1,3}\\.\\d{1,3}\\.\\d{1,3})[:：](\\d{1,5})'\n", "    matches = re.findall(pattern, text)\n", "    \n", "    results = []\n", "    for ip, port in matches:\n", "        # 验证IP地址每个部分是否合法（0-255）\n", "        ip_parts = ip.split('.')\n", "        valid_ip = True\n", "        for part in ip_parts:\n", "            if not 0 <= int(part) <= 255:\n", "                valid_ip = False\n", "                break\n", "        \n", "        # 验证端口是否合法（0-65535）\n", "        valid_port = 0 <= int(port) <= 65535\n", "        \n", "        # 如果IP和端口都合法，则组合成\"ip:端口\"格式\n", "        if valid_ip and valid_port:\n", "            # 保留原始文本中的冒号（通过重新查找原始格式）\n", "            original_format = re.search(re.escape(ip) + r'[:：]' + re.escape(port), text)\n", "            if original_format:\n", "                results.append(original_format.group(0))\n", "    if len(results) == 0:\n", "        # 如果IPv4地址和端口没有匹配出结果，则匹配https://IPv4地址或http://IPv4地址\n", "        pattern = r'(https?://)?(\\d{1,3}\\.\\d{1,3}\\.\\d{1,3}\\.\\d{1,3})'\n", "        matches = re.findall(pattern, text)\n", "        \n", "        for protocol, ip in matches:\n", "            ip_parts = ip.split('.')\n", "            valid_ip = True\n", "            for part in ip_parts:\n", "                if not 0 <= int(part) <= 255:\n", "                    valid_ip = False\n", "                    break\n", "            if valid_ip and protocol:\n", "                if protocol == \"http://\":\n", "                    # http时端口默认为80\n", "                    results.append(f\"{ip}:{80}\")\n", "                if protocol == \"https://\":\n", "                    # http时端口默认为80\n", "                    results.append(f\"{ip}:{443}\")\n", "\n", "    return results"]}, {"cell_type": "code", "execution_count": 8, "id": "e7dcd4c5", "metadata": {}, "outputs": [], "source": ["# 填写完成的系统名单\n", "# finish_system_list = []\n", "# 参评系统名称列表\n", "# system_list = system_list_df[\"新信息系统名称\"].values.tolist()\n", "# 参评系统名单\n", "online_system_df = online_system_df[pd.isna(online_system_df[\"备注\"])]\n", "online_system_list = online_system_df[\"信息系统名称\"].values.tolist()\n", "# system_list = [system for system in system_list if system in online_system_list]"]}, {"cell_type": "code", "execution_count": 9, "id": "0250e63b", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-06-27 17:40:11,362 [WARNING] __main__: 市财政局-广州市收支两条线管理收缴系统 对应的云服务订单数据有多条\n", "2025-06-27 17:40:12,721 [WARNING] __main__: 市交通运输局-广州市道路路政审批及监管系统 没有对应的云服务订单数据\n", "2025-06-27 17:40:14,637 [WARNING] __main__: 市人力资源社会保障局-广州市人力资源和社会保障发展研究中心广州市技工院校综合信息管理系统(广州职教网) 没有对应的云服务订单数据\n", "2025-06-27 17:40:19,760 [WARNING] __main__: 市住房城乡建设局-广州市建设工程融合监管平台高大支模及脚手架系统 对应的云服务订单数据有多条\n", "2025-06-27 17:40:19,770 [WARNING] __main__: 市住房城乡建设局-广州市工程建设项目竣工联合验收系统 对应的云服务订单数据有多条\n", "2025-06-27 17:40:20,116 [WARNING] __main__: 市住房城乡建设局-直管房数据集约化及决策分析平台 没有对应的云服务订单数据\n"]}], "source": ["check_data = {\n", "            \"填写及格明细\":[],\n", "            # \"填写不及格明细\":[],\n", "            \"部分主机没有填写端口\":[],\n", "            \"部分主机填了端口没有填主机用途\":[],\n", "            \"有数据库或应用服务器但没有web服务器\":[],\n", "            \"web服务器没有填写端口\":[],\n", "            \"数据库服务器没有填端口\":[],\n", "            \"没有填写web或应用或数据库服务器\":[],\n", "            \"网络策略填错或漏填\":[list(network_order_df.head())],\n", "            \"总体情况\":{},\n", "            }\n", "# 逐个系统确认填报情况\n", "for index,rowdata in online_system_df.iterrows():\n", "    unit = rowdata.一级单位\n", "    system = rowdata.信息系统名称\n", "    system_code = rowdata.系统编号\n", "    unit_system = f\"{unit}|{system}\"\n", "    if unit_system not in check_data[\"总体情况\"]:\n", "        check_data[\"总体情况\"][unit_system] = {\"完成填写情况\":\"否\",\"是否未填报\":\"否\",\"填错情况\":\"是\",\"云服务订单填报情况\":\"否\",\"网络策略填报情况\":\"是\"}\n", "    system_df = origin_df[origin_df[\"系统名称\"] == system]\n", "    if len(system_df) > 0:\n", "        web_df = system_df[system_df[\"主机用途\"] == \"Web服务器\"]\n", "        database_df = system_df[system_df[\"主机用途\"] == \"数据库服务器\"]\n", "        application_df = system_df[system_df[\"主机用途\"] == \"应用服务器\"]\n", "        # 筛出填写合格的名单\n", "        # ① 有web服务器及数据库服务器，全部填写了端口\n", "        if len(web_df) > 0 and len(database_df) > 0 and len(web_df[web_df[\"端口\"].isna()]) == 0 and len(database_df[database_df[\"端口\"].isna()]) == 0:\n", "            check_data[\"填写及格明细\"].append(system)\n", "            check_data[\"总体情况\"][unit_system][\"完成填写情况\"] = \"是\"\n", "            check_data[\"总体情况\"][unit_system][\"填错情况\"] = \"否\"\n", "            \n", "        # ② 没有数据库服务器，有web服务器并且填写了端口\n", "        elif len(database_df) == 0 and len(web_df) > 0 and len(web_df[web_df[\"端口\"].isna()]) == 0:\n", "            check_data[\"填写及格明细\"].append(system)\n", "            check_data[\"总体情况\"][unit_system][\"完成填写情况\"] = \"是\"\n", "            check_data[\"总体情况\"][unit_system][\"填错情况\"] = \"否\"\n", "            \n", "        # ③ 有web服务器，没有数据库服务器，有应用服务器\n", "        elif len(web_df) > 0 and len(web_df[web_df[\"端口\"].isna()]) == 0 and len(database_df) == 0 and len(application_df) > 0:\n", "            check_data[\"填写及格明细\"].append(system)\n", "            check_data[\"总体情况\"][unit_system][\"完成填写情况\"] = \"是\"\n", "            check_data[\"总体情况\"][unit_system][\"填错情况\"] = \"否\"\n", "\n", "        if check_data[\"总体情况\"][unit_system][\"完成填写情况\"] == \"否\":\n", "            # 对填写不及格的名单进行分类\n", "            # ① 没有填端口 \n", "            # port_df = system_df[system_df[\"端口\"].notna()]\n", "            if len(system_df[system_df[\"端口\"].isna()]) > 0:\n", "                check_data[\"部分主机没有填写端口\"].append(system)\n", "            # ② 填了端口没有填主机用途\n", "            if len(system_df[(system_df[\"端口\"].notna()) & (system_df[\"主机用途\"].isna()) ]) > 0:\n", "                check_data[\"部分主机填了端口没有填主机用途\"].append(system)\n", "            # ③ 有数据库服务器但没有web服务器\n", "            if (len(database_df) > 0 or len(application_df) > 0) and len(web_df) == 0:\n", "                check_data[\"有数据库或应用服务器但没有web服务器\"].append(system)\n", "            # ④ web服务器没有填写端口\n", "            if len(web_df) > 0 and len(web_df[web_df[\"端口\"].isna()]) > 0:\n", "                check_data[\"web服务器没有填写端口\"].append(system)\n", "            # ⑤ 数据库服务器没有填端口\n", "            if len(database_df) > 0 and len(database_df[database_df[\"端口\"].isna()]) > 0:\n", "                check_data[\"数据库服务器没有填端口\"].append(system)\n", "            if len(web_df) == 0 and len(database_df) == 0 and len(application_df) == 0:\n", "                check_data[\"没有填写web或应用或数据库服务器\"].append(system)\n", "            unfill_system = check_data[\"部分主机填了端口没有填主机用途\"]+\\\n", "                            check_data[\"有数据库或应用服务器但没有web服务器\"]+\\\n", "                            check_data[\"web服务器没有填写端口\"]+\\\n", "                            check_data[\"数据库服务器没有填端口\"]+\\\n", "                            check_data[\"部分主机没有填写端口\"]+\\\n", "                            check_data[\"没有填写web或应用或数据库服务器\"]\n", "            if system not in unfill_system:\n", "                logger.warning(f\"{unit}-{system} 没有归类到任一类错误\")\n", "    else:\n", "        check_data[\"总体情况\"][unit_system][\"是否未填报\"] = \"是\"\n", "\n", "    service_rows = system_list_df[(system_list_df[\"新信息系统名称\"] == system) & (system_list_df[\"一级单位\"] == unit)]\n", "    if len(service_rows) == 0:\n", "        logger.warning(f\"{unit}-{system} 没有对应的云服务订单数据\")\n", "        check_data[\"总体情况\"][unit_system][\"网络策略填报情况\"] = \"否\"\n", "        continue\n", "    elif len(service_rows) > 1:\n", "        logger.warning(f\"{unit}-{system} 对应的云服务订单数据有多条\")\n", "    else:\n", "        if service_rows[\"是否申请探针\"].values.tolist()[0] == \"是\":\n", "            check_data[\"总体情况\"][unit_system][\"云服务订单填报情况\"] = \"是\"\n", "    # 转换为云保障平台的单位进行匹配\n", "    yun_unit = service_rows[\"一级单位（云保障平台）\"].values.tolist()[0]\n", "    network_rows = network_order_df[network_order_df[\"信息系统编码\"] == system_code]\n", "    if len(network_rows) == 0:\n", "        check_data[\"总体情况\"][unit_system][\"网络策略填报情况\"] = \"否\"\n", "        continue\n", "    system_ip_list = list(set(network_rows[\"源ip\"].values.tolist()))\n", "    for source_ip in system_ip_list:\n", "        source_ip_rows = network_rows[network_rows[\"源ip\"] == source_ip]\n", "        # 每个ip最少要填报一个udp的12201端口和一个tcp的9000端口\n", "        udp_count = source_ip_rows[(source_ip_rows[\"协议\"] == \"UDP\") & (source_ip_rows[\"端口\"] == 12201)]\n", "        tcp_count = source_ip_rows[(source_ip_rows[\"协议\"] == \"TCP\") & (source_ip_rows[\"端口\"] == 9000)]\n", "        if len(udp_count) == 0 or len(tcp_count) == 0:\n", "            check_data[\"总体情况\"][unit_system][\"网络策略填报情况\"] = \"否\"\n", "            # logger.warning(f\"{unit}-{system}-{source_ip} 的网络策略没有填写12201端口或者9000端口\")\n", "            check_data[\"网络策略填错或漏填\"].extend(source_ip_rows.values.tolist())"]}, {"cell_type": "code", "execution_count": 10, "id": "9ba2a473", "metadata": {}, "outputs": [], "source": ["# # ①+②\n", "# # 判断只有填了端口的web服务器行\n", "# web_system = yunhong_df[(yunhong_df[\"主机用途\"] == \"Web服务器\") & (yunhong_df[\"端口\"].notna())]\n", "# web_system_list = web_system[\"系统名称\"].unique().tolist()\n", "# len(web_system_list)\n", "# finish_system_list.extend(web_system_list)"]}, {"cell_type": "code", "execution_count": 11, "id": "da70b3b4", "metadata": {}, "outputs": [], "source": ["# # ③\n", "# # 筛出云宏表中有数据库服务器和端口的业务系统\n", "# database_system = yunhong_df[(yunhong_df[\"主机用途\"] == \"数据库服务器\") & (yunhong_df[\"端口\"].notna())]\n", "# database_system_list = list(set(database_system[\"系统名称\"].values.tolist()))\n", "# # database_system_list\n", "# # 从项目管理系统匹配web信息\n", "# manage_web  = []\n", "# manage_system_data = manage_system_df[manage_system_df[\"信息系统名称\"].isin(database_system_list)]\n", "# for index, row in manage_system_data.iterrows():\n", "#     # 从【系统访问入口】字段中提取IP地址和端口\n", "#     entrance = str(row.系统访问入口)\n", "#     extract_res = extract_ip_ports(entrance)\n", "#     if len(extract_res) == 0:\n", "#         pass\n", "#         # logger.warning(f\"【系统访问入口】字段中提取IP地址和端口失败，请检查【系统访问入口】字段内容：{entrance}\")\n", "#     if len(extract_res) >= 1:\n", "#         manage_web.append(row.信息系统名称)\n", "# finish_system_list.extend(manage_web)"]}, {"cell_type": "code", "execution_count": 12, "id": "7e02e95c", "metadata": {}, "outputs": [], "source": ["# # ④\n", "# # 筛出云宏表中有应用服务器的业务系统\n", "# application_system = yunhong_df[(yunhong_df[\"主机用途\"] == \"应用服务器\")]\n", "# application_system_list = list(set(application_system[\"系统名称\"].values.tolist()))\n", "# finish_system_list.extend(application_system_list)"]}, {"cell_type": "code", "execution_count": 13, "id": "ab41e9f7", "metadata": {}, "outputs": [], "source": ["# 过滤下线系统\n", "\n", "# finish_system_list = [system for system in finish_system_list if system in online_system_list]"]}, {"cell_type": "code", "execution_count": 14, "id": "b45d8b73", "metadata": {}, "outputs": [], "source": ["# # 判断也有填了端口的web服务器行\n", "# web_system = yunhong_df[~(yunhong_df[\"系统名称\"].isin(finish_system_list)) & (yunhong_df[\"主机用途\"] == \"Web服务器\") & (yunhong_df[\"端口\"].notna())]\n", "# web_system_list = web_system[\"系统名称\"].unique().tolist()\n", "# len(web_system_list)\n", "# finish_system_list.extend(web_system_list)"]}, {"cell_type": "code", "execution_count": 15, "id": "48e683f2", "metadata": {}, "outputs": [], "source": ["# # 拿web服务器没有填端口的系统匹配项目管理系统\n", "# no_web_system = yunhong_df[ (yunhong_df[\"系统名称\"].isin(database_system_list)) & \\\n", "#                             (yunhong_df[\"主机用途\"] == \"Web服务器\") &\\\n", "#                             (yunhong_df[\"端口\"].isna()) \n", "#                             ]\n", "# no_web_system_list = no_web_system[\"系统名称\"].unique().tolist()\n", "# # no_web_system_list = [system for system in no_web_system_list if system in database_system]\n", "# len(no_web_system_list)\n", "# # no_web_system_list"]}, {"cell_type": "code", "execution_count": 16, "id": "067b6270", "metadata": {}, "outputs": [], "source": ["# 生成已填写完成明细\n", "output_folder_path = r\"D:/系统统计202506271735.xlsx\"\n", "# finish_system_list = [system for system in  finish_system_list  if system in system_list]\n", "\n", "web_system_info = origin_df[origin_df[\"系统名称\"].isin(check_data[\"填写及格明细\"])].drop_duplicates(subset=['系统名称'])\n", "# web_system_info.to_excel(output_folder_path,index=False)\n", "# 已填写数量\n", "filled_count = pd.pivot_table(web_system_info,values=\"系统名称\",index=\"所属一级单位\",aggfunc=\"count\")\n", "# filled_count\n"]}, {"cell_type": "code", "execution_count": 17, "id": "8b156078", "metadata": {}, "outputs": [], "source": ["#总体数量\n", "total_system_info = system_list_df[system_list_df[\"新信息系统名称\"].isin(online_system_list)].drop_duplicates(subset=['新信息系统名称'])\n", "total_count = pd.pivot_table(total_system_info,values=\"新信息系统名称\",index=\"一级单位\",aggfunc=\"count\")\n", "# total_count"]}, {"cell_type": "code", "execution_count": 18, "id": "7de3d9b5", "metadata": {}, "outputs": [], "source": ["# 合并总体数量及已填写数量\n", "combined = pd.concat([filled_count, total_count], axis=1).fillna(0)\n", "combined.columns = ['已填写数量', '总体数量']\n", "# combined"]}, {"cell_type": "code", "execution_count": 19, "id": "37a32c6f", "metadata": {}, "outputs": [], "source": ["# with pd.ExcelWriter(output_folder_path, mode='w', engine='openpyxl') as writer:\n", "#     combined.to_excel(writer, sheet_name=\"填写情况统计\", index=True,index_label=\"所属一级单位\")\n", "    # web_system_info.to_excel(writer, sheet_name=\"已填写系统明细\", index=False)\n", "# check_data"]}, {"cell_type": "code", "execution_count": 20, "id": "fe2399c9", "metadata": {}, "outputs": [], "source": ["final_data = {}\n", "for key,value in check_data.items():\n", "    if isinstance(value,dict) or key == \"网络策略填错或漏填\":\n", "        final_data[key] = value\n", "        continue\n", "    final_data[key] = [list(origin_df.head())]\n", "    final_data[key].extend(origin_df[origin_df[\"系统名称\"].isin(value)].values.tolist())\n", "# check_data"]}, {"cell_type": "code", "execution_count": 21, "id": "ee4b5588", "metadata": {}, "outputs": [], "source": ["with pd.ExcelWriter(output_folder_path, mode='w', engine='openpyxl') as writer:\n", "    combined.to_excel(writer, sheet_name=\"填写情况统计\", index=True,index_label=\"所属一级单位\")\n", "    for sheet_name, data_list in final_data.items():\n", "        if isinstance(data_list, dict):\n", "            columns = [\"所属一级单位\",\"系统\",\"完成填写情况\",\"是否未填报\",\"填错情况\",\"云服务订单填报情况\",\"网络策略填报情况\"]\n", "            data = [[k.split(\"|\")[0],k.split(\"|\")[1],*list(v.values())] for k,v in data_list.items()]\n", "            df = pd.DataFrame(data, columns=columns)\n", "            df.to_excel(writer, sheet_name=sheet_name, index=False)\n", "        else:\n", "            # 提取表头（第一行）和数据（剩余行）\n", "            columns = data_list[0]\n", "            data_rows = data_list[1:]\n", "            \n", "            df = pd.DataFrame(data_rows, columns=columns)\n", "            df.to_excel(writer, sheet_name=sheet_name, index=False)"]}], "metadata": {"kernelspec": {"display_name": "uniarch", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 5}