
ruledict = {
    "REQUEST-900-EXCLUSION-RULES-BEFORE-CRS.conf": {
        "name": "REQUEST-900-EXCLUSION-RULES-BEFORE-CRS.conf",
        "describe": "在核心规则集(CRS)之前应用的排除规则。"
    },
    "REQUEST-901-INITIALIZATION.conf": {
        "name": "REQUEST-901-INITIALIZATION.conf",
        "describe": "初始化ModSecurity核心规则集的文件，包括启用主体检查和采样配置等。"
    },
    "REQUEST-903.9001-DRUPAL-EXCLUSION-RULES.conf": {
        "name": "REQUEST-903.9001-DRUPAL-EXCLUSION-RULES.conf",
        "describe": "用于Drupal的排除规则。"
    },
    "REQUEST-903.9002-WORDPRESS-EXCLUSION-RULES.conf": {
        "name": "REQUEST-903.9002-WORDPRESS-EXCLUSION-RULES.conf",
        "describe": "用于WordPress的排除规则。"
    },
    "REQUEST-903.9003-NEXTCLOUD-EXCLUSION-RULES.conf": {
        "name": "REQUEST-903.9003-NEXTCLOUD-EXCLUSION-RULES.conf",
        "describe": "用于Nextcloud的排除规则。"
    },
    "REQUEST-903.9004-DOKUWIKI-EXCLUSION-RULES.conf": {
        "name": "REQUEST-903.9004-DOKUWIKI-EXCLUSION-RULES.conf",
        "describe": "用于DokuWiki的排除规则。"
    },
    "REQUEST-903.9005-CPANEL-EXCLUSION-RULES.conf": {
        "name": "REQUEST-903.9005-CPANEL-EXCLUSION-RULES.conf",
        "describe": "用于cPanel的排除规则。"
    },
    "REQUEST-903.9006-XENFORO-EXCLUSION-RULES.conf": {
        "name": "REQUEST-903.9006-XENFORO-EXCLUSION-RULES.conf",
        "describe": "用于XenForo的排除规则。"
    },
    "REQUEST-905-COMMON-EXCEPTIONS.conf": {
        "name": "REQUEST-905-COMMON-EXCEPTIONS.conf",
        "describe": "常见的例外规则。"
    },
    "REQUEST-910-IP-REPUTATION.conf": {
        "name": "REQUEST-910-IP-REPUTATION.conf",
        "describe": "基于IP信誉的规则，包括已知恶意客户端、高风险国家等。"
    },
    "REQUEST-911-METHOD-ENFORCEMENT.conf": {
        "name": "REQUEST-911-METHOD-ENFORCEMENT.conf",
        "describe": "方法执行规则，检测不被策略允许的HTTP方法。"
    },
    "REQUEST-912-DOS-PROTECTION.conf": {
        "name": "REQUEST-912-DOS-PROTECTION.conf",
        "describe": "拒绝服务(DoS)攻击保护规则。"
    },
    "REQUEST-913-SCANNER-DETECTION.conf": {
        "name": "REQUEST-913-SCANNER-DETECTION.conf",
        "describe": "检测安全扫描器的规则。"
    },
    "REQUEST-920-PROTOCOL-ENFORCEMENT.conf": {
        "name": "REQUEST-920-PROTOCOL-ENFORCEMENT.conf",
        "describe": "HTTP协议执行规则，确保请求符合HTTP协议标准。"
    },
    "REQUEST-921-PROTOCOL-ATTACK.conf": {
        "name": "REQUEST-921-PROTOCOL-ATTACK.conf",
        "describe": "检测HTTP协议攻击的规则，例如请求走私和响应分裂攻击。"
    },
    "REQUEST-930-APPLICATION-ATTACK-LFI.conf": {
        "name": "REQUEST-930-APPLICATION-ATTACK-LFI.conf",
        "describe": "本地文件包含(LFI)攻击的检测规则。"
    },
    "REQUEST-931-APPLICATION-ATTACK-RFI.conf": {
        "name": "REQUEST-931-APPLICATION-ATTACK-RFI.conf",
        "describe": "远程文件包含(RFI)攻击的检测规则。"
    },
    "REQUEST-932-APPLICATION-ATTACK-RCE.conf": {
        "name": "REQUEST-932-APPLICATION-ATTACK-RCE.conf",
        "describe": "远程命令执行(RCE)攻击的检测规则。"
    },
    "REQUEST-933-APPLICATION-ATTACK-PHP.conf": {
        "name": "REQUEST-933-APPLICATION-ATTACK-PHP.conf",
        "describe": "PHP注入攻击的检测规则。"
    },
    "REQUEST-934-APPLICATION-ATTACK-NODEJS.conf": {
        "name": "REQUEST-934-APPLICATION-ATTACK-NODEJS.conf",
        "describe": "Node.js注入攻击的检测规则。"
    },
    "REQUEST-941-APPLICATION-ATTACK-XSS.conf": {
        "name": "REQUEST-941-APPLICATION-ATTACK-XSS.conf",
        "describe": "跨站脚本(XSS)攻击的检测规则。"
    },
    "REQUEST-942-APPLICATION-ATTACK-SQLI.conf": {
        "name": "REQUEST-942-APPLICATION-ATTACK-SQLI.conf",
        "describe": "SQL注入(SQLi)攻击的检测规则。"
    },
    "REQUEST-943-APPLICATION-ATTACK-SESSION-FIXATION.conf": {
        "name": "REQUEST-943-APPLICATION-ATTACK-SESSION-FIXATION.conf",
        "describe": "会话固定攻击的检测规则。"
    },
    "REQUEST-944-APPLICATION-ATTACK-JAVA.conf": {
        "name": "REQUEST-944-APPLICATION-ATTACK-JAVA.conf",
        "describe": "Java相关的远程命令执行攻击的检测规则。"
    },
    "REQUEST-949-BLOCKING-EVALUATION.conf": {
        "name": "REQUEST-949-BLOCKING-EVALUATION.conf",
        "describe": "阻止评估规则，基于IP信誉进行请求拒绝。"
    },
    "RESPONSE-950-DATA-LEAKAGES.conf": {
        "name": "RESPONSE-950-DATA-LEAKAGES.conf",
        "describe": "检测和防止数据泄露的规则，例如目录列表和CGI源代码泄漏。"
    },
    "RESPONSE-951-DATA-LEAKAGES-SQL.conf": {
        "name": "RESPONSE-951-DATA-LEAKAGES-SQL.conf",
        "describe": "SQL信息泄露的检测规则。"
    },
    "RESPONSE-952-DATA-LEAKAGES-JAVA.conf": {
        "name": "RESPONSE-952-DATA-LEAKAGES-JAVA.conf",
        "describe": "Java源代码和错误信息泄露的检测规则。"
    },
    "RESPONSE-953-DATA-LEAKAGES-PHP.conf": {
        "name": "RESPONSE-953-DATA-LEAKAGES-PHP.conf",
        "describe": "PHP源代码和信息泄露的检测规则。"
    },
    "RESPONSE-954-DATA-LEAKAGES-IIS.conf": {
        "name": "RESPONSE-954-DATA-LEAKAGES-IIS.conf",
        "describe": "IIS相关信息泄露的检测规则。"
    },
    "RESPONSE-959-BLOCKING-EVALUATION.conf": {
        "name": "RESPONSE-959-BLOCKING-EVALUATION.conf",
        "describe": "阻止评估规则，基于出站异常分数进行请求阻止。"
    }
}

