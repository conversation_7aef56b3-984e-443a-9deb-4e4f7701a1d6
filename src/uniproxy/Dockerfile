FROM registry.jxit.net.cn:5000/alpine:3.19.1
# FROM registry.jxit.net.cn:5000/alpine:3.19.1-arm

RUN sed -i '<EMAIL>@mirrors.tencent.com@g' /etc/apk/repositories && \
    apk update && \
    apk add nginx tcpdump vim curl bash python3 py3-pip py3-flask py3-requests py3-kubernetes tzdata && \
    cp /usr/share/zoneinfo/Asia/Shanghai /etc/localtime && \
    echo "Asia/Shanghai" > /etc/timezone

RUN mkdir -p /app/templates

WORKDIR /app

COPY templates/ /app/templates
COPY uniproxy.py k8sclient.py ruledesc.py /app/
