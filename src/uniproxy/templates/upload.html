<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Install Files</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 0;
            background-color: #f4f4f4;
        }
        .container {
            display: flex;
            max-width: 1200px;
            margin: 50px auto;
            background: #fff;
            padding: 20px;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
            border-radius: 8px;
        }
        .file-list, .upload-section {
            flex: 1;
            margin: 10px;
        }
        .file-list {
            border-right: 1px solid #ddd;
            padding-right: 20px;
            text-align: left;
        }
        h1 {
            color: #333;
            text-align: center;
        }
        ul {
            list-style: none;
            padding: 0;
        }
        ul li {
            background: #e9e9e9;
            margin: 5px 0;
            padding: 10px;
            border-radius: 4px;
        }
        table {
          width: 100%;
          border-collapse: collapse;
        }
        table, th, td {
          border: 1px solid #ddd;
        }
        th, td {
          padding: 8px;
          text-align: left;
        }
        th {
          background-color: #f2f2f2;
        }
        ul li a {
            text-decoration: none;
            color: #007BFF;
        }
        .upload-section form {
            margin: 20px 0;
            text-align: center;
        }
        input[type="file"] {
            display: block;
            margin: 0 auto 10px auto;
        }
        input[type="submit"] {
            background-color: #007BFF;
            color: #fff;
            border: none;
            padding: 10px 20px;
            cursor: pointer;
            border-radius: 4px;
        }
        input[type="submit"]:hover {
            background-color: #0056b3;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="file-list">
          <table>
            <thead>
              <tr>
                <th>文件名</th>
                <th>大小</th>
                <th>最后修改时间</th>
              </tr>
            </thead>
            <tbody>
              {% for file in file_list %}
              <tr>
                <td><a href="{{ url_for('up_install', filename=file.name) }}">{{ file.name }}</a></td>
                <td>{{ file.size }} bytes</td>
                <td>{{ file.mtime }}</td>
              </tr>
              {% endfor %}
            </tbody>
          </table>
        </div>
        <div class="upload-section">
            <form method="POST" enctype="multipart/form-data" action="/up-install">
		<p>上传文件</p>
                <input type="file" name="file">
                <input type="submit" value="Upload">
            </form>
            <hr>
            <form method="POST" enctype="multipart/form-data" action="/up-install/upload-login-logo">
                <p>上传登录页Logo</p>
                <input type="file" name="login_logo">
                <input type="submit" value="Upload Login Logo">
            </form>
            <hr>
            <form method="POST" enctype="multipart/form-data" action="/up-install/upload-nav-logo">
                <p>上传导航栏Logo</p>
                <input type="file" name="nav_logo">
                <input type="submit" value="Upload Nav Logo">
            </form>
        </div>
    </div>
</body>
</html>

