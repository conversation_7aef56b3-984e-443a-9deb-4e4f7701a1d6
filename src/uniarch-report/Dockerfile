FROM registry.jxit.net.cn:5000/alpine:3.19.1
# FROM registry.jxit.net.cn:5000/alpine:3.19.1-arm

RUN sed -i '<EMAIL>@mirrors.tencent.com@g' /etc/apk/repositories && \
    apk update && \
    apk add vim curl bash python3 py3-pip py3-requests tzdata && \
    cp /usr/share/zoneinfo/Asia/Shanghai /etc/localtime && \
    echo "Asia/Shanghai" > /etc/timezone && \
    mkdir -p /app

RUN pip install python-docx --break-system-packages -i https://repo.huaweicloud.com/repository/pypi/simple

WORKDIR /app

COPY . /app

ENTRYPOINT ["bash", "-c", "sleep 8888888888888888"]
