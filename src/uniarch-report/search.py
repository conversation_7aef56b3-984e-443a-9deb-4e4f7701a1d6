import os
import sys
import time
import math
import json
import shutil
import socket
import base64
import requests
from log import logger
from docx import Document
from datetime import datetime, timedelta
from docx.enum.text import WD_UNDERLINE
from docx.shared import Pt

def date_to_timestamp(date_str):
  """将日期字符串转换为时间戳"""
  try:
    # 尝试解析为YYYY-MM-DD格式
    dt = datetime.strptime(date_str, "%Y-%m-%d")
    return int(dt.timestamp())
  except ValueError:
    raise ValueError("日期格式不正确，请使用YYYY-MM-DD")

# 检查参数数量
if len(sys.argv) != 3:
    print("Usage: python3 search.py <start_date> <end_date>")
    print("日期格式: YYYY-MM-DD")
    sys.exit(1)

# 开始结束时间
# 转换日期为时间戳
START_TS = date_to_timestamp(sys.argv[1])
END_TS = date_to_timestamp(sys.argv[2])

# 确保结束时间大于开始时间
if END_TS <= START_TS:
    raise ValueError("结束时间必须大于开始时间")

# 地址、采样率等参数
UNISERVER_ADDR = "xxxxx"
# web端口、数据端口、用户名密码
UNISERVER_WEB_PORT = 9000
UNISERVER_DATA_PORT = 12201
UNISERVER_USERNAME="admin"
UNISERVER_PASSWORD="xxxx"
# 前端负载均衡采样率
UNISERVER_SAMPLE = 1
# 聚合结果发送协议
UNISERVER_SEND_PROTOCOL = "udp"
# 仪表板ID和查询ID，根据浏览器审查元素获取
UNISERVER_DASHBOARD_ID="680eeb250ab1f6173e17a5cf"
# 系统维度结果集ID
UNISERVER_SYSTEM_REPORT_QUERY_ID="ce60ac07-d3ce-4d56-8a04-bda98ed323f0"

# 仪表板元素数据
METADATA_FILE = "metadata.json"
# 登录请求session地址
TOKEN_URL = f"http://{UNISERVER_ADDR}:{UNISERVER_WEB_PORT}/api/system/sessions"
# 提交仪表板元数据地址
METADATA_URL = f"http://{UNISERVER_ADDR}:{UNISERVER_WEB_PORT}/api/views/search/metadata"
# 执行查询获取结果地址
EXECUTE_URL = f"http://{UNISERVER_ADDR}:{UNISERVER_WEB_PORT}/api/views/search/{UNISERVER_DASHBOARD_ID}/execute"

# 聚合结果文件保存路径
SYSTEM_REPORT_ARCH_RESULT_SAVE_PATH = "/opt/system-report"

# 模拟登录请求体
TOKEN_DATA = {
  "username": UNISERVER_USERNAME,
  "password": UNISERVER_PASSWORD,
  "host": f"{UNISERVER_ADDR}:{UNISERVER_WEB_PORT}"
}

# 模拟查询仪表板请求体
EXECUTE_DATA = {
  "global_override": {
    "timerange": {
      "type": "absolute",
      "from": None,
      "to": None
    }
  },
  "parameter_bindings": {}
}

# 模拟任何请求的头
REQUEST_HEADERS = {
  "Accept": "application/json",
  "Accept-Language": "zh-CN,zh;q=0.9",
  "Content-Type": "application/json",
  "X-Requested-By": "XMLHttpRequest",
  "X-Requested-With": "XMLHttpRequest"
}


def format_value(system_name, host, key, value):
  """格式化值，如果是数字则保留2位小数，否则保持原样"""
  # 尝试转换为浮点数
  num = float(value)
  if math.isinf(num):
    print(f'{system_name} {host} has inf host proc data {key} {value}')
  return '{:.2f}'.format(num)

def replace_in_word(template_path, output_path, system_name, replace_dict, url_dict, host_dict):
  # 加载Word文档
  doc = Document(template_path)
  
  # 遍历文档中的所有段落
  for paragraph in doc.paragraphs:
    for old_text, new_text in replace_dict.items():
      if old_text in paragraph.text:
        # 替换文本
        for run in paragraph.runs:
          if old_text in run.text:
            run.text = run.text.replace(old_text, str(new_text))
            if "DEPARTMENT_NAME" in old_text or "SYSTEM_NAME" in old_text:
              run.font.underline = WD_UNDERLINE.NONE
              run.font.size = Pt(26)  # 设置字体大小
            else:
              run.font.underline = WD_UNDERLINE.SINGLE
              run.font.size = Pt(15)  # 设置字体大小
  
  # 需要增加行的表格
  url_4xx_table = None
  url_5xx_table = None
  host_table = None

  # 遍历文档中的所有表格
  for table in doc.tables:
    for row in table.rows:
      for cell in row.cells:
        # 根据表头查找
        if cell.text == '客户端错误接口': 
          url_4xx_table = table
        if cell.text == '服务端错误接口': 
          url_5xx_table = table
        if cell.text == 'CPU核数': 
          host_table = table
        # 全局替换
        for old_text, new_text in replace_dict.items():
          if old_text in cell.text:
            cell.text = cell.text.replace(old_text, str(new_text))

  for url, val_dict in url_dict.items():
    if 'SYSTEM_REPORT_4XX_COUNT' in val_dict: 
      new_row = url_4xx_table.add_row()
      new_row.cells[0].text = system_name
      new_row.cells[1].text = str(val_dict['URL'])
    if 'SYSTEM_REPORT_5XX_COUNT' in val_dict: 
      new_row = url_5xx_table.add_row()
      new_row.cells[0].text = system_name
      new_row.cells[1].text = str(val_dict['URL'])

  for host, val_dict in host_dict.items():
    try:
      new_row = host_table.add_row()
      new_row.cells[0].text = system_name
      new_row.cells[1].text = str(val_dict['HOST'])
      new_row.cells[2].text = format_value(system_name, host, 'SYSTEM_REPORT_CPU_AVG',    val_dict['SYSTEM_REPORT_CPU_AVG'])
      new_row.cells[3].text = format_value(system_name, host, 'SYSTEM_REPORT_CPU_CORE',   val_dict['SYSTEM_REPORT_CPU_CORE'])
      new_row.cells[4].text = format_value(system_name, host, 'SYSTEM_REPORT_MEM_AVG',    val_dict['SYSTEM_REPORT_MEM_AVG'])
      tmp = val_dict['SYSTEM_REPORT_MEM_TOTAL']
      if(tmp > 4) and (tmp < 8): tmp = 8
      if(tmp > 8) and (tmp < 16): tmp = 16
      if(tmp > 16) and (tmp < 32): tmp = 32
      if(tmp > 32) and (tmp < 48): tmp = 48
      if(tmp > 48) and (tmp < 64): tmp = 64
      if(tmp > 64) and (tmp < 96): tmp = 96
      if(tmp > 96) and (tmp < 128): tmp = 128
      new_row.cells[5].text = format_value(system_name, host, 'SYSTEM_REPORT_MEM_TOTAL',  tmp)
      new_row.cells[6].text = format_value(system_name, host, 'SYSTEM_REPORT_DISK_AVG',   val_dict['SYSTEM_REPORT_DISK_AVG'])
      new_row.cells[7].text = format_value(system_name, host, 'SYSTEM_REPORT_DISK_TOTAL', val_dict['SYSTEM_REPORT_DISK_TOTAL'])
    except:
      print(f'{system_name} has error host proc data {val_dict}')

  # 保存修改后的文档
  doc.save(f'{SYSTEM_REPORT_ARCH_RESULT_SAVE_PATH}/{output_path}')

def create_system_report_val_dict(system_name, department_name, start, end):
  start_str = datetime.fromtimestamp(start).strftime("%Y-%m-%d %H:%M:%S")
  end_str   = datetime.fromtimestamp(end).strftime("%Y-%m-%d %H:%M:%S")
  return {
    # 必须字段
    "timestamp": start,
    "message": system_name,
    "host": system_name,
    "SYSTEM_NAME": system_name,
    "DEPARTMENT_NAME": department_name,
    # 归档信息
    "ARCH_SIZE": end - start,
    "ARCH_TYPE": "REPORT",
    "ARCH_START": start_str,
    "ARCH_END": end_str,
    # 可能没有但不能为空字段
    "SYSTEM_REPORT_ERROR_COUNT": 0,
    "SYSTEM_REPORT_4XX_COUNT": 0,
    "SYSTEM_REPORT_5XX_COUNT": 0,
    "SYSTEM_REPORT_UV_COUNT": 0,
    "SYSTEM_REPORT_PV_COUNT": 0,
    "SYSTEM_REPORT_BYTES": 0,
    "SYSTEM_REPORT_RESPONSE_AVG": 0,
    "SYSTEM_REPORT_RESPONSE_MAX": 0,
    # 三级分组信息
    "URL": {},
    "HOST": {}
  }

# 处理仪表板每个小部件中的一行
# 'key': ['********'],
# 'values': [{
# 	'key': ['HOST_SESSION_COUNT'],
# 	'value': 29406,
# 	'rollup': True,
# 	'source': 'row-leaf'
# }, {
def process_system_report_metrics(system_report_metrics, system_report_agg_result):
  # 必须同时存在系统名和部门名
  if len(system_report_metrics['key']) < 2: return

  # 获取系统和部门名称以及键值对字典
  system_name = system_report_metrics['key'][0]
  department_name = system_report_metrics['key'][1]

  # 获取系统和部门名称以及键值对字典
  vals = system_report_metrics['values']

  # 如果不存在创建主机字典
  if system_name not in system_report_agg_result:
    system_report_agg_result[system_name] = create_system_report_val_dict(system_name, department_name, START_TS, END_TS)

  # 获取URL或者IP地址三级分组key并判断类型
  group3_key = None
  group3_key_type = None
  if len(system_report_metrics['key']) == 3: 
    group3_key = system_report_metrics['key'][2]
    if '/' in group3_key: 
      group3_key_type = 'URL'
    else: 
      group3_key_type = 'HOST'
    system_report_agg_result[system_name][group3_key_type][group3_key] = {group3_key_type: group3_key}

  # 遍历指标键值对字典
  for val_dict in vals:
    key = val_dict['key'][0]
    val = val_dict['value']
    if len(system_report_metrics['key']) == 2: 
      system_report_agg_result[system_name][key] = val
    elif len(system_report_metrics['key']) == 3: 
      system_report_agg_result[system_name][group3_key_type][group3_key][key] = val

def save_arch_merge_result(dir_path, agg_result):
  # 目录不存在，创建目录
  if not os.path.exists(dir_path): os.makedirs(dir_path)
  # 保存到文件存储
  filename = datetime.fromtimestamp(START_TS).strftime("%Y-%m-%d %H:%M:%S")
  result_file = f'{dir_path}/{filename}.json'
  with open(result_file, 'w', encoding='utf-8') as file:
    json.dump(agg_result, file, ensure_ascii=False, indent=2)
  # 打印保存主机数量
  logger.info(f'MERGE: save {len(agg_result)} records to {result_file} ...')

# 输入系统查询结果，返回系统聚合结果
def arch_system_report_metrics(system_report_query_result):
  # 系统聚合结果
  system_report_agg_result = {}
  # 获取系统维度仪表板查询结果，遍历仪表板的小部件
  for unit_id, unit_result in system_report_query_result.items():
    # 小部件包括多个系统，每个系统有很多指标
    system_report_metrics_list = unit_result['rows']
    # 遍历每个系统的指标字典
    for system_report_metrics in system_report_metrics_list:
      process_system_report_metrics(system_report_metrics, system_report_agg_result)
  return system_report_agg_result

# 模拟登录查询仪表板
def query_dashboard():
  # 读取仪表板查询JSON
  metadata_data = None
  with open(METADATA_FILE, "r") as f: metadata_data = json.load(f)
  
  # 设置查询时间
  EXECUTE_DATA['global_override']['timerange']['from'] = datetime.utcfromtimestamp(START_TS).isoformat() + "Z"
  EXECUTE_DATA['global_override']['timerange']['to'] = datetime.utcfromtimestamp(END_TS).isoformat() + "Z"
  logger.info(EXECUTE_DATA)
  
  # 模拟登录获取session
  logger.info(f'get token from {TOKEN_URL} ...')
  response = requests.post(TOKEN_URL, headers=REQUEST_HEADERS, json=TOKEN_DATA, verify=False)
  logger.info(f'token response {response.text} ...')
  session_id = json.loads(response.text).get('session_id', None)
  
  # 根据session_id生成token令牌
  uniserver_token = base64.b64encode(f'{session_id}:session'.encode('utf-8')).decode('utf-8')
  REQUEST_HEADERS["Authorization"] = f"Basic {uniserver_token}"
  logger.info(f'token is: {uniserver_token} ...')
  
  # 查询仪表板
  logger.info(f'query {START_TS} to {END_TS} ...')
  requests.post(METADATA_URL, headers=REQUEST_HEADERS, json=metadata_data, verify=False)
  response = requests.post(EXECUTE_URL, headers=REQUEST_HEADERS, json=EXECUTE_DATA, verify=False)
  
  # 解析查询结果
  query_dashboard_result = None
  try:
    query_dashboard_result = json.loads(response.text)
    if "results" not in query_dashboard_result:
      logger.warning(f'no query results in {response.text}')
  except:
    logger.warning(f'failed to load {response.text}')

  return query_dashboard_result

def clean_export_dir():
  if os.path.exists(SYSTEM_REPORT_ARCH_RESULT_SAVE_PATH):
    try:
      # 删除整个目录（包括所有子文件和子目录）
      shutil.rmtree(SYSTEM_REPORT_ARCH_RESULT_SAVE_PATH)
      print(f"已删除目录: {SYSTEM_REPORT_ARCH_RESULT_SAVE_PATH}")
    except Exception as e:
      print(f"删除目录失败: {e}")
  # 重新创建目录
  try:
    os.makedirs(SYSTEM_REPORT_ARCH_RESULT_SAVE_PATH, exist_ok=True)  # exist_ok=True 防止目录已存在时报错
    print(f"已重新创建目录: {SYSTEM_REPORT_ARCH_RESULT_SAVE_PATH}")
  except Exception as e:
    print(f"创建目录失败: {e}")

# 查询仪表板
query_dashboard_result = query_dashboard()

# 处理系统仪表板查询结果
system_report_query_result = query_dashboard_result["results"][UNISERVER_SYSTEM_REPORT_QUERY_ID]["search_types"]
system_report_agg_result = arch_system_report_metrics(system_report_query_result)

# 用400和500数量计算总错误
for system_name, val_dict in system_report_agg_result.items():
  val_dict['SYSTEM_REPORT_ERROR_COUNT'] = val_dict['SYSTEM_REPORT_4XX_COUNT'] + val_dict['SYSTEM_REPORT_5XX_COUNT']

# 发送保存系统聚合结果
save_arch_merge_result(SYSTEM_REPORT_ARCH_RESULT_SAVE_PATH, system_report_agg_result)

# 清理输出目录
clean_export_dir()

# 定义要替换的内容
for system_name, val_dict in system_report_agg_result.items():
  replace_dict = {
      '[SYSTEM_NAME]': val_dict['SYSTEM_NAME'],
      '[DEPARTMENT_NAME]': val_dict['DEPARTMENT_NAME'],
      '[SYSTEM_REPORT_DATE]': datetime.fromtimestamp(val_dict['timestamp']).strftime("%Y年%-m月%-d日"),
      '[SYSTEM_REPORT_START]': datetime.strptime(val_dict['ARCH_START'], "%Y-%m-%d %H:%M:%S").strftime("%Y年%-m月%-d日"),
      '[SYSTEM_REPORT_END]': datetime.strptime(val_dict['ARCH_END'], "%Y-%m-%d %H:%M:%S").strftime("%Y年%-m月%-d日"),
      '[SYSTEM_REPORT_SYSTEM_COUNT]': 1,
      '[SYSTEM_REPORT_HOST_COUNT]': len(val_dict['HOST']),
      '[SYSTEM_REPORT_UV_COUNT]': val_dict['SYSTEM_REPORT_UV_COUNT'],
      '[SYSTEM_REPORT_PV_COUNT]': val_dict['SYSTEM_REPORT_PV_COUNT'],
      '[SYSTEM_REPORT_BYTES]': '{:.2f}'.format(val_dict['SYSTEM_REPORT_BYTES'] / 1024 / 1024 / 1024),
      '[SYSTEM_REPORT_RESPONSE_AVG]': '{:.2f}'.format(val_dict['SYSTEM_REPORT_RESPONSE_AVG']),
      '[SYSTEM_REPORT_RESPONSE_MAX]': '{:.2f}'.format(val_dict['SYSTEM_REPORT_RESPONSE_MAX']),
      '[SYSTEM_REPORT_ERROR_COUNT]': val_dict['SYSTEM_REPORT_ERROR_COUNT'],
      '[SYSTEM_REPORT_4XX_COUNT]': val_dict['SYSTEM_REPORT_4XX_COUNT'],
      '[SYSTEM_REPORT_5XX_COUNT]': val_dict['SYSTEM_REPORT_5XX_COUNT'],
  }

  # 执行替换
  replace_in_word('input.docx', f'{system_name}.docx', system_name, replace_dict, val_dict['URL'], val_dict['HOST'])
