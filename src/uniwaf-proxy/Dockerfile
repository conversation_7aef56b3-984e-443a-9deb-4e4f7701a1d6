FROM registry.jxit.net.cn:5000/alpine:3.19.1 AS builder

RUN sed -i '<EMAIL>@mirrors.tencent.com@g' /etc/apk/repositories && \
    apk update && \
    apk add tcpdump vim curl bash build-base gcc zlib-dev tzdata python3 && \
    apk add automake autoconf libtool make curl-dev libxml2-dev pcre-dev && \
    apk add git linux-headers subversion libmaxminddb-dev geoip-dev yajl-dev pcre2-dev && \
    cp /usr/share/zoneinfo/Asia/Shanghai /etc/localtime && \
    echo "Asia/Shanghai" > /etc/timezone

VOLUME  /ms
WORKDIR /ms

RUN git clone https://gitee.com/laoyang103/ModSecurity && \
    cd ModSecurity && \
    git submodule init && \ 
    git submodule update && \
    ./build.sh && \
    ./configure

RUN cd /ms/ModSecurity && make install && rm -rf /ms/ModSecurity

RUN cd /usr/local && git clone https://github.com/SpiderLabs/ModSecurity-nginx && \
    wget https://openresty.org/download/openresty-********.tar.gz && \
    tar -xzvf openresty-********.tar.gz && \
    cd /usr/local/openresty-******** && \
    ./configure --prefix=/usr/local/openresty --with-luajit --without-http_redis2_module --add-module=/usr/local/ModSecurity-nginx && \
    gmake && gmake install && \
    rm -rf /usr/local/openresty-********* && \
    rm -rf /usr/local/modsecurity/lib/libmodsecurity.a && \
    ln -sf /usr/local/modsecurity/lib/libmodsecurity.so.3.0.12 /usr/local/lib/libmodsecurity.so.3

FROM registry.jxit.net.cn:5000/alpine:3.19.1

RUN sed -i '<EMAIL>@mirrors.tencent.com@g' /etc/apk/repositories && \
    apk update && \
    apk add --no-cache libxml2 pcre zlib libstdc++ yajl libmaxminddb tzdata && \
    apk add --no-cache tcpdump geoip vim curl python3 && \
    cp /usr/share/zoneinfo/Asia/Shanghai /etc/localtime && \
    echo "Asia/Shanghai" > /etc/timezone

WORKDIR /app

COPY --from=builder /usr/local/openresty /usr/local/openresty

COPY --from=builder /usr/local/modsecurity/lib/libmodsecurity.so /usr/local/lib/libmodsecurity.so.3

COPY sendmsg.py /app

COPY modsecurity/ /usr/local/openresty/nginx/conf/modsecurity
