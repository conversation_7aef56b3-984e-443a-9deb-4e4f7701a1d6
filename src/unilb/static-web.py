import os
import http.server
import socketserver
import csv

PORT = 8000
DIRECTORY = "/usr/share/nginx/html/up-install"
IP_FILE = "/usr/share/nginx/html/up-install/allowlist.csv"  # Path to the CSV file containing IPs

# Read allowed IPs from the CSV file
def read_allowed_ips(file_path):
    allowed_ips = set()
    with open(file_path, mode='r') as file:
        csv_reader = csv.reader(file)
        for row in csv_reader:
            if row:  # Skip empty rows
                allowed_ips.add(row[0].strip())  # Add the IP from the first column
    return allowed_ips

class CustomHTTPRequestHandler(http.server.SimpleHTTPRequestHandler):
    def translate_path(self, path):
        if path.startswith("/up-install"): 
            path = path[len("/up-install"):]
        path = os.path.join(DIRECTORY, path.lstrip("/"))
        return path

    def do_GET(self):
        # Fetch the client IP from the custom 'srcip' header
        client_ip = self.headers.get('srcip')
        print(client_ip)
        
        ALLOWED_IPS = read_allowed_ips(IP_FILE)

        # Check if the source IP is allowed
        if not client_ip or client_ip not in ALLOWED_IPS:
            self.send_response(404)
            self.end_headers()
            self.wfile.write(b"404 Not Found")
            return
        
        if self.path == "/up-install": 
            self.path += '/'
        if self.path == "/up-install/centos6": 
            self.path += '/'
        
        return super().do_GET()

Handler = CustomHTTPRequestHandler

with socketserver.TCPServer(("", PORT), Handler) as httpd:
    print(f"Serving HTTP on 0.0.0.0 port {PORT} (http://0.0.0.0:{PORT}/) ...")
    httpd.serve_forever()

