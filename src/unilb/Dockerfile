FROM registry.jxit.net.cn:5000/alpine:3.19.1
# FROM registry.jxit.net.cn:5000/alpine:3.19.1-arm

RUN sed -i '<EMAIL>@mirrors.tencent.com@g' /etc/apk/repositories && \
    apk update && \
    apk add nginx tcpdump vim curl bash build-base && \
    apk add python3 py3-requests py3-kubernetes tzdata && \
    cp /usr/share/zoneinfo/Asia/Shanghai /etc/localtime && \
    echo "Asia/Shanghai" > /etc/timezone

RUN mkdir -p /app/templates

WORKDIR /app

COPY templates/ /app/templates
COPY unidsas.py k8sclient.py udp_forward.c /app

RUN gcc -g -O2 -o /app/udp_forward /app/udp_forward.c
