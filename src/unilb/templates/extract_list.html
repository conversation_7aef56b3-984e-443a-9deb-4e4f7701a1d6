<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <title>Job List</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      background-color: #f4f4f9;
      margin: 0;
      padding: 20px;
    }
    h1 {
      text-align: center;
      color: #333;
    }
    form {
      max-width: 1320px;
      margin: 20px auto;
      padding: 20px;
      background-color: #fff;
      box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
      display: grid;
      grid-template-columns: 1fr 1fr 1fr 1fr;
      grid-gap: 10px;
      align-items: center;
    }
    label {
      margin-bottom: 5px;
      font-weight: bold;
    }
    input[type="text"], input[type="datetime-local"] {
      padding: 8px;
      box-sizing: border-box;
      border: 1px solid #ddd;
      border-radius: 4px;
    }
    input[type="submit"] {
      grid-column: span 4;
      padding: 10px 20px;
      background-color: #4CAF50;
      color: white;
      border: none;
      border-radius: 4px;
      cursor: pointer;
      text-align: center;
    }
    input[type="submit"]:hover {
      background-color: #45a049;
    }
    table {
      width: 100%;
      border-collapse: collapse;
      margin: 20px 0;
      background-color: #fff;
      box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
    }
    th, td {
      padding: 10px;
      border: 1px solid #ddd;
      text-align: left;
    }
    th {
      background-color: #f2f2f2;
      color: #333;
    }
    tr:nth-child(even) {
      background-color: #f9f9f9;
    }
    tr:hover {
      background-color: #f1f1f1;
    }
    .success {
      color: green;
    }
    .failure {
      color: red;
    }
    .tooltip {
      position: relative;
      display: inline-block;
      cursor: pointer;
    }
    .tooltip .tooltiptext {
      visibility: hidden;
      width: 300px;
      background-color: #333;
      color: #fff;
      text-align: center;
      border-radius: 5px;
      padding: 5px 10px;
      position: absolute;
      z-index: 1;
      bottom: 125%;
      left: 50%;
      margin-left: -150px;
      opacity: 0;
      transition: opacity 0.3s;
    }
    .tooltip:hover .tooltiptext {
      visibility: visible;
      opacity: 1;
    }
    .start-time, .task-name {
      width: 20%;
    }
    .status, .command, .download {
      width: 26.67%;
    }
  </style>
</head>
<body>
  <form>
    <div style="grid-column: span 2;">
      <label for="start-time">开始时间</label>
      <input type="datetime-local" id="start-time" name="start-time">
    </div>
    <div style="grid-column: span 2;">
      <label for="end-time">结束时间</label>
      <input type="datetime-local" id="end-time" name="end-time">
    </div>
    <div style="grid-column: span 4;">
      <label for="network-filter">网络过滤</label>
      <input type="text" id="network-filter" name="network-filter">
    </div>
    <div style="grid-column: span 4;">
      <label for="feature-filter">特征过滤</label>
      <input type="text" id="feature-filter" name="feature-filter">
    </div>
    <input type="submit" value="提交">
  </form>
  <table>
    <thead>
      <tr>
        <th class="start-time">开始时间</th>
        <th class="task-name">提取任务</th>
        <th class="status">状态</th>
        <th class="command">命令行</th>
        <th class="download">操作</th>
      </tr>
    </thead>
    <tbody>
      {% for job in jobs %}
      <tr>
        <td>{{ job.start_time|datetimeformat if job.start_time else 'N/A' }}</td>
        <td>{{ job.name }}</td>
        <td class="{% if job.active %}success{% elif job.succeeded %}success{% else %}failure{% endif %}">
          {% if job.active %}
            提取中
          {% elif job.succeeded %}
            提取成功
          {% else %}
            提取失败
          {% endif %}
        </td>
        <td>
          <div class="tooltip">查看命令
            <span class="tooltiptext">{{ job.command }}</span>
          </div>
        </td>
        <td><a href="/api/unidsas/download/{{ job.extract_file }}">下载</a></td>
      </tr>
      {% endfor %}
    </tbody>
  </table>
</body>
</html>

