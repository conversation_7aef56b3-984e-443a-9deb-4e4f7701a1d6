<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <title>文件列表</title>
  <style>
    table {
      width: 100%;
      border-collapse: collapse;
    }
    th, td {
      padding: 10px;
      border: 1px solid #ddd;
    }
    th {
      background-color: #f2f2f2;
    }
    tr:nth-child(even) {
      background-color: #f9f9f9;
    }
  </style>
</head>
<body>
  <h1>文件列表</h1>
  <table>
    <tr>
      <th>文件名</th>
      <th>大小 (字节)</th>
      <th>修改时间</th>
      <th>下载</th>
    </tr>
    {% for file in files %}
    <tr>
      <td>{{ file.name }}</td>
      <td>{{ file.size }}</td>
      <td>{{ file.mtime | datetimeformat }}</td>
      <td><a href="{{ url_for('download_file', filename=file.name) }}">下载</a></td>
    </tr>
    {% endfor %}
  </table>
</body>
</html>

