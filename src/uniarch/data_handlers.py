import os
import json
import socket
import zipfile
import aiofiles
import orjson
from concurrent import futures
from datetime import datetime,UTC
from util import is_chinese,gen_ts_range,ScoreCalculator
from uniserver import UniserverApiManager
from log import logger
import pandas as pd
from collections import defaultdict

def get_unit_system(uniserver_obj:UniserverApiManager,csv_file_path=None):
    unit_system = {}
    for i in range(1, 10):
        try:
            df = uniserver_obj.get_zhuji_csv(csv_file_path)
            if df.empty or df is None:
                continue
            for index,row in df.iterrows():
                try:
                    unit = row.iloc[2].replace('@','')
                    system = row.iloc[1]
                    if unit in unit_system:
                        if system in unit_system[unit]:
                            continue
                        else:
                            unit_system[unit].append(system)
                    else:
                        unit_system[unit] = [system]
                except Exception as e:
                    logger.error(f"generate unit_system reflect Error: {e},row:{row}",exc_info=True)
                    continue
            break
        except Exception as e:
            logger.error(f"get unit_system Error: {e}",exc_info=True)
            continue
    return unit_system

def get_system_name(csv_file_path=None):
    unit_system = {"市规划和自然资源局":{"GZXT-JS-N-029-000587":"一张图","GZXT-JS-N-029-000589":"国土空间","GZXT-JS-N-029-000584":"三调成果管理","GZXT-JS-N-029-000463":"空间数据","GZXT-JS-N-029-000467":"数据统计分析"}}
    for i in range(1, 10):
        try:
            uniserver_obj = UniserverApiManager()
            df = uniserver_obj.get_zhuji_csv(csv_file_path)
            if df.empty or df is None:
                continue
            for index,row in df.iterrows():
                try:
                    # 系统简称
                    system_name = row.iloc[4].replace('@','')
                    # 系统编码
                    system = row.iloc[1]
                    # 单位
                    unit = row.iloc[2]
                    if unit in unit_system:
                        if system in unit_system[unit]:
                            continue
                        else:
                            unit_system[unit][system] = system_name
                    else:
                        unit_system[unit] = {system:system_name}
                except Exception as e:
                    logger.error(f"generate system_name reflect match Error: {e},row:{row}",exc_info=True)
                    continue
            break
        except Exception as e:
            logger.error(f"get system_name Error: {e}",exc_info=True)
            continue
    return unit_system

def get_system_catalogcount(csv_file_path=None):
    system_catalogcount = {}
    for i in range(1, 10):
        try:
            uniserver_obj = UniserverApiManager()
            df = uniserver_obj.get_zhuji_csv(csv_file_path)
            if df.empty or df is None:
                continue
            for index,row in df.iterrows():
                try:
                    # 公共数据资源编目
                    catalogcount = row.iloc[5]
                    # 系统编码
                    system = row.iloc[1]
                    # 单位
                    unit = row.iloc[2]
                    if unit in system_catalogcount:
                        if system in system_catalogcount[unit]:
                            continue
                        else:
                            system_catalogcount[unit][system] = catalogcount
                    else:
                        system_catalogcount[unit] = {system:catalogcount}
                except Exception as e:
                    logger.error(f"generate system_name reflect match Error: {e},row:{row}",exc_info=True)
                    continue
            break
        except Exception as e:
            logger.error(f"get system_name Error: {e}",exc_info=True)
            continue
    return system_catalogcount

def get_host_num_dict(uniserver_obj:UniserverApiManager,csv_file_path=None):
    host_num_dict = {
                    "市规划和自然资源局":
                        {
                        "GZXT-JS-N-029-000467":["0.0.0.0"]*42,
                        "GZXT-JS-N-029-000587":["0.0.0.0"]*42,
                        "GZXT-JS-N-029-000589":["0.0.0.0"]*42,
                        "GZXT-JS-N-029-000584":["0.0.0.0"]*42,
                        "GZXT-JS-N-029-000463":["0.0.0.0"]*42
                        }
                    }
    for i in range(1, 10):
        try:
            df = uniserver_obj.get_zhuji_csv(csv_file_path)
            if df.empty or df is None:
                continue
            for index,row in df.iterrows():
                try:
                    unit = row.iloc[2].replace('@','')
                    system = row.iloc[1]
                    ip = row.iloc[0].split('-')[0]
                    if unit in host_num_dict:
                        if system in host_num_dict[unit]:
                            host_num_dict[unit][system].append(ip)
                        else:
                            host_num_dict[unit][system] = [ip]
                    else:
                        host_num_dict[unit] = {system: [ip]}
                except Exception as e:
                    logger.error(f"generate host_num reflect Error: {e},row:{row}",exc_info=True)
                    continue
            break
        except Exception as e:
            logger.error(f"get host_num_dict Error: {e}",exc_info=True)
            continue
    return host_num_dict

def gen_evaluate_report(system_month_report_df,algrithm= "percent_score_calculator"):
    """
    赋分报告生成
    """
    # 从zhuji.csv获取系统的接入网络类型
    # 根据接入网络类型及指标值进行赋分
    system_evaluate_report = []
    host_network_dict = get_host_network_dict()
    for index,system_row in system_month_report_df.iterrows():
        result_score = ScoreCalculator(system_data = system_row,
                                    system_network_status = host_network_dict.get(system_row.一级单位,{}).get(system_row.系统编号),
                                    algrithm = algrithm).calculate_score()
        system_evaluate_report.append(result_score)
    evaluate_report_df = pd.DataFrame(system_evaluate_report)
    return evaluate_report_df

def handle_host_network(host_network:str):
    system_network_list = host_network.split(",")
    # 取第一个
    system_network = system_network_list[0]
    # 取第二个(用于测试)
    # if len(system_network_list) > 1:
    #     system_network = system_network_list[1]
    if system_network not in ["互联网", "政务外网"] and "业务专网" not in system_network:
        pass
    if "业务专网" in system_network:
        system_network = "政务外网"
    return system_network

def get_host_network_dict(csv_file_path=None):
    host_network_dict = {}
    for i in range(1, 10):
        try:
            uniserver_obj = UniserverApiManager()
            df = uniserver_obj.get_zhuji_csv(csv_file_path)
            if df.empty or df is None:
                continue
            for index,row in df.iterrows():
                try:
                    unit = row.iloc[2]
                    system = row.iloc[1]
                    if pd.isna(row.iloc[3]):
                        network = '政务外网'
                    else:
                        network = handle_host_network(row.iloc[3].replace('@',''))
                    if unit in host_network_dict:
                        host_network_dict[unit][system] = network
                    else:
                        host_network_dict[unit] = {system: network}
                except Exception as e:
                    logger.error(f"generate host_network reflect Error: {e},row:{row}",exc_info=True)
                    continue
            break
        except Exception as e:
            logger.error(f"get host_netowrk_dict Error: {e}",exc_info=True)
            continue
    return host_network_dict

async def merge_single_json_file(system_data,json_data,dimension,host_cpu_avg_threashold):
    """
    ## 按照指定维度聚合单个json文件
    :param system_data: 系统数据
    :param json_data: json数据
    :param dimension: 维度
    :param host_cpu_avg_threashold: 主机CPU平均阈值
    """
    sample_entry = list(next(iter(json_data.values())).values())[0] if json_data else None
    if sample_entry:
        start_time = datetime.strptime(sample_entry['ARCH_START'], '%Y-%m-%d %H:%M:%S')
        end_time = datetime.strptime(sample_entry['ARCH_END'], '%Y-%m-%d %H:%M:%S')
        time_window = ((end_time - start_time).total_seconds()+1) / 60
        if dimension == "hour":
            key = end_time.strftime('%Y-%m-%d %H:00:00')
        elif dimension == 'day':
            key = end_time.strftime('%Y-%m-%d')
        elif dimension == 'month':
            key = end_time.strftime('%Y-%m')
        else:
            raise Exception("Invalid dimension")
    else:
        return system_data  # 跳过空文件

    system_metrics = json_data.get("系统访问总量、系统数据流量", {})
    for entry in system_metrics.values():
        system_name = entry['SYSTEM_CODE'].replace("SJK_","")
        dept_name = entry['UNIT_NAME']
        system_key = f"{dept_name}|{system_name}"
        system_record = system_data[key][system_key]
        system_record['UNIT_NAME'] = dept_name
        system_record['SYSTEM_CODE'] = entry['SYSTEM_CODE'].replace("SJK_","")
        system_record['SYSTEM_VISIT_TOTAL'] += entry.get('SYSTEM_VISIT_TOTAL', 0)
        system_record['SYSTEM_VISIT_TOTAL_BYTES'] += entry.get('SYSTEM_VISIT_TOTAL_BYTES', 0.0)
        system_record['SYSTEM_NAME'] = ""
        system_record['HOST_NUM'] = entry.get('HOST_NUM', 0)

    db_metrics = json_data.get("数据库访问量、数据库访问流量", {})
    for entry in db_metrics.values():
        system_name = entry['SYSTEM_CODE'].replace("SJK_","")
        # GZXT-JS-N-042-000859 每天只统计08:00:00-18:00:00
        if system_name =="GZXT-JS-N-042-000859" and (start_time.hour < 8 or end_time.hour >= 18):
            continue
        dept_name = entry['UNIT_NAME']
        system_key = f"{dept_name}|{system_name}"
        system_record = system_data[key][system_key]
        system_record['UNIT_NAME'] = dept_name
        system_record['SYSTEM_CODE'] = entry['SYSTEM_CODE'].replace("SJK_","")
        system_record['DATABASE_VISIT_TOTAL'] += entry.get('DATABASE_VISIT_TOTAL', 0)
        system_record['DATABASE_VISIT_TOTAL_BYTES'] += entry.get('DATABASE_VISIT_TOTAL_BYTES', 0.0)
        system_record['SYSTEM_NAME'] = ""
        system_record['HOST_NUM'] = entry.get('HOST_NUM', 0)

    system_metrics = json_data.get("K8s系统访问量/流量", {})
    for entry in system_metrics.values():
        system_name = entry['SYSTEM_CODE'].replace("SJK_","")
        dept_name = entry['UNIT_NAME']
        system_key = f"{dept_name}|{system_name}"
        system_record = system_data[key][system_key]
        system_record['UNIT_NAME'] = dept_name
        system_record['SYSTEM_CODE'] = entry['SYSTEM_CODE'].replace("SJK_","")
        system_record['SYSTEM_VISIT_TOTAL'] += entry.get('SYSTEM_VISIT_TOTAL', 0)
        system_record['SYSTEM_VISIT_TOTAL_BYTES'] += entry.get('SYSTEM_VISIT_TOTAL_BYTES', 0.0)
        system_record['SYSTEM_NAME'] = ""
        system_record['HOST_NUM'] = entry.get('HOST_NUM', 0)

    db_metrics = json_data.get("数据库TCP", {})
    for entry in db_metrics.values():
        system_name = entry['SYSTEM_CODE'].replace("SJK_","")
        # GZXT-JS-N-042-000859 每天只统计08:00:00-18:00:00
        if system_name =="GZXT-JS-N-042-000859" and (start_time.hour < 8 or end_time.hour >= 18):
            continue
        dept_name = entry['UNIT_NAME']
        system_key = f"{dept_name}|{system_name}"
        system_record = system_data[key][system_key]
        system_record['UNIT_NAME'] = dept_name
        system_record['SYSTEM_CODE'] = entry['SYSTEM_CODE'].replace("SJK_","")
        system_record['DATABASE_ACK_PACKAGES_TOTAL'] += entry.get('DATABASE_ACK_PACKAGES_TOTAL', 0)
        system_record['DATABASE_BYTES_TOTAL'] += entry.get('DATABASE_BYTES_TOTAL', 0.0)
        system_record['SYSTEM_NAME'] = system_name
        system_record['HOST_NUM'] = entry.get('HOST_NUM', 0)
    
    db_metrics = json_data.get("共用数据库SQL统计", {})
    for entry in db_metrics.values():
        # GZXT-JS-N-042-000859 每天只统计08:00:00-18:00:00
        if system_name =="GZXT-JS-N-042-000859" and (start_time.hour < 8 or end_time.hour >= 18):
            continue
        system_name = entry['SYSTEM_CODE'].replace("SJK_","")
        dept_name = entry['UNIT_NAME']
        system_key = f"{dept_name}|{system_name}"
        system_record = system_data[key][system_key]
        system_record['UNIT_NAME'] = dept_name
        system_record['SYSTEM_CODE'] = entry['SYSTEM_CODE'].replace("SJK_","")
        system_record['DATABASE_VISIT_TOTAL'] += entry.get('DATABASE_VISIT_TOTAL', 0)
        system_record['DATABASE_VISIT_TOTAL_BYTES'] += entry.get('DATABASE_VISIT_TOTAL_BYTES', 0.0)
        system_record['SYSTEM_NAME'] = ""
        system_record['HOST_NUM'] = entry.get('HOST_NUM', 0)

    db_metrics = json_data.get("共用数据库TCP统计", {})
    for entry in db_metrics.values():
        system_name = entry['SYSTEM_CODE'].replace("SJK_","")
        # GZXT-JS-N-042-000859 每天只统计08:00:00-18:00:00
        if system_name =="GZXT-JS-N-042-000859" and (start_time.hour < 8 or end_time.hour >= 18):
            continue
        dept_name = entry['UNIT_NAME']
        system_key = f"{dept_name}|{system_name}"
        system_record = system_data[key][system_key]
        system_record['UNIT_NAME'] = dept_name
        system_record['SYSTEM_CODE'] = entry['SYSTEM_CODE'].replace("SJK_","")
        system_record['DATABASE_ACK_PACKAGES_TOTAL'] += entry.get('DATABASE_ACK_PACKAGES_TOTAL', 0)
        system_record['DATABASE_BYTES_TOTAL'] += entry.get('DATABASE_BYTES_TOTAL', 0.0)
        system_record['SYSTEM_NAME'] = system_name
        system_record['HOST_NUM'] = entry.get('HOST_NUM', 0)

    db_metrics = json_data.get("长链接数据库包统计", {})
    for entry in db_metrics.values():
        system_name = entry['SYSTEM_CODE'].replace("SJK_","")
        # GZXT-JS-N-042-000859 每天只统计08:00:00-18:00:00
        if system_name =="GZXT-JS-N-042-000859" and (start_time.hour < 8 or end_time.hour >= 18):
            continue
        dept_name = entry['UNIT_NAME']
        system_key = f"{dept_name}|{system_name}"
        system_record = system_data[key][system_key]
        system_record['UNIT_NAME'] = dept_name
        system_record['SYSTEM_CODE'] = entry['SYSTEM_CODE'].replace("SJK_","")
        system_record['DATABASE_PACKAGES_TOTAL'] += entry.get('DATABASE_PACKAGES_TOTAL', 0)
        system_record['DATABASE_BYTES_TOTAL'] += entry.get('DATABASE_BYTES_TOTAL', 0.0)
        system_record['SYSTEM_NAME'] = system_name
        system_record['HOST_NUM'] = entry.get('HOST_NUM', 0)

    db_metrics = json_data.get("额外系统访问统计", {})
    for entry in db_metrics.values():
        system_name = entry['SYSTEM_CODE'].replace("SJK_","")
        # GZXT-JS-N-042-000859 每天只统计08:00:00-18:00:00
        if system_name =="GZXT-JS-N-042-000859" and (start_time.hour < 8 or end_time.hour >= 18):
            continue
        dept_name = entry['UNIT_NAME']
        system_key = f"{dept_name}|{system_name}"
        system_record = system_data[key][system_key]
        system_record['UNIT_NAME'] = dept_name
        system_record['SYSTEM_CODE'] = entry['SYSTEM_CODE'].replace("SJK_","")
        system_record['SYSTEM_VISIT_TOTAL'] += entry.get('SYSTEM_VISIT_TOTAL', 0)
        system_record['SYSTEM_VISIT_TOTAL_BYTES'] += entry.get('SYSTEM_VISIT_TOTAL_BYTES', 0)
        system_record['SYSTEM_NAME'] = system_name
        system_record['HOST_NUM'] = entry.get('HOST_NUM', 0)

    cpu_metrics = json_data.get("CPU平均值", {})
    for entry in cpu_metrics.values():
        system_name = entry['SYSTEM_CODE'].replace("SJK_","")
        dept_name = entry['UNIT_NAME']
        HOST_CPU_AVG = entry.get('HOST_CPU_AVG', 0)
        if HOST_CPU_AVG == 'Infinity':
            HOST_CPU_AVG = 0
        system_key = f"{dept_name}|{system_name}"
        system_record = system_data[key][system_key]
        system_record['UNIT_NAME'] = dept_name
        system_record['SYSTEM_CODE'] = entry['SYSTEM_CODE'].replace("SJK_","")
        system_record['SYSTEM_NAME'] = ""
        system_record['HOST_NUM'] = entry.get('HOST_NUM', 0)
        if HOST_CPU_AVG > host_cpu_avg_threashold:
            system_record['CPU_ACTIVE_MINUTES'] += time_window

    dbopr_metrics = json_data.get("数据库操作量（增删改）", {})
    for entry in dbopr_metrics.values():
        system_name = entry['SYSTEM_CODE'].replace("SJK_","")
        # GZXT-JS-N-042-000859 每天只统计08:00:00-18:00:00
        if system_name =="GZXT-JS-N-042-000859" and (start_time.hour < 8 or end_time.hour >= 18):
            continue
        dept_name = entry['UNIT_NAME']
        system_key = f"{dept_name}|{system_name}"
        system_record = system_data[key][system_key]
        system_record['UNIT_NAME'] = dept_name
        system_record['SYSTEM_CODE'] = entry['SYSTEM_CODE'].replace("SJK_","")
        system_record['SYSTEM_NAME'] = ""
        system_record['DATABASE_OPR'] +=  entry['DATABASE_OPR']
        system_record['HOST_NUM'] = entry.get('HOST_NUM', 0)

async def merge_single_system_hour_json_file(system_data,json_data,host_cpu_avg_threashold):
    """
    ## 按照指定维度聚合单个json文件
    :param system_data: 系统数据
    :param json_data: json数据
    :param dimension: 维度
    :param host_cpu_avg_threashold: 主机CPU平均阈值
    """
    sample_entry = list(next(iter(json_data.values())).values())[0] if json_data else None
    if sample_entry:
        start_time = datetime.strptime(sample_entry['ARCH_START'], '%Y-%m-%d %H:%M:%S')
        end_time = datetime.strptime(sample_entry['ARCH_END'], '%Y-%m-%d %H:%M:%S')
        time_window = ((end_time - start_time).total_seconds()+1) / 60
        time_key = end_time.strftime('%Y-%m-%d %H:00:00')
        day_key = time_key[:10]
        hour_key = time_key[11:]
    else:
        return system_data  # 跳过空文件

    system_metrics = json_data.get("系统访问总量、系统数据流量", {})
    for entry in system_metrics.values():
        system_name = entry['SYSTEM_CODE'].replace("SJK_","")
        dept_name = entry['UNIT_NAME']
        system_key = f"{dept_name}|{system_name}"
        system_record = system_data[system_key][day_key][hour_key]
        system_record['UNIT_NAME'] = dept_name
        system_record['SYSTEM_CODE'] = entry['SYSTEM_CODE'].replace("SJK_","")
        system_record['SYSTEM_VISIT_TOTAL'] += entry.get('SYSTEM_VISIT_TOTAL', 0)
        system_record['SYSTEM_VISIT_TOTAL_BYTES'] += entry.get('SYSTEM_VISIT_TOTAL_BYTES', 0.0)
        system_record['SYSTEM_NAME'] = ""
        system_record['HOST_NUM'] = entry.get('HOST_NUM', 0)

    db_metrics = json_data.get("数据库访问量、数据库访问流量", {})
    for entry in db_metrics.values():
        system_name = entry['SYSTEM_CODE'].replace("SJK_","")
        # GZXT-JS-N-042-000859 每天只统计08:00:00-18:00:00
        if system_name =="GZXT-JS-N-042-000859" and (start_time.hour < 8 or end_time.hour >= 18):
            continue
        dept_name = entry['UNIT_NAME']
        system_key = f"{dept_name}|{system_name}"
        system_record = system_data[system_key][day_key][hour_key]
        system_record['UNIT_NAME'] = dept_name
        system_record['SYSTEM_CODE'] = entry['SYSTEM_CODE'].replace("SJK_","")
        system_record['DATABASE_VISIT_TOTAL'] += entry.get('DATABASE_VISIT_TOTAL', 0)
        system_record['DATABASE_VISIT_TOTAL_BYTES'] += entry.get('DATABASE_VISIT_TOTAL_BYTES', 0.0)
        system_record['SYSTEM_NAME'] = ""
        system_record['HOST_NUM'] = entry.get('HOST_NUM', 0)

    system_metrics = json_data.get("K8s系统访问量/流量", {})
    for entry in system_metrics.values():
        system_name = entry['SYSTEM_CODE'].replace("SJK_","")
        dept_name = entry['UNIT_NAME']
        system_key = f"{dept_name}|{system_name}"
        system_record = system_data[system_key][day_key][hour_key]
        system_record['UNIT_NAME'] = dept_name
        system_record['SYSTEM_CODE'] = entry['SYSTEM_CODE'].replace("SJK_","")
        system_record['SYSTEM_VISIT_TOTAL'] += entry.get('SYSTEM_VISIT_TOTAL', 0)
        system_record['SYSTEM_VISIT_TOTAL_BYTES'] += entry.get('SYSTEM_VISIT_TOTAL_BYTES', 0.0)
        system_record['SYSTEM_NAME'] = ""
        system_record['HOST_NUM'] = entry.get('HOST_NUM', 0)

    db_metrics = json_data.get("数据库TCP", {})
    for entry in db_metrics.values():
        system_name = entry['SYSTEM_CODE'].replace("SJK_","")
        # GZXT-JS-N-042-000859 每天只统计08:00:00-18:00:00
        if system_name =="GZXT-JS-N-042-000859" and (start_time.hour < 8 or end_time.hour >= 18):
            continue
        dept_name = entry['UNIT_NAME']
        system_key = f"{dept_name}|{system_name}"
        system_record = system_data[system_key][day_key][hour_key]
        system_record['UNIT_NAME'] = dept_name
        system_record['SYSTEM_CODE'] = entry['SYSTEM_CODE'].replace("SJK_","")
        system_record['DATABASE_ACK_PACKAGES_TOTAL'] += entry.get('DATABASE_ACK_PACKAGES_TOTAL', 0)
        system_record['DATABASE_BYTES_TOTAL'] += entry.get('DATABASE_BYTES_TOTAL', 0.0)
        system_record['SYSTEM_NAME'] = system_name
        system_record['HOST_NUM'] = entry.get('HOST_NUM', 0)
    
    db_metrics = json_data.get("共用数据库SQL统计", {})
    for entry in db_metrics.values():
        # GZXT-JS-N-042-000859 每天只统计08:00:00-18:00:00
        if system_name =="GZXT-JS-N-042-000859" and (start_time.hour < 8 or end_time.hour >= 18):
            continue
        system_name = entry['SYSTEM_CODE'].replace("SJK_","")
        dept_name = entry['UNIT_NAME']
        system_key = f"{dept_name}|{system_name}"
        system_record = system_data[system_key][day_key][hour_key]
        system_record['UNIT_NAME'] = dept_name
        system_record['SYSTEM_CODE'] = entry['SYSTEM_CODE'].replace("SJK_","")
        system_record['DATABASE_VISIT_TOTAL'] += entry.get('DATABASE_VISIT_TOTAL', 0)
        system_record['DATABASE_VISIT_TOTAL_BYTES'] += entry.get('DATABASE_VISIT_TOTAL_BYTES', 0.0)
        system_record['SYSTEM_NAME'] = ""
        system_record['HOST_NUM'] = entry.get('HOST_NUM', 0)

    db_metrics = json_data.get("共用数据库TCP统计", {})
    for entry in db_metrics.values():
        system_name = entry['SYSTEM_CODE'].replace("SJK_","")
        # GZXT-JS-N-042-000859 每天只统计08:00:00-18:00:00
        if system_name =="GZXT-JS-N-042-000859" and (start_time.hour < 8 or end_time.hour >= 18):
            continue
        dept_name = entry['UNIT_NAME']
        system_key = f"{dept_name}|{system_name}"
        system_record = system_data[system_key][day_key][hour_key]
        system_record['UNIT_NAME'] = dept_name
        system_record['SYSTEM_CODE'] = entry['SYSTEM_CODE'].replace("SJK_","")
        system_record['DATABASE_ACK_PACKAGES_TOTAL'] += entry.get('DATABASE_ACK_PACKAGES_TOTAL', 0)
        system_record['DATABASE_BYTES_TOTAL'] += entry.get('DATABASE_BYTES_TOTAL', 0.0)
        system_record['SYSTEM_NAME'] = system_name
        system_record['HOST_NUM'] = entry.get('HOST_NUM', 0)

    db_metrics = json_data.get("长链接数据库包统计", {})
    for entry in db_metrics.values():
        system_name = entry['SYSTEM_CODE'].replace("SJK_","")
        # GZXT-JS-N-042-000859 每天只统计08:00:00-18:00:00
        if system_name =="GZXT-JS-N-042-000859" and (start_time.hour < 8 or end_time.hour >= 18):
            continue
        dept_name = entry['UNIT_NAME']
        system_key = f"{dept_name}|{system_name}"
        system_record = system_data[system_key][day_key][hour_key]
        system_record['UNIT_NAME'] = dept_name
        system_record['SYSTEM_CODE'] = entry['SYSTEM_CODE'].replace("SJK_","")
        system_record['DATABASE_PACKAGES_TOTAL'] += entry.get('DATABASE_PACKAGES_TOTAL', 0)
        system_record['DATABASE_BYTES_TOTAL'] += entry.get('DATABASE_BYTES_TOTAL', 0.0)
        system_record['SYSTEM_NAME'] = system_name
        system_record['HOST_NUM'] = entry.get('HOST_NUM', 0)

    db_metrics = json_data.get("额外系统访问统计", {})
    for entry in db_metrics.values():
        system_name = entry['SYSTEM_CODE'].replace("SJK_","")
        # GZXT-JS-N-042-000859 每天只统计08:00:00-18:00:00
        if system_name =="GZXT-JS-N-042-000859" and (start_time.hour < 8 or end_time.hour >= 18):
            continue
        dept_name = entry['UNIT_NAME']
        system_key = f"{dept_name}|{system_name}"
        system_record = system_data[system_key][day_key][hour_key]
        system_record['UNIT_NAME'] = dept_name
        system_record['SYSTEM_CODE'] = entry['SYSTEM_CODE'].replace("SJK_","")
        system_record['SYSTEM_VISIT_TOTAL'] += entry.get('SYSTEM_VISIT_TOTAL', 0)
        system_record['SYSTEM_VISIT_TOTAL_BYTES'] += entry.get('SYSTEM_VISIT_TOTAL_BYTES', 0)
        system_record['SYSTEM_NAME'] = system_name
        system_record['HOST_NUM'] = entry.get('HOST_NUM', 0)

    cpu_metrics = json_data.get("CPU平均值", {})
    for entry in cpu_metrics.values():
        system_name = entry['SYSTEM_CODE'].replace("SJK_","")
        dept_name = entry['UNIT_NAME']
        HOST_CPU_AVG = entry.get('HOST_CPU_AVG', 0)
        if HOST_CPU_AVG == 'Infinity':
            HOST_CPU_AVG = 0
        system_key = f"{dept_name}|{system_name}"
        system_record = system_data[system_key][day_key][hour_key]
        system_record['UNIT_NAME'] = dept_name
        system_record['SYSTEM_CODE'] = entry['SYSTEM_CODE'].replace("SJK_","")
        system_record['SYSTEM_NAME'] = ""
        system_record['HOST_NUM'] = entry.get('HOST_NUM', 0)
        if HOST_CPU_AVG > host_cpu_avg_threashold:
            system_record['CPU_ACTIVE_MINUTES'] += time_window

    dbopr_metrics = json_data.get("数据库操作量（增删改）", {})
    for entry in dbopr_metrics.values():
        system_name = entry['SYSTEM_CODE'].replace("SJK_","")
        # GZXT-JS-N-042-000859 每天只统计08:00:00-18:00:00
        if system_name =="GZXT-JS-N-042-000859" and (start_time.hour < 8 or end_time.hour >= 18):
            continue
        dept_name = entry['UNIT_NAME']
        system_key = f"{dept_name}|{system_name}"
        system_record = system_data[system_key][day_key][hour_key]
        system_record['UNIT_NAME'] = dept_name
        system_record['SYSTEM_CODE'] = entry['SYSTEM_CODE'].replace("SJK_","")
        system_record['SYSTEM_NAME'] = ""
        system_record['DATABASE_OPR'] +=  entry['DATABASE_OPR']
        system_record['HOST_NUM'] = entry.get('HOST_NUM', 0)

async def process_single_json_file(semaphore,file_path):
    """
    ## 处理单个JSON文件
    :param file_path: JSON文件路径
    :return: json数据
    """
    try:
        async with semaphore:
            async with aiofiles.open(file_path, 'rb') as f:
                f_data = await f.read()
                data = orjson.loads(f_data)
                return data
    except Exception as e:
        logger.error(f"Error processing file {file_path}: {e}",exc_info=True)
    return None

def merge_system_data(system_data, local_system_data):
    for key, systems in local_system_data.items():
        for system_key, data in systems.items():
            record = system_data[key][system_key]
            for k, v in data.items():
                if isinstance(v, (int, float)):
                    record[k] += v
                else:
                    record[k] = v

def cal_bytes_unit(num:float):
    """
    ## 计算字节单位
    :param num: 字节数
    :return: 字节数转换后的带单位字符串
    """
    if num > 1024*1024*1024*1024*1024*1024*1024*1024:
        num = f"{round(num/1024/1024/1024/1024/1024/1024/1024/1024,2)} YB"
    elif num > 1024*1024*1024*1024*1024*1024*1024:
        num = f"{round(num/1024/1024/1024/1024/1024/1024/1024,2)} ZB"
    elif num > 1024*1024*1024*1024*1024*1024:
        num = f"{round(num/1024/1024/1024/1024/1024/1024,2)} EB"
    elif num > 1024*1024*1024*1024*1024:
        num = f"{round(num/1024/1024/1024/1024/1024,2)} PB"
    elif num > 1024*1024*1024*1024:
        num = f"{round(num/1024/1024/1024/1024,2)} TB"
    elif num > 1024*1024*1024:
        num = f"{round(num/1024/1024/1024,2)} GB"
    elif num > 1024*1024:
        num = f"{round(num/1024/1024,2)} MB"
    elif num > 1024:
        num = f"{round(num/1024,2)} KB"
    else:
        num = f"{num} B"
    return num

async def network_field_decision(system_data):
    """
    ## 将小时数据按天透视聚合,根据当日总和决定取7层还是4层,生成配置返回
    """
    network_field_decision_config = defaultdict(lambda: defaultdict(lambda: {
        'pkg_field': '',
        'byte_field':''
        })) 
    try:
        report_df_total = pd.DataFrame([
                            {"日期": d, "时间": t, **fields}
                            for _, dates in system_data.items()
                            for d, times in dates.items()
                            for t, fields in times.items()
                        ])
        # 通过透视按天聚合
        report_df = pd.pivot_table(report_df_total, index=["日期","SYSTEM_CODE"],values=["CPU_ACTIVE_MINUTES","DATABASE_ACK_PACKAGES_TOTAL","DATABASE_BYTES_TOTAL","DATABASE_OPR","DATABASE_PACKAGES_TOTAL","DATABASE_VISIT_TOTAL","DATABASE_VISIT_TOTAL_BYTES","SYSTEM_NAME","SYSTEM_VISIT_TOTAL","SYSTEM_VISIT_TOTAL_BYTES"],aggfunc="sum").reset_index()
        # 生成每个系统每日按7层还是4层取值决策配置{系统编号:{日期：{"pkg_field":"访问量字段","byte_field":"流量字段"}}}}
        for index,data in report_df.iterrows():
            system_code = data["SYSTEM_CODE"]
            date = data["日期"]
            # 数据库访问量
            if data['DATABASE_VISIT_TOTAL'] == 0 and data['DATABASE_VISIT_TOTAL_BYTES'] == 0:
                # 如果数据库加密，取4层流量
                if data['DATABASE_ACK_PACKAGES_TOTAL'] == 0:
                    # 长连接数据库访问量统计采用5个包约合一个SQL的方式
                    network_field_decision_config[system_code][date]["pkg_field"] = 'DATABASE_PACKAGES_TOTAL'
                else:
                    network_field_decision_config[system_code][date]["pkg_field"] = 'DATABASE_ACK_PACKAGES_TOTAL'
                network_field_decision_config[system_code][date]["byte_field"] = 'DATABASE_BYTES_TOTAL'
            else:
                network_field_decision_config[system_code][date]["pkg_field"] = 'DATABASE_VISIT_TOTAL'
                network_field_decision_config[system_code][date]["byte_field"] = 'DATABASE_VISIT_TOTAL_BYTES'
    except Exception as e:
        logger.error(f"network_field_decision Error: {e}",exc_info=True)
    return network_field_decision_config

async def make_hour_dataframe(system_data,output_system,ratio,network_field_decision_config,byte_unit=""):
    """
    ## 将json数据转换为DataFrame
    ## 参数
    - system_data: json数据
    - output_system: 输出的系统名称
    - dimension: 输出的维度
    - ratio: 输入数据的比例
    """
    system_records = []
    system_name_dict = get_system_name("/root/uniarch/uniserver/src/uniarch/zhuji.csv")
    system_catalogcount = get_system_catalogcount("/root/uniarch/uniserver/src/uniarch/zhuji.csv")
    for system_key,day_data  in system_data.items():
        for day_key, hour_data in day_data.items():
            for hour_key, data in hour_data.items():
                if output_system and data['SYSTEM_CODE'] not in output_system:
                    continue
                record = {"日期":day_key,"时间":f"{hour_key[:2]}:00:00"}
                # # 获取主机数
                host_num = data.get('HOST_NUM',0)
                network_field_config = network_field_decision_config.get(data['SYSTEM_CODE'],{}).get(day_key,{"pkg_field":"DATABASE_VISIT_TOTAL","byte_field":"DATABASE_VISIT_TOTAL_BYTES"})
                # 数据库访问量
                if network_field_config["pkg_field"] == "DATABASE_PACKAGES_TOTAL":
                    DATABASE_VISIT_TOTAL = int(data['DATABASE_PACKAGES_TOTAL']/5)
                elif network_field_config["pkg_field"] == "DATABASE_VISIT_TOTAL":
                    DATABASE_VISIT_TOTAL = data['DATABASE_VISIT_TOTAL']*ratio
                else:
                    DATABASE_VISIT_TOTAL = data[network_field_config["pkg_field"]]
                # 数据库访问量流量
                if network_field_config["byte_field"] == "DATABASE_VISIT_TOTAL_BYTES":
                    DATABASE_VISIT_TOTAL_BYTES = data['DATABASE_VISIT_TOTAL_BYTES']*ratio
                else:
                    DATABASE_VISIT_TOTAL_BYTES = data[network_field_config["byte_field"]]

                record.update({
                    '一级单位': data['UNIT_NAME'],
                    '系统编号': data['SYSTEM_CODE'],
                    '系统名称': system_name_dict.get(data['UNIT_NAME'],{}).get(data['SYSTEM_CODE'],""),
                    '主机数量':host_num,
                    '数据库操作量':data["DATABASE_OPR"],
                    '公共数据资源目录编目': system_catalogcount.get(data['UNIT_NAME'],{}).get(data['SYSTEM_CODE'],0)
                })
                if byte_unit == "MB":
                        record.update({
                        '系统访问总量': data['SYSTEM_VISIT_TOTAL'],
                        '系统数据流量(MB)': data["SYSTEM_VISIT_TOTAL_BYTES"]/1024/1024,
                        '数据库访问量': DATABASE_VISIT_TOTAL,
                        '数据库访问流量(MB)': DATABASE_VISIT_TOTAL_BYTES/1024/1024,
                    })
                elif byte_unit == "Byte":
                    record.update({
                        '系统访问总量': data['SYSTEM_VISIT_TOTAL'],
                        '系统数据流量(字节)': data["SYSTEM_VISIT_TOTAL_BYTES"],
                        '数据库访问量': DATABASE_VISIT_TOTAL,
                        '数据库访问流量(字节)': DATABASE_VISIT_TOTAL_BYTES,
                    })
                else:
                    record.update({
                        '系统访问总量': data['SYSTEM_VISIT_TOTAL'],
                        '系统数据流量': cal_bytes_unit(data["SYSTEM_VISIT_TOTAL_BYTES"]),
                        '数据库访问量': DATABASE_VISIT_TOTAL,
                        '数据库访问流量': cal_bytes_unit(DATABASE_VISIT_TOTAL_BYTES),
                    })
                # 主机数为0时不显示CPU活跃时长
                record['虚拟CPU(vCPU)活跃累计时长'] = round(data['CPU_ACTIVE_MINUTES'],2) if host_num > 0 else 0
                system_records.append(record)
    if len(system_records) == 0:
        return pd.DataFrame()
    system_df = pd.DataFrame(system_records)
    
    # 按时间排序
    system_df = system_df.sort_values("日期", ignore_index=True)
    
    return system_df

async def json_to_dataframe(system_data,output_system,dimension,ratio,byte_unit=""):
    """
    ## 将json数据转换为DataFrame
    ## 参数
    - system_data: json数据
    - output_system: 输出的系统名称
    - dimension: 输出的维度
    - ratio: 输入数据的比例
    """
    system_records = []
    system_name_dict = get_system_name()
    for key, systems in system_data.items():
        for system_key, data in systems.items():
            if output_system and data['SYSTEM_CODE'] not in output_system:
                continue
            if dimension == "hour":
                record = {"日期":key[:10],"时间":f"{key[11:13]}:00:00"}
            elif dimension == "day":
                record = {"日期":key[:10],"时间":"00:00:00-23:59:59"}
            elif dimension == "month":
                record = {'日期(年月)': key}
            # # 获取主机数
            host_num = data.get('HOST_NUM',0)
            # 数据库访问量
            if data['DATABASE_VISIT_TOTAL'] == 0 and data['DATABASE_VISIT_TOTAL_BYTES'] == 0:
                # 如果数据库加密，取4层流量
                if data['DATABASE_ACK_PACKAGES_TOTAL'] == 0:
                    # 长连接数据库访问量统计采用5个包约合一个SQL的方式
                    DATABASE_VISIT_TOTAL = int(data['DATABASE_PACKAGES_TOTAL']/5)
                else:
                    DATABASE_VISIT_TOTAL = data['DATABASE_ACK_PACKAGES_TOTAL']
                DATABASE_VISIT_TOTAL_BYTES = data['DATABASE_BYTES_TOTAL']
            else:
                # 取7层SQL需要乘采样率
                DATABASE_VISIT_TOTAL = data['DATABASE_VISIT_TOTAL']*ratio
                # 数据库访问流量
                DATABASE_VISIT_TOTAL_BYTES = data['DATABASE_VISIT_TOTAL_BYTES']*ratio
            record.update({
                '一级单位': data['UNIT_NAME'],
                '系统编号': data['SYSTEM_CODE'],
                '系统名称': system_name_dict.get(data['UNIT_NAME'],{}).get(data['SYSTEM_CODE'],""),
                '主机数量':host_num,
                '数据库操作量':data["DATABASE_OPR"],
            })
            if byte_unit == "MB":
                    record.update({
                    '系统访问总量': data['SYSTEM_VISIT_TOTAL'],
                    '系统数据流量(MB)': data["SYSTEM_VISIT_TOTAL_BYTES"]/1024/1024,
                    '数据库访问量': DATABASE_VISIT_TOTAL,
                    '数据库访问流量(MB)': DATABASE_VISIT_TOTAL_BYTES/1024/1024,
                })
            elif byte_unit == "Byte":
                record.update({
                    '系统访问总量': data['SYSTEM_VISIT_TOTAL'],
                    '系统数据流量(字节)': data["SYSTEM_VISIT_TOTAL_BYTES"],
                    '数据库访问量': DATABASE_VISIT_TOTAL,
                    '数据库访问流量(字节)': DATABASE_VISIT_TOTAL_BYTES,
                    '数据库操作量': data['DATABASE_OPR'],
                })
            else:
                record.update({
                    '系统访问总量': data['SYSTEM_VISIT_TOTAL'],
                    '系统数据流量': cal_bytes_unit(data["SYSTEM_VISIT_TOTAL_BYTES"]),
                    '数据库访问量': DATABASE_VISIT_TOTAL,
                    '数据库访问流量': cal_bytes_unit(DATABASE_VISIT_TOTAL_BYTES),
                })
            # 主机数为0时不显示CPU活跃时长
            record['虚拟CPU(vCPU)活跃累计时长'] = round(data['CPU_ACTIVE_MINUTES'],2) if host_num > 0 else 0
            system_records.append(record)
    system_df = pd.DataFrame(system_records)
    
    # 按时间排序
    system_df = system_df.sort_values('日期(年月)' if dimension=="month" else "日期", ignore_index=True)
    
    return system_df

async def json_to_dataframe_sp(system_data,output_system,dimension,ratio,byte_unit=""):
    """
    ## 将json数据转换为DataFrame
    ## 参数
    - system_data: json数据
    - output_system: 输出的系统名称
    - dimension: 输出的维度
    - ratio: 输入数据的比例
    """
    system_records = []
    system_name_dict = get_system_name()
    for key, systems in system_data.items():
        for system_key, data in systems.items():
            if output_system and data['SYSTEM_CODE'] not in output_system:
                continue
            if dimension == "hour":
                record = {"日期":key[:10],"时间":f"{key[11:13]}:00:00"}
            elif dimension == "day":
                record = {"日期":key[:10],"时间":"00:00:00-23:59:59"}
            elif dimension == "month":
                record = {'日期(年月)': key}
            # # 获取主机数
            host_num = data.get('HOST_NUM',0)
            # 数据库访问量
            if data['DATABASE_VISIT_TOTAL'] == 0 and data['DATABASE_VISIT_TOTAL_BYTES'] == 0:
                # 如果数据库加密，取4层流量
                if data['DATABASE_ACK_PACKAGES_TOTAL'] == 0:
                    # 长连接数据库访问量统计采用5个包约合一个SQL的方式
                    DATABASE_VISIT_TOTAL = int(data['DATABASE_PACKAGES_TOTAL']/5)
                else:
                    DATABASE_VISIT_TOTAL = data['DATABASE_ACK_PACKAGES_TOTAL']
                DATABASE_VISIT_TOTAL_BYTES = data['DATABASE_BYTES_TOTAL']
            else:
                # 取7层SQL需要乘采样率
                DATABASE_VISIT_TOTAL = data['DATABASE_VISIT_TOTAL']*ratio
                # 数据库访问流量
                DATABASE_VISIT_TOTAL_BYTES = data['DATABASE_VISIT_TOTAL_BYTES']*ratio
            record.update({
                '一级单位': data['UNIT_NAME'],
                '系统编号': data['SYSTEM_CODE'],
                '系统名称': system_name_dict.get(data['UNIT_NAME'],{}).get(data['SYSTEM_CODE'],""),
                '主机数量':host_num,
            })

            if dimension=="hour":
                record.update({
                    '系统访问总量': data['SYSTEM_VISIT_TOTAL'],
                    '系统数据流量(字节)': data["SYSTEM_VISIT_TOTAL_BYTES"],
                    '数据库访问量': DATABASE_VISIT_TOTAL,
                    '数据库访问流量(字节)': DATABASE_VISIT_TOTAL_BYTES,
                    '数据库操作量': data['DATABASE_OPR'],
                })
            else:
                if byte_unit == "MB":
                        record.update({
                        '系统访问总量': data['SYSTEM_VISIT_TOTAL'],
                        '系统数据流量(MB)': data["SYSTEM_VISIT_TOTAL_BYTES"]/1024/1024,
                        '数据库访问量': DATABASE_VISIT_TOTAL,
                        '数据库访问流量(MB)': DATABASE_VISIT_TOTAL_BYTES/1024/1024,
                    })
                else:
                    record.update({
                        '系统访问总量': data['SYSTEM_VISIT_TOTAL'],
                        '系统数据流量': cal_bytes_unit(data["SYSTEM_VISIT_TOTAL_BYTES"]),
                        '数据库访问量': DATABASE_VISIT_TOTAL,
                        '数据库访问流量': cal_bytes_unit(DATABASE_VISIT_TOTAL_BYTES),
                    })
            # 主机数为0时不显示CPU活跃时长
            record['虚拟CPU(vCPU)活跃累计时长'] = round(data['CPU_ACTIVE_MINUTES'],2) if host_num > 0 else 0
            system_records.append(record)
    system_df = pd.DataFrame(system_records)
    
    # 按时间排序
    system_df = system_df.sort_values('日期(年月)' if dimension=="month" else "日期", ignore_index=True)
    
    return system_df

async def match_report_files(task_id,json_dir,date):
    """
    ## 匹配符合时间范围的json文件
    :param task_id: 任务id
    :param json_dir: json文件目录
    :param date: 时间范围字符串
    """
    data_range = date.split("-")
    start_time = int(datetime.strptime(data_range[0], '%Y%m%d').timestamp())
    end_time = int(datetime.strptime(data_range[1], '%Y%m%d').timestamp()) + 86400
    date_list = list(datetime.fromtimestamp(st).strftime("%Y%m%d") for st,et in gen_ts_range(start_ts=start_time,end_ts=end_time,step=86400))
    # 获取所有JSON文件
    json_files = [os.path.join(json_dir, f) 
            for f in os.listdir(json_dir) \
            for date in date_list\
            if f.startswith(date) and f.endswith('.json')]
    
    if not json_files:
        logger.warning(f"{task_id}在目录 {json_dir} 中没有找到JSON文件")
        return None
    else:
        logger.info(f"{task_id}匹配到 {len(json_files)}个文件")
        return json_files

async def async_save_department_report(semaphore,department,output_path,date,system_report):
    async with semaphore:
        department_system_report = system_report[system_report['一级单位'] == department].reset_index(drop=True)
        file_path = os.path.join(output_path,f"{department}_{date}.xlsx")
        # with pd.ExcelWriter(file_path) as writer:
        department_system_report.to_excel(file_path, sheet_name='系统评估报告', index=True,index_label='序号')
        return file_path

def save_department_report(department,output_path,date,system_report):
    if department:
        department_system_report = system_report[system_report['一级单位'] == department].reset_index(drop=True)
    else:
        department_system_report = system_report
    file_path = os.path.join(output_path,f"{department}_{date}.xlsx")
    # with pd.ExcelWriter(file_path) as writer:
    department_system_report.to_excel(file_path, sheet_name='系统评估报告', index=True,index_label='序号')
    return file_path

def save_department_score_report(department,output_path,date,system_report,data_report=None):
    if department:
        department_system_report = system_report[system_report['一级单位'] == department].reset_index(drop=True)
        if data_report is not None:
            department_data_report = data_report[data_report['一级单位'] == department].reset_index(drop=True)
    else:
        department_system_report = system_report
        if data_report is not None:
            department_data_report = data_report
    file_path = os.path.join(output_path,f"{department}_{date}.xlsx")
    with pd.ExcelWriter(file_path) as writer:
        department_system_report.to_excel(writer, sheet_name='系统评估报告', index=True,index_label='序号')
        if data_report is not None:
            department_data_report.to_excel(writer, sheet_name='数据报告', index=True,index_label='序号')
    return file_path

def zip_excels(file_list, output_zip):
    try:
        with zipfile.ZipFile(output_zip, 'w', zipfile.ZIP_DEFLATED) as zipf:
            for file in file_list:
                if not os.path.isfile(file):
                    logger.error(f"压缩文件不存在: {file}")
                    raise FileNotFoundError(f"文件不存在: {file}")
                zipf.write(file)
        return True
    except Exception as e:
        logger.error(f"{output_zip}压缩错误: {str(e)}",exc_info=True)
        return False

class ArchDataKEY:
    """
    归档数据键类，用于合并映射键
    """
    def __init__(self,**group_dict):
        for k,v in group_dict.items():
            setattr(self,k,v)

    def __hash__(self):
        # 多同类键值不同顺序的组合字段重排，形成哈希去重
        return hash('_'.join(reversed(list(self.__dict__.values()))))

    def __eq__(self, other):
        other_values = other.__dict__.values()
        self_values = self.__dict__.values()
        if len(other_values) != len(self_values):
            return False
        if len(list(set(other_values) - set(self_values))) > 0:
            # 值不一样
            return False
        return True

    def __str__(self):
        return f"{'_'.join([str(v) for k, v in self.__dict__.items()])}"


def extract_from_execute(execute_data:dict):
    """
    将search/{dashboard_id}/execute接口响应结果提取为{"模块id":{"窗体id":[窗体行数据{"":""},{"":""}]}}数据形式\n
    :param execute_data: 响应结果
    :return:抽取结果
    """
    res_dic = {}
    for k,v in execute_data.items():
        # 遍历tab
        # k:tab_id
        # v:(search_types:小窗口配置)
        tab_dic = {}
        # 获取当前search_type
        # 拿出query中的search_types(包含id[拿到外层search_types搜索]、row_groups窗体分组字段、series窗体算法字段)
        for query in v['query']['search_types']:
            tab_dic[query['id']] = []
            # 拼接算法字段映射
            if 'series' not in  query.keys():
                # 遇到过没有series触发异常的情况
                continue
            # 生成算法字段映射
            series_mapping = {sery['id']: f"{(sery['field'].upper() + '_') if sery['field'] is not None else ''}{sery['type'].upper()}" for sery in query['series']}
            # 拼接分组字段列表
            # row_groups_list = [row_group['field'] for row_group in query['row_groups'] if row_group['type'] != 'timestamp']
            # tab_dic[query['id']]['group_fields'] = row_groups_list
            # 遍历每一个窗体id
            for row in v['search_types'][query['id']]['rows']:
                widget_row = {}
                # 过滤多余数据
                if row['source'] != 'leaf':
                    continue
                row_data = {}
                key = ArchDataKEY(**{group_field['field']:group_value for group_field,group_value in zip(query['row_groups'],row['key']) if group_field['type'] != 'time' and group_value not in ['',None,'NULL']})
                # 分组字段小于1个不归档
                if len(key.__dict__) < 1:
                    continue
                # 组合窗体分组字段相应值
                for group_field,group_value in zip(query['row_groups'],row['key']):
                    # 别名字段不是中文且不为空则采用别用作为字段名,否则用原始字段
                    row_data[group_field['name'] if not is_chinese(group_field['name']) and group_field['name'] not in ['',None] else group_field['field']] = group_value

                # 组合窗体算法字段相应值
                for value in row['values']:
                    # 如果别名包含中文才拼接
                    row_data[series_mapping.get(value['key'][0],'') if is_chinese(value['key'][0]) else value['key'][0]] = value['value']
                widget_row[key] = row_data

                tab_dic[query['id']].append(widget_row)

        # 以对象作为键，方便后续做窗体合并
        res_dic[k] = tab_dic

    return  res_dic


def extract_config_data(config_data) -> list:
    """
    从配置文件中获取需要进行归档的仪表盘/模块/窗体id列表

    :param config_data:配置文件中的仪表盘配置内容[]
    :return: 需要匹配的id列表
    """

    def recursive_flatten(data, path=None):
        if path is None:
            path = []

        # 如果是列表，迭代每个元素
        if isinstance(data, list):
            for i, item in enumerate(data):
                recursive_flatten(item, path)

        # 如果是字典，迭代每个键值对
        elif isinstance(data, dict):
            for key, value in data.items():
                recursive_flatten(value, path + [key])

        # 如果是基础数据类型（如字符串、数字等），则记录路径和值
        else:
            flattened_result.append(path + [str(data)])

    flattened_result = []
    recursive_flatten(config_data)
    return flattened_result

def extract_tab_dict(uniserver_obj:UniserverApiManager,dashboard_id:str,) -> dict:
    """
    请求仪表盘，返回{"id":"仪表盘id","name":"仪表盘名称","list":[{"id":"模块id","name":"模块名称","list":[窗体..]}]}
    :param uniserver_obj:
    :param dashboard_id:
    :return:
    """
    tab_widgets = uniserver_obj.get_tab_widgets(dashboard_id)
    # tab_widgets['id']为仪表盘id,tab_widgets['title']为仪表盘名称
    tab_dic = {"id":tab_widgets['id'], "search_id":tab_widgets['search_id'], 'name': tab_widgets['title'], "list": []}
    for id, tab_widget in tab_widgets['state'].items():
        # tab模块层
        # id为模块id
        tab_sub_dic = {"id": id, 'name': tab_widget['titles']['tab']['title'], "list": []}
        if "widget" not in tab_widget['titles'].keys():
            # 某些模块没有搭建窗体
            continue
        for k, v in tab_widget['titles']['widget'].items():
            # 小窗口层
            if k not in tab_widget['widget_mapping']:
                continue
            # 在未知有多个mapping的原因的情况下，暂用第一个作为id
            tab_sub_dic['list'].append({"id": tab_widget['widget_mapping'][k][0], "name": v})
        tab_dic['list'].append(tab_sub_dic)
    return tab_dic

def get_uniserver_dashboards_list(uniserver_obj:UniserverApiManager,config_list) -> list:
    """
    获取分组列表、仪表盘列表中文
    :param: uniserver_obj:UniserverApi实例对象
    :return:仪表盘分组映射列表[{"id":"分组id","name":"分组名称",list:[{"id":"仪表盘id","name":"仪表盘名称",list:[{模块..[窗体]}]}]},{..}]
    """
    # 可能不存在list列表
    dashboard_mappings = []
    dashboard_ids = []
    dashboard_groups = uniserver_obj.get_dashboard_groups()
    if len(dashboard_groups) == 0:
        raise Exception("没有查询到仪表盘分组")
    else:
        for group in dashboard_groups:
            # 仪表盘分组层
            dashboard_group_dict = {"id":group['_id'],"name":group['groupName'],"list":[]}
            dashboard_list = uniserver_obj.get_dashboards(group_id=group['_id'])
            for dashboard in dashboard_list:
                dashboard_ids.append(dashboard['id'])
                # 仪表盘层
                tab_dict = extract_tab_dict(uniserver_obj,dashboard['id'])
                dashboard_group_dict['list'].append(tab_dict)
            dashboard_mappings.append(dashboard_group_dict)
        for config_id in  config_list:
            if config_id in dashboard_ids:
                # 默认直接提供的是仪表盘id，以此判断是否需要额外获取
                continue
            if isinstance(config_id,str) and not is_chinese(config_id):
                # 对单独提供的仪表盘id处理
                tab_dict = extract_tab_dict(uniserver_obj,config_id)
                dashboard_mappings.append({"id":"","name":"","list":[tab_dict]})
        return dashboard_mappings


def define_arch_id_list(arch_list:list,dashboard_list:list) -> list:
    """
    归档决策函数【核心函数】\n
    将识别用户配置，生成需要获取的仪表盘id及其下模块和窗体列表\n
    阶段1：(当前)用户填写中文(分组名称-仪表盘名称)，归档仪表盘下所有tab和widget\n
    阶段2：用户填写中文或id(分组名称-仪表盘名称或id)，归档仪表盘下所有tab和widget\n
    # 必须提供仪表盘id或名称
    :param arch_list: 配置文件抽取结果
    :param dashboard_list: 分组列表、仪表盘列表
    :return:[{"仪表盘id":[{"模块id":["窗体id","窗体id"]}]},{"仪表盘id":[{"模块id":["窗体id","窗体id"]}]}]
    """
    arch_id_list = []

    def match_id(match_item:list,match_str:str) -> str:
        """
        :param match_item: 贪婪匹配列表
        :param match_str: 待匹配对象
        :return: id
        """
        # 贪婪递归匹配id
        # DFS
        for item in match_item:
            if item['name'] == match_str:
                # 直接给中文名称需要看情况返回id或search_id
                return item['id'] if  item.get('search_id','') == '' else item['search_id']
            elif item.get('search_id','') == match_str:
                # 直接给id是匹配search_id
                return item['search_id']
            elif item['id'] == match_str:
                # 直接给id是匹配id
                return item['search_id']
            else:
                # 否则递归查找
                if 'list' in item.keys():
                    sub_match =  match_id(item['list'],match_str)
                    if sub_match:
                        return sub_match
        return ""

    def iter_subitem(item:list|dict|str):
        """
        :param item: 配置文件元素(递归)
        :return:
        """
        if isinstance(item, str):
            # 直接提供单层配置(分组名称/id或仪表盘名称/id)
            if is_chinese(item):
                # 匹配分组名称
                _id = match_id(dashboard_list, item)
                if _id:
                    return _id
            else:
                # 直接给ID需要验证id是否合法,如果合法，则获取对应search_id
                return match_id(dashboard_list, item)
        elif isinstance(item, dict):
            # 可能是分组字典或仪表盘字典
            for k,v in item.items():
                # 分组名称/id或仪表盘名称/id
                if is_chinese(k):
                    # 匹配id
                    _id = match_id(dashboard_list,k)
                    if _id:
                        return {_id: iter_subitem(v)}
                else:
                    return {k: iter_subitem(v)}
        elif isinstance(item, list):
            match_list = []
            for sub_item in item:
                res = iter_subitem(sub_item)
                if res:
                    match_list.append(res)
            return match_list

    for arch_item in arch_list:
        match = iter_subitem(arch_item)
        if match in [None,""]:
            logger.warning(f"配置项【{arch_item}】未匹配到对应id")
            continue
        arch_id_list.append(match)

    return arch_id_list

def extract_dashboard_data(dashboard_data:dict,extract_id:str) -> dict:
    """
    从execute抽取的单个仪表盘数据，根据配置获取层级抽取返回\n
    :param dashboard_data: 单个仪表盘数据: {"模块id":{"窗体id":[]}}
    :param extract_id: 配置获取层级
    :return:
    """

    def recursive_extract(data,extract_id):
        if isinstance(extract_id, dict):
            # {"模块id":{}/[]}
            for k, v in extract_id.items():
                widget_id = data.get(k, '')
                if widget_id != '':
                    recursive_extract(v, widget_id)
        elif isinstance(extract_id, list):
            for item in extract_id:
                recursive_extract(data, item)
        elif isinstance(extract_id, str):
            res_data = data.get(extract_id, '')
            if res_data == "":
                if isinstance(data, dict):
                    for k,v in data.items():
                        if isinstance(v,list):
                            extract_data[k] = v
                            return
                        recursive_extract(v, extract_id)

    extract_data = {}
    recursive_extract(dashboard_data,extract_id)
    return extract_data

def get_arch_id_result(arch_id_item,uniserver_obj:UniserverApiManager,dashboards_list,dashboard_group_id_list,param) -> dict:
    """
    通过判断找到仪表盘id，请求execute(耗时任务)获取归档数据\n
    :param arch_id_item:
    :param uniserver_obj:
    :param dashboards_list:
    :param dashboard_group_id_list:
    :param param:
    :return:
    """
    arch_id_result = {}
    # arch_id_item只会是dict或str
    if isinstance(arch_id_item, dict):
        for k, v in arch_id_item.items():
            if k in dashboard_group_id_list:
                arch_id_result[k] = {}
                # k为分组id，按照设计，v预期是[{"仪表盘id":[..]}]或["仪表盘id",""]
                for dashboard in v:

                    if isinstance(dashboard, dict):
                        for dashboard_id, sub_id_list in dashboard.items():
                            dashboard_data = extract_from_execute(uniserver_obj.execute_search(dashboard_id, param))
                            # 匹配抽取函数
                            arch_id_result[k][dashboard_id] = dashboard_data
                    elif isinstance(dashboard, str):
                        dashboard_data = extract_from_execute(uniserver_obj.execute_search(dashboard, param))
                        print("未实现仪表盘合并")
            else:
                # 如果不是分组id，就是仪表盘id
                dashboard_data = extract_from_execute(uniserver_obj.execute_search(k, param))
                print("未实现仪表盘合并")
    else:
        # 直接给分组/仪表盘id
        if arch_id_item in dashboard_group_id_list:
            # arch_id_item为分组id
            # dashboards = dashboards_list[arch_id_item]
            for dashboards in dashboards_list:
                if dashboards['id'] == arch_id_item:
                    arch_id_result[arch_id_item] = {}
                    for dashboard in dashboards['list']:
                        # 如果不是分组id，就是仪表盘id
                        dashboard_data = extract_from_execute(
                            uniserver_obj.execute_search(dashboard['search_id'], param))
                        extract_data = extract_dashboard_data(dashboard_data, arch_id_item)
                        arch_id_result[arch_id_item][dashboard['id']] = extract_data
        else:
            # arch_id_item为仪表盘id
            dashboard_data = extract_from_execute(uniserver_obj.execute_search(arch_id_item, param))
            # extract_data = extract_dashboard_data(dashboard_data, arch_id_item)
            arch_id_result[arch_id_item] = dashboard_data
    return arch_id_result

def get_agg_result(uniserver_obj:UniserverApiManager,arch_id_list,dashboards_list,start_ts,end_ts,query=None) -> list:
    """
    根据配置文件id从execute接口抽取结果抽取合成最终数据,返回列表中元素与代表配置列表中的一个元素\n
    :param uniserver_obj
    :param arch_id_list:
    :param dashboards_list:
    :param start_ts
    :param end_ts
    :return:
    """
    agg_result = []
    # 分组id
    dashboard_group_id_list = [dashboard['id'] for dashboard in dashboards_list]
    if not start_ts and not end_ts and not query:
        param = {}
    else:
        param = {"global_override": {}}
        if start_ts is not None:
            param["global_override"].update({"timerange": {
                "type": "absolute",
                "from": datetime.fromtimestamp(start_ts,UTC).isoformat().replace("+00:00","") + ".000" + "Z",
            }})
        if end_ts is not None:
            if "timerange" in param["global_override"].keys():
                param["global_override"]["timerange"].update({"to": datetime.fromtimestamp(end_ts,UTC).isoformat().replace("+00:00","") + ".000" + "Z"})
            else:
                param["global_override"].update({"timerange": {
                    "type": "absolute",
                    "to": datetime.fromtimestamp(end_ts,UTC).isoformat().replace("+00:00","") + ".000" + "Z"
                }})
        if query is not None:
            param["global_override"].update({"query": {"query_string":query,"type":"elasticsearch"}})

    # # 调用execute获取仪表盘抽取数据
    with futures.ThreadPoolExecutor(max_workers=10 if len(arch_id_list) > 10 else len(arch_id_list)) as executor:
        # 提交任务到线程池
        future_to_task = {str(arch_id_item):executor.submit(get_arch_id_result, arch_id_item,uniserver_obj,dashboards_list,dashboard_group_id_list,param) for arch_id_item in arch_id_list}
        # 获取结果
        for future in futures.as_completed(future_to_task.values()):
            agg_result.append(future.result())
    # 单线程DEBUG
    # for arch_id_item in arch_id_list:
    #     agg_result.append(get_arch_id_result(arch_id_item,uniserver_obj,dashboards_list,dashboard_group_id_list,param))
    return agg_result

def get_name_from_mapping(_id,mapping):
    if isinstance(mapping,list):
        for dic_map in mapping:
            name =  get_name_from_mapping(_id,dic_map)
            if name == "":
                continue
            else:
                return name
        else:
            return ""
    elif isinstance(mapping,dict):
        if mapping['id'] == _id or mapping.get('search_id','') == _id:
            return mapping['name']
        else:
            if 'list' in mapping.keys():
                return get_name_from_mapping(_id,mapping['list'])
            else:
                return ""
    else:
        return ""

def sub_generator(key,data):
    """
    字典合成管道，保证数据拆解生成器每次返回的都是字段格式数据
    :param key:
    :param data:
    :return:
    """
    for k,v in data.items():
        if not isinstance(v,(list,dict)):
            # GELF要求必须有message和host字段
            if "message" not in data.keys():
                # 没有message字段默认为ARCH_TYPE++窗体路径
                data['message'] = f"{str(data['ARCH_TYPE'])}_{str(key)}"
            if "host" not in data.keys():
                # 没有host字段默认为本地
                data['host'] = '127.0.0.1'
            yield data
            break
        else:
            yield from sub_generator(k,v)

def send_arch_data_generator(agg_result):
    """
    原始数据拆解生成器
    :param agg_result:
    :return:
    """
    if isinstance(agg_result,list):
        for res in agg_result:
            yield from send_arch_data_generator(res)
    if isinstance(agg_result,dict):
        for k,v in agg_result.items():
            if isinstance(v,dict):
                yield from sub_generator(k,v)
            elif isinstance(v,list):
                for item in v:
                    yield from send_arch_data_generator(item)


def save_arch_merge_result(dir_path, agg_result,start_ts):
    # 目录不存在，创建目录
    if not os.path.exists(dir_path):
        os.makedirs(dir_path)
    # 保存到文件存储(文件名格式为年月日时分秒.json)
    filename = datetime.fromtimestamp(start_ts).strftime("%Y%m%d%H%M%S")
    result_file = f'{dir_path}/{filename}.json'
    with open(result_file, 'w', encoding='utf-8') as file:
        json.dump(agg_result, file, ensure_ascii=False, indent=2)
    # 打印保存主机数量
    logger.info(f'MERGE: save {len(agg_result)} records to {result_file} ...')


def extract_widget_data(result):
    """
    提取窗体数据,应对不同层级的抽取合成数据，按照设计，最里层窗体数据行为列表，其余外层均为字典\n
    :param result:抽取合成数据
    :return:{"窗体id":[]}
    """
    def recursive_extract(data):
        if isinstance(data, dict):
            # widget_data_dict[key] = {}
            for k1, v1 in data.items():
                if isinstance(v1, dict):
                    recursive_extract(v1)
                    # widget_data_dict.pop(key)
                elif isinstance(v1, list):
                    widget_data_list.append({"id":k1,"list":v1})
    widget_data_list = []
    recursive_extract(result)
    return widget_data_list

def convert_agg_result_to_widget_dict(agg_result,start_ts, end_ts, step,config,dashboards_list,host_num_dict=None) -> dict:
    # dashboard_group_id_list = [dashboard['id'] for dashboard in dashboards_list]
    # BUG: 通过分组ID/名称获取时未能提取到窗体数据
    # TODO: 适配分组id/名称及仪表盘id/名称的情况(自动判断递归深度)
    # 递归到最里层，提取窗体数据
    arch_dict = {}
    for result in agg_result:
        # 一个result对应配置列表的一个元素，默认进行窗体合并
        for _id, result_dict in result.items():
            dashboard_name = get_name_from_mapping(_id, dashboards_list)
            arch_dict[dashboard_name] = []
            for module_id,module_content in result_dict.items():
                module_dict = {}
                module_name = get_name_from_mapping(module_id, dashboards_list)
                widget_list = extract_widget_data(module_content)
                for widget in widget_list:
                    if len(widget['list']) == 0:
                        continue
                    widget_dic = {}
                    widget_keys = []
                    arch_type = get_name_from_mapping(widget['id'], dashboards_list)
                    arch_data = {"ARCH_TYPE": f"{dashboard_name}_{module_name}_{arch_type}",#以模块名称作为归档类型
                                 "timestamp": start_ts,
                                 "ARCH_SIZE": end_ts - start_ts if step is None else step,
                                 "ARCH_START": datetime.fromtimestamp(start_ts).strftime("%Y-%m-%d %H:%M:%S"),
                                 "ARCH_END": datetime.fromtimestamp(end_ts).strftime("%Y-%m-%d %H:%M:%S"),
                                 "ARCH_COUNT": 0}
                    for data in widget['list']:
                        for k, v in data.items():
                            v.update(arch_data)
                            if host_num_dict:
                                #TODO: 设计更灵活的映射配置
                                #WARNING: 表头映射字段写死
                                if v["UNIT_NAME"] in host_num_dict:
                                    if v["SYSTEM_CODE"].replace("SJK_","") in host_num_dict[v["UNIT_NAME"]]:
                                        v["HOST_NUM"] = len(host_num_dict[v["UNIT_NAME"]][v["SYSTEM_CODE"].replace("SJK_","")])
                                    else:
                                        v["HOST_NUM"] = 0
                                else:
                                    v["HOST_NUM"] = 0
                            for key in widget_keys:
                                if key == k:
                                    # 只要分组字段中某一字段的值一致即进行合并
                                    v.update(widget_dic[key])
                                    widget_dic[k] = v
                            else:
                                widget_dic[k] = v
                                widget_keys.append(k)
                    widget_dic = {str(k): v for k, v in widget_dic.items()}
                    module_dict[arch_type] = widget_dic
                # arch_dict[f"{dashboard_name}_{module_name}"] = module_dict
                # 每个模块一个归档文件夹合并在同一仪表盘下
                save_arch_merge_result(f"{config['general_config']['RESULT_PATH']}/{dashboard_name}/{module_name}", module_dict,start_ts)
                arch_dict[dashboard_name].append(module_dict)
    return arch_dict

def send_data_mission(uniserver_obj,data_gen,config):
    with socket.socket(socket.AF_INET, socket.SOCK_DGRAM) as udp_obj:
        # for data in send_arch_data_generator(arch_data):
        # 发送logstash
        # print(f"send data {json.dumps(arch_data, ensure_ascii=False, indent=2)}")
        while True:
            try:
                data = next(data_gen)
            except StopIteration:
                return
            send_data = json.dumps(data, ensure_ascii=False, indent=2)
            if config['general_config']['UNISERVER_SEND_PROTOCOL'] == 'udp':
                uniserver_obj.udp_send_arch_merge_result(udp_obj, send_data)
            else:
                uniserver_obj.http_send_arch_merge_result(send_data)

def mission(start_ts, end_ts, step,config,rrh,run_id,threads):
    retry = 20
    for i in range(retry):
        try:
            with UniserverApiManager() as UAM:
                threads = 2 * len(eval(os.getenv("UNISERVER_ADDR","[]"))) if len(eval(os.getenv("UNISERVER_ADDR","[]"))) !=0 else 10
                executor = futures.ThreadPoolExecutor(threads)
                rrh.insert_run_record(run_id,start_ts, end_ts, step, threads, json.dumps(config,ensure_ascii=True), 'running', '')
                dashboards_list = get_uniserver_dashboards_list(UAM,config['dashboard_config'])
                arch_id_list = define_arch_id_list(config['dashboard_config'],dashboards_list)
                # arch_id_list:[{"用户调用api/views的id":},...]
                agg_result = get_agg_result(UAM,arch_id_list,dashboards_list,start_ts,end_ts)
                host_num_dict = get_host_num_dict(UAM)
                def merge_multiple_result(system_code,ip_list):
                    with UniserverApiManager() as UAM_multi:
                        host_visit_agg_result = get_agg_result(UAM_multi,arch_id_list,dashboards_list,start_ts,end_ts,f"APP_NAME:{system_code} AND NOT SRC_IP:({' OR '.join(ip_list)})")
                        for result in host_visit_agg_result:
                            for dashboard_id,dashboard_content in result.items():
                                for agg_r in agg_result:
                                    if dashboard_id in agg_r.keys():
                                        for tab_id,tab_content in dashboard_content.items():
                                            for widget_id,widget_content in tab_content.items():
                                                for row in widget_content:
                                                    for system_key,system_data in row.items():
                                                        if "SYSTEM_VISIT_TOTAL" not in system_data.keys():
                                                            continue
                                                        if tab_id in agg_r[dashboard_id].keys() and widget_id in agg_r[dashboard_id][tab_id].keys():
                                                            for data in agg_r[dashboard_id][tab_id][widget_id]:
                                                                if system_key == list(data.keys())[0]:
                                                                    data[system_key].update(system_data)
                                                                    break
                                                            else:
                                                                agg_r[dashboard_id][tab_id][widget_id].append(row)
                future_to_task = [executor.submit(merge_multiple_result,system_code,ip_list) 
                                  for unit_name,unit_content in host_num_dict.items()
                                  for system_code,ip_list in unit_content.items()
                                  ]
                for future in futures.as_completed(future_to_task):
                    future.result()
                arch_list = convert_agg_result_to_widget_dict(agg_result,start_ts, end_ts, step,config,dashboards_list,host_num_dict)
                data_gen = send_arch_data_generator(arch_list)
                data_gen_count = send_arch_data_generator(arch_list)
                logger.info(f"data_gen_count: {len(list(data_gen_count))}")
                logger.info("start sending data to uniserver")
                future_to_task = [executor.submit(send_data_mission,UAM,data_gen,config) for i in range(threads)]
                for future in futures.as_completed(future_to_task):
                    future.result()
                logger.info("finish sending data to uniserver")
                rrh.update_run_record(run_id, 'success', None)
                break
        except Exception as e:
            rrh.update_run_record(run_id, 'running', str(e))
            logger.error(f"归档任务异常:{str(e)}",exc_info=True)
    else:
        rrh.update_run_record(run_id, 'failed', f"重试{retry}次失败")



if __name__ == '__main__':
    import yaml
    config_path = ''
    with open(config_path, 'r', encoding='utf-8') as file:
        config = yaml.safe_load(file)
    mission(1627584000,1627587600,300,config)

