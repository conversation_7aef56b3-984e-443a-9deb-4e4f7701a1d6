import os
import json
import asyncio
from uuid import uuid4
from apscheduler.schedulers.asyncio import AsyncIOScheduler
from apscheduler.triggers.cron import Cron<PERSON>rigger
from datetime import datetime, timedelta
from log import logger
from third_part_apis import ProjectManagementApi
from pytz import timezone
import yaml
from util import RunRecordHelper,get_latest_file
from data_handlers import gen_evaluate_report
from collections import defaultdict
from data_handlers import match_report_files,process_single_json_file,merge_single_json_file,json_to_dataframe,merge_single_system_hour_json_file,network_field_decision,make_hour_dataframe,get_system_catalogcount
from supervise import AlertPlatform
import numpy as np
import pandas as pd

# 单例调度器实例
scheduler = AsyncIOScheduler()

with open(f'{os.path.dirname(__file__)}/report_settings.yaml', 'r', encoding='utf-8') as file:
    report_config = yaml.safe_load(file)

with open(f'{os.path.dirname(__file__)}/settings.yaml', 'r', encoding='utf-8') as file:
    uniarch_config = yaml.safe_load(file)

# 设置北京时区
beijing_tz = timezone('Asia/Shanghai')

def start_scheduler():
    """启动调度器并添加任务"""
    if not scheduler.running:
        scheduler.start()
        if report_config.get('ENABLE_DAILY_REPORT_SCHEDULE', True):
            # 添加每日凌晨2点执行的每日汇总任务
            scheduler.add_job(
                daily_summary_task,
                trigger=CronTrigger(hour=2, minute=0,timezone=beijing_tz),  
                id='daily_summary',
                replace_existing=True
            )
            logger.info(f"调度器已启动，每日汇总下次执行时间: {scheduler.get_job('daily_summary').next_run_time}")
        if report_config.get('ENABLE_MONTHLY_REPORT_SCHEDULE', True):
            # 添加每月1号凌晨3点执行的每月汇总任务
            scheduler.add_job(
                monthly_summary_task,
                trigger=CronTrigger(day=1,hour=3, minute=0,timezone=beijing_tz),  
                id='monthly_summary',
                replace_existing=True
            )
            logger.info(f"调度器已启动，每月汇总下次执行时间: {scheduler.get_job('monthly_summary').next_run_time}")
        if report_config.get('ENABLE_INSPECTION_SCHEDULE', True):
            # 每天9点、15点、21点执行一次巡检
            scheduler.add_job(
                daily_inspection_task,
                trigger=CronTrigger(hour='9,15,21', minute=0, timezone=beijing_tz),  
                id='daily_inspection',
                replace_existing=True
            )
            logger.info(f"调度器已启动，巡检下次执行时间: {scheduler.get_job('daily_inspection').next_run_time}")

async def daily_inspection_task():
    """
    # 巡检函数
    每天9,15,21时触发,其中15时不管正不正常都会发短信通知,\n
    其余时间只有异常(最后一次运行记录时间与当前相距大于1小时)才发通知
    """
    now_time = datetime.now()
    # 最后一次运行记录
    run_data = RunRecordHelper(f"{uniarch_config['general_config']['RESULT_PATH']}/record.db").get_last_run_record()
    # 最后一次运行时间
    logger.info(f"最后一次运行时间：{run_data['update_time']}")
    last_run_time = datetime.strptime(run_data["update_time"], '%Y-%m-%d %H:%M:%S') if run_data else None
    # 最新归档数据时间
    latest_run_file = get_latest_file(report_config['arch_data_path'])
    latest_run_file_time = datetime.fromtimestamp(latest_run_file.stat().st_mtime)
    logger.info(f"最新归档数据{str(latest_run_file)}时间：{latest_run_file_time}")
    if last_run_time and now_time - last_run_time  > timedelta(seconds=report_config['SUPERVISE_SECOND_THREASHOLD']):
        logger.info(f"{now_time} 巡检结果:\n归档运行异常\n最后一次运行时间{last_run_time}")
        # 最后一次运行记录时间与当前相距大于1小时为异常
        AlertPlatform().send_sms_alert(f"{now_time} 巡检结果:\n归档运行异常\n最后一次运行时间{last_run_time}", os.getenv('ALERT_RECEIVER'))  
    elif latest_run_file_time and now_time - latest_run_file_time  > timedelta(seconds=report_config['SUPERVISE_SECOND_THREASHOLD']):
        logger.info(f"{now_time} 巡检结果:\n归档数据异常\n最新归档数据时间{latest_run_file_time}")
        # 最新的归档数据时间与当前相距大于1小时为异常
        AlertPlatform().send_sms_alert(f"{now_time} 巡检结果:\n归档数据异常\n最新归档数据时间{latest_run_file_time}", os.getenv('ALERT_RECEIVER'))
    elif int(now_time.strftime('%H')) == report_config['NORMAL_NOTICE_HOUR']:
        logger.info(f"{now_time} 巡检结果：\n归档运行正常")
        # 15时不管正不正常都会发短信通知
        AlertPlatform().send_sms_alert(f"{now_time} 巡检结果：\n归档运行正常", os.getenv('ALERT_RECEIVER'))
    else:
        logger.info(f"{now_time} 巡检结果：\n归档运行正常")
async def shutdown_scheduler():
    """关闭调度器"""
    if scheduler.running:
        scheduler.shutdown()
        logger.info("调度器已关闭")

async def daily_summary_task(day_delta=1):
    """实际的数据汇总任务"""
    #  获取当前日期前一日
    date = (datetime.now() - timedelta(days=day_delta)).strftime("%Y%m%d")
    logger.info(f"开始执行每日汇总 [{date}]")
    await generate_daily_report(date)
    logger.info("每日汇总完成")


async def generate_daily_report(date):
    """每日汇总数据"""
    try:
        task_id = str(uuid4())
        logger.info(f"正在生成每日汇总报告{task_id}...")
        pma = ProjectManagementApi()
        system_data = defaultdict(lambda: defaultdict(lambda: defaultdict(lambda: {
        'UNIT_NAME': '',
        'SYSTEM_CODE': '',
        'SYSTEM_VISIT_TOTAL': 0,
        'SYSTEM_VISIT_TOTAL_BYTES': 0.0,
        'DATABASE_VISIT_TOTAL': 0.0,
        'DATABASE_VISIT_TOTAL_BYTES': 0.0,
        'CPU_ACTIVE_MINUTES': 0,
        'DATABASE_OPR':0,
        'DATABASE_PACKAGES_TOTAL': 0.0,
        'DATABASE_BYTES_TOTAL': 0.0,
        'DATABASE_ACK_PACKAGES_TOTAL' : 0.0,
        'HOST_NUM':0,
        }))) 
        MAX_CONCURRENT_READS = report_config['MAX_ASYNC_CONCURRENT_READS']
        semaphore = asyncio.Semaphore(MAX_CONCURRENT_READS)
        task_id = str(uuid4())
        date = f"{date}-{date}"
        logger.info(f"{task_id}开始生成报告{date}|{report_config['SEND_REPORT_SYSTEMS']}")
        json_files = await match_report_files(task_id,report_config['arch_data_path'],date)
        if not json_files:
            logger.warning("每日报告没有数据")
            raise Exception("没有数据")
        batch_size = report_config['JSON_FILE_BATCH_SIZE']
        for i in range(0, len(json_files), batch_size):
            logger.info(f"{task_id}正在读取 {i}-{i+batch_size}批数据")
            # 分批处理避免内存溢出
            batch = json_files[i:i+batch_size]
            local_system_data_list = await asyncio.gather(*(process_single_json_file(
                                                            semaphore,
                                                            p, 
                                                            ) for p in batch))
            logger.info(f"{task_id}正在聚合 {i}-{i+batch_size}批数据")
            for local_system_data in local_system_data_list:
                if local_system_data is None:
                    continue
                # 按小时合并数据
                await merge_single_system_hour_json_file(system_data,local_system_data,report_config['host_cpu_avg_threashold'])
        network_field_decision_config = await network_field_decision(system_data)
        host_df = await make_hour_dataframe(system_data,report_config["SEND_REPORT_SYSTEMS"],report_config['ratio'],network_field_decision_config,"Byte")
        if host_df.empty:
            logger.warning("每日报告没有数据")
            raise Exception("没有数据")
        daily_data = []
        for index, row in host_df.iterrows():
            data = {}
            data["unitName"] = str(row.一级单位)
            data["firstLevelDeptName"] = str(row.一级单位)
            data["systemName"] = ""
            data["systemCode"] = str(row.系统编号)
            data["hostIp"] = ""
            data["hostUsage"] = str(row.主机数量)
            data["systemVisitTotal"] = str(row.系统访问总量)
            data["systemVisitTotalBytes"] = str(row["系统数据流量(字节)"])
            data["databaseVisitTotal"] = str(row.数据库访问量)
            data["databaseVisitTotalBytes"] = str(row["数据库访问流量(字节)"])
            data["virtualCpuActiveTime"] = str(row["虚拟CPU(vCPU)活跃累计时长"])
            data["databaseOpr"] = ""
            data["catalogCount"] = str(row.公共数据资源目录编目)
            data["date"] = row.日期
            data["timePoint"] = row.时间[:8]
            daily_data.append(data)
        pma.send_daily_data(json.dumps(daily_data))
        logger.info("每日报告生成完毕")
    except Exception as e:
        logger.error(f"每日报告生成失败：{e}",exc_info=True)

async def monthly_summary_task(day_delta=1,algrithm="percent_score_calculator"):
    """实际的数据汇总任务"""
    #  获取当前日期前一月
    month = (datetime.now() - timedelta(days=day_delta)).strftime("%Y%m")
    logger.info(f"开始执行每月汇总 [{month}]")
    await generate_monthly_report(month,algrithm)
    logger.info("每月汇总完成")


async def generate_monthly_report(month,algrithm):
    try:
        """每月汇总拿上一个月数据打分"""
        task_id = str(uuid4())
        logger.info(f"正在生成每月汇总报告{task_id}...")
        month_report = []
        pma = ProjectManagementApi()
        system_data = defaultdict(lambda: defaultdict(lambda: defaultdict(lambda: {
        'UNIT_NAME': '',
        'SYSTEM_CODE': '',
        'SYSTEM_VISIT_TOTAL': 0,
        'SYSTEM_VISIT_TOTAL_BYTES': 0.0,
        'DATABASE_VISIT_TOTAL': 0.0,
        'DATABASE_VISIT_TOTAL_BYTES': 0.0,
        'CPU_ACTIVE_MINUTES': 0,
        'DATABASE_OPR':0,
        'DATABASE_PACKAGES_TOTAL': 0.0,
        'DATABASE_BYTES_TOTAL': 0.0,
        'DATABASE_ACK_PACKAGES_TOTAL' : 0.0,
        'HOST_NUM':0,
        }))) 
        MAX_CONCURRENT_READS = report_config['MAX_ASYNC_CONCURRENT_READS']
        semaphore = asyncio.Semaphore(MAX_CONCURRENT_READS)
        date = f"{month}01-{month}31"
        json_files = await match_report_files(task_id,report_config['arch_data_path'],date)
        if not json_files:
            logger.warning("每月报告没有数据")
            raise Exception("没有数据")
        batch_size = report_config['JSON_FILE_BATCH_SIZE']
        for i in range(0, len(json_files), batch_size):
            logger.info(f"{task_id}正在读取 {i}-{i+batch_size}批数据")
            # 分批处理避免内存溢出
            batch = json_files[i:i+batch_size]
            local_system_data_list = await asyncio.gather(*(process_single_json_file(
                                                            semaphore,
                                                            p, 
                                                            ) for p in batch))
            logger.info(f"{task_id}正在聚合 {i}-{i+batch_size}批数据")
            for local_system_data in local_system_data_list:
                if local_system_data is None:
                    continue
                await merge_single_system_hour_json_file(system_data,local_system_data,report_config['host_cpu_avg_threashold'])
        network_field_decision_config = await network_field_decision(system_data)
        report_df = await make_hour_dataframe(system_data,report_config["SEND_REPORT_SYSTEMS"],report_config['ratio'],network_field_decision_config,"MB")
        if report_df.empty:
            logger.warning(f"{task_id}没有报告数据")
            raise Exception("没有数据")
        def custom_agg(x):
            return np.sum(x) * 31 / 15
        report_df = pd.pivot_table(report_df, index=["一级单位","系统编号","系统名称"],values=["系统访问总量","系统数据流量(MB)","数据库访问量","数据库访问流量(MB)","虚拟CPU(vCPU)活跃累计时长"],aggfunc='sum').reset_index()
        system_catalogcount_dict = get_system_catalogcount()
        def get_catalogcount(row):
            return system_catalogcount_dict.get(row["一级单位"],{}).get(row["系统编号"],0)
        # 通过透视按月聚合
        pivot_df = pd.pivot_table(report_df, index=["一级单位","系统编号","系统名称"],values=["数据库操作量","系统访问总量","系统数据流量(MB)","数据库访问量","数据库访问流量(MB)","虚拟CPU(vCPU)活跃累计时长"],aggfunc="sum").reset_index()
        pivot_df["公共数据资源目录编目"] = pivot_df.apply(get_catalogcount, axis=1)
        report_df['日期(年月)'] = month
        report_df = gen_evaluate_report(report_df,algrithm)
        for index, row in report_df.iterrows():
            data = {}
            data["yearMonth"] = str(row["日期(年月)"])
            data["unitName"] = str(row.一级单位)
            data["systemName"] = ""
            data["systemCode"] = str(row.系统编号)
            data["ip"] = str(row.IP数量)
            data["evaluationResult"] = ""
            data["systemEfficiencyPoint"] = str(row.系统效能赋分)
            data["systemVisitTotalPoint"] = str(row.系统访问量赋分)
            data["systemVisitTotalBytesPoint"] = str(row.系统访问流量赋分)
            data["databaseVisitTotalPoint"] = str(row.数据库访问量赋分)
            data["databaseVisitTotalBytesPoint"] = str(row.数据库访问流量赋分)
            data["virtualCpuActiveTimePoint"] = str(row["虚拟CPU(VCPU)活跃累计时长赋分"])
            data["databaseOprPoint"] = ""
            data["catalogPoint"] = str(row["公共数据资源目录编目赋分"])
            month_report.append(data)
        pma.send_monthly_data(json.dumps(month_report))
        logger.info("每月报告生成完毕")
    except Exception as e:
        logger.error(f"每月报告生成失败：{e}",exc_info=True)