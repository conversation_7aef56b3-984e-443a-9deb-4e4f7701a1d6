#!/bin/bash
#检查main.py相关进程是否存在
if [ $(ps -ef | grep -v grep | grep main.py | wc -l) -eq 0 ]; then
    echo "main.py进程不存在，正在启动..."
    # 重新挂起常驻的归档程序
    nohup python3 /app/main.py -s 600 -et 9900000000 >> main.log 2>> main_err.log &
    echo "main.py进程启动成功！"
else
    echo "main.py进程已存在，无需启动！"
fi

if [ $(ps -ef | grep -v grep | grep app.py | wc -l) -eq 0 ]; then
    echo "app.py进程不存在，正在启动..."
    # 重新挂起常驻的归档程序
    nohup fastapi run app.py --host 0.0.0.0 --port 9878 --workers 1 >> app.log 2>> app_err.log &
    echo "app.py进程启动成功！"
else
    echo "app.py进程已存在，无需启动！"
fi