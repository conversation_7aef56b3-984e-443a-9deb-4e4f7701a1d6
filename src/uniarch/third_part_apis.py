import os
from log import logger
from util import general_requests

class ProjectManagementApi:
    """
    项目管理平台接口封装类
    """
    def __init__(self, **kwargs):
        self.PROJECT_MANAGE_PORT = int(os.getenv("PROJECT_MANAGE_PORT",8881))
        self.PROJECT_MANAGE_ADDR = os.getenv("PROJECT_MANAGE_ADDR","")
        self.PROJECT_MANAGE_PROTOCOL = os.getenv("PROJECT_MANAGE_PROTOCOL","")
        self.base_url = f"{self.PROJECT_MANAGE_PROTOCOL}://{self.PROJECT_MANAGE_ADDR}:{self.PROJECT_MANAGE_PORT}"
        for k,v in kwargs.items():
            setattr(self, k, v)
        self.headers = {
        "referer": "xiaoneng",
        "Content-Type": "application/json",
        }
    
    def send_daily_data(self,data):
        """ 每天凌晨1点发送前一天数据接口 
        :param data: 前一天的数据
        [
            {
                "unitName":"单位名称",
                "firstLevelDeptName":"一级单位名称",
                "systemName":"系统名称",
                "systemCode":"系统编号",
                "hostIp":"主机ip",
                "hostUsage":"主机用途",
                "systemVisitTotal":"系统访问总量",
                "systemVisitTotalBytes":"系统访问数据流量",
                "databaseVisitTotal":"数据库访问量",
                "databaseVisitTotalBytes":"数据库访问流量",
                "virtualCpuActiveTime":"vCPU活跃时常",
                "databaseOpr":"数据库操作量",
                "catalogCount":"公共数据资源目录编目",
                "date":"2025-06-13",
                "timePoint":"11:00:00"
            }
        ]
        """
        try:
            url = f"{self.base_url}/newoa/system-metrics/save"
            response = general_requests(url=url,method="post",data=data,headers=self.headers)
            logger.info(f"send data to {url} success,response:{response}")
        except Exception as e:
            logger.error(f"send data to {url} error:{str(e)}",exc_info=True)

    def send_monthly_data(self,data):
        """ 每月一号凌晨三点发送前一月数据接口
        :param data: 前一天的数据
        [
            {
                "yearMonth":"2025-06",
                "unitName":"单位名称",
                "systemName":"系统名称",
                "systemCode":"系统编号",
                "ip":"ip数量",
                "evaluationResult":"评估结果描述",
                "systemEfficiencyPoint":"系统效能赋分",
                "systemVisitTotalPoint":"系统访问赋分",
                "systemVisitTotalBytesPoint":"系统数据流量赋分",
                "databaseVisitTotalPoint":"数据库访问量赋分",
                "databaseVisitTotalBytesPoint":"数据库访问流量赋分",
                "virtualCpuActiveTimePoint":"虚拟CPU活跃时长赋分",
                "databaseOprPoint":"数据库操作量赋分",
                "catalogPoint":"公共资源数据编目赋分"
            }
        ]
        """
        try:
            url = f"{self.base_url}/newoa/system-metrics-point/saveList"
            response = general_requests(url=url,method="post",data=data,headers=self.headers)
            logger.info(f"send data to {url} success,response:{response}")
        except Exception as e:
            logger.info(f"send data to {url} error:{str(e)}",exc_info=True)
