import os
import time
import yaml
from uuid import uuid4
import argparse
from util import gen_ts_range, get_current_min_ts, load_env
# 配置文件读取
load_env(f'{os.path.dirname(__file__)}/.env')
from log import logger
from concurrent import futures
from data_handlers import mission
from util import RunRecordHelper



def parse_args():

    parser = argparse.ArgumentParser()
    # 配置入参关键字
    parser.add_argument(

        "-s",

        "--step",

        help="归档时长间隔(秒)",

        dest="step",

        type = int


    )
    parser.add_argument(

        "-st",

        "--start_time",

        help="归档开始时间",

        dest="start_time",

        type=int

    )
    parser.add_argument(

        "-et",

        "--end_time",

        help="归档结束时间",

        dest="end_time",

        type=int

    )

    parser.add_argument(

        "-t",

        "--multi_thread",

        help="启用多线程",

        dest="multi_thread",

        type=int

    )

    parser.add_argument(

        "-cs",

        "--calculate_sequential",

        help="是否计算环比",

        dest="calculate_sequential",

        type=int

    )

    parser.add_argument(

        "-cf",

        "--config_file",

        help="指定配置文件",

        dest="config_file",

        type=str,

        default='settings.yaml'

    )

    args = parser.parse_args()

    return args, parser



if __name__ == '__main__':
    # 命令行入参解析
    args, parser = parse_args()
    step = args.step
    cs = args.calculate_sequential
    config_file = args.config_file
    if config_file !="settings.yaml":
        if not os.path.isabs(config_file):
            config_file = os.path.join(os.getcwd(), config_file)
        if not os.path.exists(config_file):
            logger.error(f"指定的配置文件 {config_file} 不存在，请检查路径和文件名是否正确。", exc_info=True)
            exit(1)
        # 如果用户指定了配置文件，则使用用户指定的配置文件
        logger.info(f"使用指定配置文件：{config_file}")
        with open(config_file, 'r', encoding='utf-8') as file:
            config = yaml.safe_load(file)
    else:
        logger.info(f"使用默认配置文件：{config_file}")
        with open(f'{os.path.dirname(__file__)}/{config_file}', 'r', encoding='utf-8') as file:
            config = yaml.safe_load(file)
    # 判断归档文件夹路径是否存在，不存在则创建
    if not os.path.exists(f"{config['general_config']['RESULT_PATH']}"):
        os.mkdir(f"{config['general_config']['RESULT_PATH']}")
    # 初始化运行记录业务数据类
    rrh = RunRecordHelper(f"{config['general_config']['RESULT_PATH']}/record.db")
    result = rrh.create_run_record_table()
    if not result:
        logger.warning(f"创建运行记录表失败")
        exit(1)
    if step is not None and step < 60:
        raise Exception(f"归档间隔时长{str(step)}秒超出限制,最低为60秒")
    s_time = args.start_time
    e_time = args.end_time
    if s_time is None:
        # 不传入开始时间则根据最后一次归档的文件时间继续归档
        last_run_record = rrh.get_last_run_record()
        if not last_run_record:
            logger.warning(f"未找到上次归档记录,使用当前时间作为开始时间")
            s_time = get_current_min_ts() - (0 if step is None else step)
        else:
            s_time = last_run_record['end_ts'] + 1
    now_time = get_current_min_ts()
    if e_time is None:
        # 不传入结束时间默认当前为结束时间
        e_time = get_current_min_ts()
    if step is not None and e_time - s_time < step:
        raise Exception(f"归档时间区间小于归档间隔{str(step)}秒,请调整-s参数值")
    MAX_THREADS = config['general_config']['MAX_THREADS']
    # 线程数控制
    threads = MAX_THREADS if args.multi_thread is not None and args.multi_thread >= MAX_THREADS else args.multi_thread
    if threads:
        logger.info(f'use multi thread: {threads}')
        executor = futures.ThreadPoolExecutor(threads)
    else:
        logger.info(f'use single thread')
        executor = None
    if threads and s_time:
        tasks = {}
        for START_TS, END_TS in gen_ts_range(int(s_time), int(e_time), step):
            try:
                now_time = get_current_min_ts()
                if END_TS > now_time:
                    logger.warning(f"结束时间大于当前时间,需等待{str(END_TS - now_time)}秒")
                    time.sleep(END_TS - now_time)
                # 生成运行id
                run_id = str(uuid4())
                if cs:
                    logger.info("待更新calculate_sequential_mission")
                else:
                    tasks[run_id] = executor.submit(mission, START_TS, END_TS,step, config,rrh,run_id,threads)
            except Exception as e:
                logger.error(f"{run_id}运行过程中发生错误：{str(e)}\n", exc_info=True)
        result = [task.result() for task in tasks.values()]
    else:
        for START_TS, END_TS in gen_ts_range(int(s_time), int(e_time), step):
            try:
                now_time = get_current_min_ts()
                if END_TS > now_time:
                    logger.warning(f"结束时间大于当前时间,需等待{str(END_TS - now_time)}秒")
                    time.sleep(END_TS - now_time)
                # 生成运行id
                run_id = str(uuid4())
                start_time = time.time()
                # 入参指定时间时，请求配置需要覆盖仪表盘查询配置
                if cs:
                    logger.info("待更新calculate_sequential_mission")
                else:
                    mission(START_TS, END_TS, step, config,rrh,run_id,0)
                end_time = time.time()
                if not threads:
                    # 增加下轮时间判断，在归档异常重新开始时可以尽快跟上区间
                    if END_TS + step > now_time:
                        if step is not None and end_time - start_time < step:
                            time.sleep(step - (end_time - start_time))
            except Exception as e:
                logger.error(f"{run_id}运行过程中发生错误：{str(e)}\n", exc_info=True)