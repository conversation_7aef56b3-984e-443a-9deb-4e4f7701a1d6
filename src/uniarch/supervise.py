# 各项日常程序巡检脚本

import os
import json
import hashlib
from datetime import datetime
from util import general_requests
from log import logger

class AlertPlatform:

    def __init__(self):
        self.ALERT_ADDR = os.getenv('ALERT_ADDR')
        self.ALERT_PORT = os.getenv('ALERT_PORT')
        self.ALERT_PROTOCOL = os.getenv('ALERT_PROTOCOL')
        self.ALERT_MANAGE_ACC = os.getenv('ALERT_MANAGE_ACC')
        self.ALERT_MANAGE_SECRET = os.getenv('ALERT_MANAGE_SECRET')
        self.AUTH_STATIC_STR = os.getenv('AUTH_STATIC_STR')

    def encrypt_pwd(self,timestamp):
        # MD5加密
        md5 = hashlib.md5()
        md5.update((self.ALERT_MANAGE_ACC + self.AUTH_STATIC_STR + self.ALERT_MANAGE_SECRET + timestamp).encode('utf-8'))
        return md5.hexdigest()

    def send_sms_alert(self,msg,receiver):
        '''
        # 发送告警
        :param msg: 告警内容
        :return:
        ## 请求参数说明
        | 参数       | 类型     | 是否必须 | 描述                                                                 | 示例       |
        |------------|----------|----------|----------------------------------------------------------------------|------------|
        | userid     | String   | 是       | 用户账号:长度最大6个字符,统一大写                                 | J10003     |
        | pwd        | String   | 是       | 鉴权信息:密码规则详见“3.1鉴权规则”                                 |            |
        | timestamp  | String   | 是       | 时间戳:24小时制格式:MMddHHmmss,即月日时分秒,定长10位,月、日、时、分、秒每段不足2位时左补0 | 0803192020 |
        | mts        | object   | 是       | 信息详情                                                             |            |
        ## mts包结构参数说明
        | 参数       | 类型     | 是否必须 | 描述                                                                 | 示例                           |
        |------------|----------|----------|----------------------------------------------------------------------|--------------------------------|
        | chtype     | int      | 是       | 消息类型:<br>1: 短信<br>3: 微信<br>4: 邮件<br>7: apppush<br>10: 企业微信 | 发送短信消息示例:1            |
        | msg        | String   | 是       | 消息内容:最大支持970个字符(不含签名),发送时请预留至少10个字的签名长度,一个字母或一个汉字都视为一个字<br>编码方式:UTF-8 | 消息内容:“验证码:6666,打死都不要告诉别人哦！” |
        | dest       | String   | 是       | 接收对象:可以是手机号、邮件、微信openid等,根据消息类型确定接收对象,单发接口只有一个接收对象。<br>邮件新格式为:<br>接收对象类型=接收对象 | 示例:13711xxxxx<br>邮件接收对象示例:<EMAIL> 或者 TO=<EMAIL> |
        | custid     | String   | 否       | 用户自定义流水号:该条消息在您业务系统的ID,比如订单号或者短信发送记录的流水号。填写后发送状态返回值内将包含用户自定义流水号。<br>最大可支持64位的ASCII字符串,字母、数字、下划线、减号,如不需要则不用提交此字段。 | 示例:b3d0a2783d31b218b573 |
        | svrtype    | String   | 否       | 业务编码:最大可支持32个长度的英文字母、数字的字符串                   | 示例:M000000                  |
        | syscode    | String   | 否       | 用户系统编码:最大可支持32个长度的英文字母、数字的字符串,如不需要则不用提交此字段。 | 示例:S001                      |
        | exno       | String   | 否       | 扩展号:长度不能超过6位,注意通道号+扩展号的总长度                   | 示例:0006                      |
        | validtime  | String   | 否       | 有效存活时间(可选):此处格式为:yyyyMMddHHmmss,即年月日时分秒,14位,24小时制计时。例如2012年12月31日16时59分0秒,即为20121231165900。该值小于或等于0或无该字段时,按系统默认有效期(24小时)执行。该值若大于当前时间48小时,按有效期(48小时)执行。有效存活时间不能小于当前时间。          | 示例:20160819165900                      |
        | rptflag    | int      | 否       | 是否需要状态报告:0:表示不需要,1:表示需要。该值若不填,系统默认为需要状态报告。                   | 示例:1                     |
        '''
        try:
            base_url = f'{self.ALERT_PROTOCOL}://{self.ALERT_ADDR}:{self.ALERT_PORT}/umc/v3/std/msg/single_txt_send'
            timestamp = str(datetime.now().strftime('%m%d%H%M%S'))
            mts_data = {
                        'chtype':'1',
                        'msg':msg,
                        'dest':receiver,
                        'custid':None,
                        'svrtype':None,
                        'syscode':None,
                        'exno':None,
                        'validtime':None,
                        'rptflag':None,
                        }
            param = {
                    'userid': self.ALERT_MANAGE_ACC,
                    'pwd':self.encrypt_pwd(timestamp),
                    'timestamp':timestamp,
                    'mts':mts_data
                    }
            header = {'Accept': 'application/json'}
            response = general_requests(base_url,method='post',headers=header,json=param)
            response_json = json.loads(response)
            logger.info(f"短信告警发送结果：{response_json}|发送内容{msg}")
        except Exception as e:
            logger.error(f'发送告警失败:{str(e)}|发送内容{msg}',exc_info=True)

if __name__ == '__main__':
    from util import load_env
    load_env(f'{os.path.dirname(__file__)}/.env')
    ap = AlertPlatform()
    ap.send_alert('你好','15602386279')