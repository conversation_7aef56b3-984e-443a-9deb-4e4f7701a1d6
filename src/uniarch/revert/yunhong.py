import os
import json
import pandas as pd
from datetime import datetime, timedelta

# 读取 Excel 文件
file_path = 'yunhong_0830.xlsx'
df = pd.read_excel(file_path)

# 初始化字典
result = {}

# 将数字转换为日期
def number_to_date(number):
  base_date = datetime(1900, 1, 1)
  return base_date + timedelta(days=number)

# 遍历每一行数据
for index, row in df.iterrows():
  # 格式化监控日期
  monitor_date = number_to_date(row['监控日期'] - 2)
  monitor_date_str = monitor_date.strftime('%Y-%m-%d')  # 格式化日期输出
  
  # IP 地址
  ip = row['ip']
  
  # 将主机数据添加到结果字典
  if monitor_date_str not in result: result[monitor_date_str] = {}

  result[monitor_date_str][ip] = row['内存平均使用率']

with open('yunhong_mem.json', 'w', encoding='utf-8') as f:
  json.dump(result, f, ensure_ascii=False, indent=2)

