import random

# IP address list
ip_list = [
    "*********", "********", "*************", "************", "************", "************",
    "*************", "*************", "*************", "*************", "*************", "*************",
    "*************", "*************", "********", "*************", "*************", "***********",
    "**********", "**********", "**********", "************", "***********", "************",
    "************", "************", "*************", "*************", "**********", "**********",
    "**********", "***********", "*************", "************", "**********", "**********",
    "*************", "**********", "*************", "*************", "************", "**********",
    "***********", "**********2", "*************", "************", "************", "***********7",
    "*************", "************", "************", "*************", "************", "************",
    "************", "**********", "************", "*************", "************", "*************",
    "192.168.0.96", "192.168.3.7", "************6", "192.168.0.105", "192.168.120.58", "192.168.120.56",
    "192.168.174.1", "192.168.120.11", "192.168.110.96", "192.168.1.9", "192.168.110.32", "192.168.120.44",
    "192.168.31.43", "192.168.120.57", "192.168.120.9", "192.168.120.43", "192.168.110.66", "192.168.63.229",
    "192.168.110.94", "192.168.43.73", "192.168.120.55", "192.168.120.61", "192.168.120.62", "192.168.120.48",
    "10.64.11.218", "192.168.110.77", "10.64.11.244", "192.168.110.12", "192.168.110.18", "192.168.110.20",
    "192.168.1.15", "192.168.120.41", "192.168.110.69", "192.168.120.7", "192.168.1.20", "192.168.70.63",
    "10.105.106.54", "192.168.1.67", "10.105.106.60", "192.168.1.6", "192.168.120.29", "192.168.68.82",
    "192.168.0.157", "192.168.110.17", "192.168.110.8", "**************", "**************", "**************",
    "***********", "**************", "************", "**************", "************", "************"
]

# System and department names
system_names = ["在线商城系统", "新闻管理系统", "拨测管理系统", "农田实时监测系统", "教学管理系统", "电子警察系统"]
department_names = ["商务局", "民政局", "大数据局", "农业局", "教育局", "交通局"]

# Shuffle the lists to randomize the assignment
random.shuffle(ip_list)
random.shuffle(system_names)
random.shuffle(department_names)

# Assign IP addresses to systems and departments
output = []
system_index = 0
department_index = 0
for i in range(0, len(ip_list), 3):
    system = system_names[system_index % len(system_names)]
    department = department_names[department_index % len(department_names)]
    for j in range(i, min(i + 3, len(ip_list))):
        output.append(f'"{ip_list[j]}","{system}","{department}"')
    system_index += 1
    if system_index % 3 == 0:
        department_index += 1

# Join the output into a CSV format
csv_output = "@\n".join(output) + "@"

print(csv_output)

