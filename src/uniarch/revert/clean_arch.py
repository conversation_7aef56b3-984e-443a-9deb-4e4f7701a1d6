import requests
import json

# Elasticsearch服务器地址
es_url = "http://127.0.0.1:9200"
index_name = "uniarch_0"

# 删除索引中的所有数据
delete_query = {
    "query": {
        "match_all": {}
    }
}

delete_url = f"{es_url}/{index_name}/_delete_by_query"
response = requests.post(delete_url, headers={"Content-Type": "application/json"}, data=json.dumps(delete_query))

# 检查响应状态码
if response.status_code == 200:
    print(f"Successfully deleted all data from the index '{index_name}'.")
else:
    print(f"Failed to delete data. Status code: {response.status_code}, Response: {response.text}")

