import os
import json
import random
import argparse

MERGE_SUM_KEYS = [
  'HOST_MONITOR_DOC_COUNT', 
  'HOST_SESSION_COUNT',
  'HOST_BYTES',
  'HOST_TCP_BYTES',
  'HOST_LIVE_BYTES',
  'HOST_TCP_SESSION_COUNT',
  'HOST_HTTP_SESSION_COUNT',
  'HOST_ACCESS_IP_COUNT',
  'HOST_WEB_DOMAIN_COUNT',
  'HOST_NEW_CALL_COUNT',
  'HOST_ACCESS_SRC_IP_COUNT',
  "HOST_SQL_COUNT",
  "HOST_SQL_ADD_COUNT",
  "HOST_SQL_ADD_SIZE_BYTES",
  "HOST_RETCODE_SQL_ERR_COUNT",
  "HOST_RETCODE_4xx_ERR_COUNT",
  "HOST_RETCODE_5xx_ERR_COUNT",
  "HOST_RETCODE_ERR_COUNT",
  "HOST_RETRANS_COUNT",
  "HOST_PKTS_COUNT",
  "HOST_NET_DELAY_AVG",
  "HOST_RETCODE_SQL_ERR_RATIO",
  "HOST_RETCODE_4xx_ERR_RATIO",
  "HOST_RETCODE_5xx_ERR_RATIO",
  "HOST_RETCODE_ERR_RATIO",
  "HOST_RETRANS_RATIO",
  "HOST_RESOURCE_INDEX",
  "HOST_PERFORMANCE_INDEX",
  "HOST_DATABASE_INDEX",
  "HOST_NETWORK_INDEX",
  "HOST_EFFICIENCY_SCORE",
  "HOST_CPU_CORE_USE",
  "HOST_MEM",
  "HOST_MEM_RATIO",
  "HOST_DISK_USE",
  "HOST_CPU_MAX",
  "HOST_CPU_AVG",
  "HOST_MEM_MAX",
  "HOST_MEM_AVG",
  "HOST_DISK_MAX",
  "HOST_DISK_AVG",
  "HOST_DISK_USE_RATIO",
  "HOST_PER_CPU_SESSION",
  "HOST_ENERGY_EFFICIENCY_RATIO",
  "HOST_MAX_BUSI_LOAD",
  'HOST_APP_RESPONSE_MAX', 
  "HOST_SQL_RESPONSE_MAX",
  'HOST_APP_RESPONSE_AVG',
  "HOST_SQL_RESPONSE_AVG"
]

def process_files(source_dir, target_dir):
  # 确保目标目录存在
  os.makedirs(target_dir, exist_ok=True)

  # 遍历源目录中的所有文件
  for filename in os.listdir(source_dir):
    source_file = os.path.join(source_dir, filename)
    
    # 使用目标目录日期更新目标文件名
    target_filename = filename.replace(source_dir, target_dir)
    target_file = os.path.join(target_dir, target_filename)

    # 读取JSON文件
    with open(source_file, 'r') as f:
      data = json.load(f)

    # 遍历每个主机的数据
    for host, metrics in data.items():
      # 更新ARCH_START为目标目录日期
      metrics['ARCH_START'] = target_filename.replace('.json', '')

      # 删除HOST_WEB_DOMAIN键
      if 'HOST_WEB_DOMAIN' in metrics:
        metrics.pop('HOST_WEB_DOMAIN')

      # 处理MERGE_SUM_KEYS中的键
      for key, value in list(metrics.items()):
        if key in MERGE_SUM_KEYS:
          fluctuation_factor = random.uniform(0.7, 1.3)
          if 'COUNT' in key or 'BYTES' in key:
            metrics[key] = int(value * fluctuation_factor)
          else:
            metrics[key] = value * fluctuation_factor

      # 单独处理HOST_SQL_ADD_COUNT
      if metrics['HOST_SQL_ADD_COUNT'] == 0:
         metrics['HOST_SQL_ADD_SIZE_BYTES'] = 0

    # 将处理后的数据保存到目标目录
    with open(target_file, 'w') as f:
      json.dump(data, f, indent=2)

if __name__ == "__main__":
  parser = argparse.ArgumentParser(description='Process JSON files in source and target directories.')
  parser.add_argument('source_dir', type=str, help='Path to the source directory')
  parser.add_argument('target_dir', type=str, help='Path to the target directory')
  args = parser.parse_args()

  process_files(args.source_dir, args.target_dir)

