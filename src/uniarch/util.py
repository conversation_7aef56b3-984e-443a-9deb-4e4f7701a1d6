import os
import ipaddress
import datetime
import requests
import sqlite3
from log import logger
import pandas as pd
import urllib3
urllib3.disable_warnings()
from pathlib import Path

def get_latest_file(folder_path):
    folder = Path(folder_path)
    files = [f for f in folder.iterdir() if f.is_file()]
    if not files:
        return None
    latest_file = max(files, key=lambda f: f.stat().st_mtime)
    return latest_file

def load_env(file_path):
    with open(file_path, 'r',encoding='utf-8') as f:
        for line in f:
            line = line.strip()
            if line and not line.startswith('#'):
                key, value = line.split('=', 1)
                os.environ[key.strip()] = value.strip()

class SqliteHelper:
    """
    sqlite数据库操作类
    """
    def __init__(self,db_path):
        self.db_path = db_path
    
    def __enter__(self):
        try:
            self.conn = sqlite3.connect(self.db_path)
            self.cursor = self.conn.cursor()
            return self
        except Exception as e:
            logger.error(f"数据库连接异常：{e}",exc_info=True)
        
    def __exit__(self, exc_type, exc_val, exc_tb):
        try:
            self.conn.commit()
            self.cursor.close()
            self.conn.close()
        except Exception as e:
            logger.error(f"数据库关闭异常：{e}",exc_info=True)
    
    def execute_sql(self,sql,params=None):
        try:
            if params is None:
                self.cursor.execute(sql)
            else:
                self.cursor.execute(sql,params)
            return True
        except Exception as e:
            logger.error(f"执行sql异常：{e}",exc_info=True)
            return False
    
    def fetch_all(self):
        return self.cursor.fetchall()

    def fetch_one(self):
        return self.cursor.fetchone()

    def fetch_all_dict(self):
        columns = [col[0] for col in self.cursor.description]
        data = self.cursor.fetchall()
        return {} if data is None else [dict(zip(columns, row)) for row in data]
    
    def fetch_one_dict(self):
        columns = [col[0] for col in self.cursor.description]
        data = self.cursor.fetchone()
        return {} if data is None else dict(zip(columns, data))

class ScoreCalculator:
    """
    系统效能评估计算类
    """

    def __init__(self,system_data:pd.array,system_network_status,algrithm = "percent_score_calculator"):
        self.system_data = system_data
        self.system_network_status = system_network_status
        """计算策略
        percent_score_calculator 百分比计算
        range_score_calculator 区间评分计算
        """
        self.algrithm = algrithm

    def calculate_score(self):
        svc = round(self.system_visit_score(self.system_network_status,self.system_data.系统访问总量),2)
        svbc = round(self.system_visit_bytes_score(self.system_network_status,self.system_data['系统数据流量(MB)']),2)
        dbv = round(self.database_visit_score(self.system_network_status,self.system_data.数据库访问量),2)
        dbvb = round(self.database_visit_bytes_score(self.system_network_status,self.system_data["数据库访问流量(MB)"]),2)
        cpu = round(self.cpu_active_score(self.system_network_status,self.system_data["虚拟CPU(vCPU)活跃累计时长"]),2)
        catalog = 5 if self.system_data["公共数据资源目录编目"] > 0 else 0
        result = {
                "日期(年月)":self.system_data["日期(年月)"] if "日期(年月)" in self.system_data else self.system_data["日期"][:8],
                "一级单位":self.system_data["一级单位"],
                "系统名称":self.system_data["系统名称"],
                "系统编号":self.system_data["系统编号"],
                "IP数量":self.system_data.get("主机数量",0),
                "系统效能赋分":svc+svbc+dbv+dbvb+cpu+catalog,
                "系统访问量赋分":svc,
                "系统访问流量赋分":svbc,
                "数据库访问量赋分":dbv,
                "数据库访问流量赋分":dbvb,
                "虚拟CPU(VCPU)活跃累计时长赋分":cpu,
                "公共数据资源目录编目赋分":catalog,
                "网络类型":self.system_network_status,
                }
        return result

    def system_visit_score(self,network_status,system_visit_total):
        """
        # 系统访问量赋分
        指标说明:以政务信息系统每月系统总访问量为评估指标\n
        权重:30\n
        | 赋分方法互联网侧系统          | 赋分方法政务网侧系统         |
        | ------------------- | ------------------ |
        | 0 分:x<1 万次          | 0 分:x<1,000 次      |
        | 5 分:1 万次≤x<10 万次    | 5 分:1,000 次≤x<1 万次 |
        | 10 分:10 万次≤x<50 万次  | 10 分:1 万次≤x<5 万次   |
        | 20 分:50 万次≤x<200 万次 | 20 分:5 万次≤x<20 万次  |
        | 30 分:x≥200 万次       | 30 分:x≥20 万次       |
        """
        if network_status == "互联网":
            if system_visit_total < 10000:
                return 0
            elif system_visit_total < 100000:
                return (system_visit_total*5/100000) if self.algrithm == "percent_score_calculator" else 5
            elif system_visit_total < 500000:
                return (system_visit_total*10/500000) if self.algrithm == "percent_score_calculator" else 10
            elif system_visit_total < 2000000:
                return (system_visit_total*20/2000000) if self.algrithm == "percent_score_calculator" else 20
            else:
                return 30
        elif network_status == "政务外网":
            if system_visit_total < 1000:
                return 0
            elif system_visit_total < 10000:
                return (system_visit_total*5/10000) if self.algrithm == "percent_score_calculator" else 5
            elif system_visit_total < 50000:
                return (system_visit_total*10/50000) if self.algrithm == "percent_score_calculator" else 10
            elif system_visit_total < 200000:
                return (system_visit_total*20/200000) if self.algrithm == "percent_score_calculator" else 20
            else:
                return 30
        else:
            return 0

    def system_visit_bytes_score(self,network_status,system_visit_total_bytes):
        """
        # 系统访问流量赋分
        指标说明:以政务信息系统每月系统总数据流量为评估指标\n
        权重:15\n
        | 赋分方法互联网侧系统 | 赋分方法政务网侧系统 |
        |----------------------|----------------------|
        | 0 分:x < 500 MB     | 0 分:x < 100 MB     |
        | 3 分:500 MB ≤x<5 GB | 3 分:100 MB ≤x<1000 MB |
        | 6 分:5 GB ≤x<25 GB  | 6 分:1000 MB ≤x<5 GB |
        | 10 分:25 GB ≤x<100 GB | 10 分:5 GB ≤x<20 GB |
        | 15 分:x≥100 G       | 15 分:x≥20 GB       |
        """
        if network_status == "互联网": 
            if system_visit_total_bytes < 500:
                return 0
            elif system_visit_total_bytes < 5120:
                return (system_visit_total_bytes*3/5120) if self.algrithm == "percent_score_calculator" else 3
            elif system_visit_total_bytes < 25600:
                return (system_visit_total_bytes*6/25600) if self.algrithm == "percent_score_calculator" else 6
            elif system_visit_total_bytes < 102400:
                return (system_visit_total_bytes*10/102400) if self.algrithm == "percent_score_calculator" else 10
            else:
                return 15
        elif network_status == "政务外网":
            if system_visit_total_bytes < 100:
                return 0
            elif system_visit_total_bytes < 1000:
                return (system_visit_total_bytes*3/1000) if self.algrithm == "percent_score_calculator" else 3
            elif system_visit_total_bytes < 5120:
                return (system_visit_total_bytes*6/5120) if self.algrithm == "percent_score_calculator" else 6
            elif system_visit_total_bytes < 20480:
                return (system_visit_total_bytes*10/20480) if self.algrithm == "percent_score_calculator" else 10
            else:
                return 15
        else:
            return 0

    def database_visit_score(self,network_status,database_visit_total):
        """
        # 数据库访问量赋分
        指标说明:以政务信息系统每月数据库活跃情况为评估指标\n
        权重:20\n
        | 赋分方法互联网侧系统 | 赋分方法政务网侧系统 |
        |----------------------|----------------------|
        | 0 分:x < 5,000 条   | 0 分:x < 5,000 条   |
        | 5 分:5,000 条≤x<2 万条 | 5 分:5,000 条≤x<2 万条 |
        | 10 分:2 万条≤x<50 万条 | 10 分:2 万条≤x<50 万条 |
        | 15 分:50 万条≤x<100 万条 | 15 分:50 万条≤x<100 万条 |
        | 20 分:x≥100 万条    | 20 分:x≥100 万条    |
        """
        if network_status == "互联网": 
            if database_visit_total < 5000:
                return 0
            elif database_visit_total < 20000:
                return (database_visit_total*5/20000) if self.algrithm == "percent_score_calculator" else 5
            elif database_visit_total < 50000:
                return (database_visit_total*10/50000) if self.algrithm == "percent_score_calculator" else 10
            elif database_visit_total < 100000:
                return (database_visit_total*15/100000) if self.algrithm == "percent_score_calculator" else 15
            else:
                return 20
        elif network_status == "政务外网":
            if database_visit_total < 5000:
                return 0
            elif database_visit_total < 20000:
                return (database_visit_total*5/20000) if self.algrithm == "percent_score_calculator" else 5
            elif database_visit_total < 50000:
                return (database_visit_total*10/50000) if self.algrithm == "percent_score_calculator" else 10
            elif database_visit_total < 100000:
                return (database_visit_total*15/100000) if self.algrithm == "percent_score_calculator" else 15
            else:
                return 20
        else:
            return 0

    def database_visit_bytes_score(self,network_status,database_visit_total_bytes):
        """
        # 数据库访问流量赋分
        指标说明:以政务信息系统每月数据库的总访问流量为评估指标\n
        权重:15\n
        | 赋分方法互联网侧系统 | 赋分方法政务网侧系统 |
        |----------------------|----------------------|
        | 0 分:x < 10 MB      | 0 分:x < 10 MB      |
        | 3 分:10 MB ≤x<100 MB | 3 分:10 MB ≤x<100 MB |
        | 6 分:100 MB ≤x<500 MB | 6 分:100 MB ≤x<500 MB |
        | 10 分:500 MB ≤x<2000 MB | 10 分:500 MB ≤x<2000 MB |
        | 15 分:x≥2000 MB     | 15 分:x≥2000 MB     |
        """
        if network_status == "互联网": 
            if database_visit_total_bytes < 10:
                return 0
            elif database_visit_total_bytes < 100:
                return (database_visit_total_bytes*3/100) if self.algrithm == "percent_score_calculator" else 3
            elif database_visit_total_bytes < 500:
                return (database_visit_total_bytes*6/500) if self.algrithm == "percent_score_calculator" else 6
            elif database_visit_total_bytes < 2000:
                return (database_visit_total_bytes*10/2000) if self.algrithm == "percent_score_calculator" else 10
            else:
                return 15
        elif network_status == "政务外网":
            if database_visit_total_bytes < 10:
                return 0
            elif database_visit_total_bytes < 100:
                return (database_visit_total_bytes*3/100) if self.algrithm == "percent_score_calculator" else 3
            elif database_visit_total_bytes < 500:
                return (database_visit_total_bytes*6/500) if self.algrithm == "percent_score_calculator" else 6
            elif database_visit_total_bytes < 2000:
                return (database_visit_total_bytes*10/2000) if self.algrithm == "percent_score_calculator" else 10
            else:
                return 15
        else:
            return 0

    def cpu_active_score(self,network_status,cpu_active_minutes):
        """
        # CPU活跃时长
        指标说明:以政务信息系统每月vCPU平均使用率超过某一数值的时长为评估依据\n
        权重:15\n
        | 赋分方法互联网侧系统 | 赋分方法政务网侧系统 |
        |----------------------|----------------------|
        | 0 分:x < 1.5 小时   | 0 分:x < 1.5 小时   |
        | 3 分:1.5 小时≤x<3 小时 | 3 分:1.5 小时≤x<3 小时 |
        | 6 分:3 小时≤x<6 小时   | 6 分:3 小时≤x<6 小时   |
        | 10 分:6 小时≤x<9 小时  | 10 分:6 小时≤x<9 小时 |
        | 15 分:x≥9 小时       | 15 分:x≥9 小时       |
        """
        if network_status == "互联网": 
            if cpu_active_minutes < 90:
                return 0
            elif cpu_active_minutes < 180:
                return (cpu_active_minutes*3/180) if self.algrithm == "percent_score_calculator" else 3
            elif cpu_active_minutes < 360:
                return (cpu_active_minutes*6/360) if self.algrithm == "percent_score_calculator" else 6
            elif cpu_active_minutes < 540:
                return (cpu_active_minutes*10/540) if self.algrithm == "percent_score_calculator" else 10
            else:
                return 15
        elif network_status == "政务外网":
            if cpu_active_minutes < 90:
                return 0
            elif cpu_active_minutes < 180:
                return (cpu_active_minutes*3/180) if self.algrithm == "percent_score_calculator" else 3
            elif cpu_active_minutes < 360:
                return (cpu_active_minutes*6/360) if self.algrithm == "percent_score_calculator" else 6
            elif cpu_active_minutes < 540:
                return (cpu_active_minutes*10/540) if self.algrithm == "percent_score_calculator" else 10
            else:
                return 15
        else:
            return 0

class RunRecordHelper:
    def __init__(self,db_path):
        self.db_path = db_path
    
    def create_run_record_table(self):
        with SqliteHelper(self.db_path) as db:
            # 检查是否已有表格
            
            res =  db.execute_sql("SELECT name FROM sqlite_master WHERE type='table' AND name='run_record'")
            exist_db = [] if not res else [row['name'] for row in db.fetch_all_dict()]
            if "run_record" in exist_db:
                return True
            encode_sql = """PRAGMA encoding = 'UTF-8';"""
            db.execute_sql(encode_sql)
            sql = """
                CREATE TABLE IF NOT EXISTS run_record (
                    id TEXT,
                    start_ts INTEGER,
                    end_ts INTEGER,
                    step INTEGER,
                    threads INTEGER,
                    config TEXT,
                    status TEXT,
                    result TEXT,
                    create_time TEXT,
                    update_time TEXT
                )"""
            create_result = db.execute_sql(sql)
            if not create_result:
                logger.warning("创建数据库表run_record失败")
            return create_result
    
    def insert_run_record(self,run_id,start_ts,end_ts,step,threads,config,status,result,create_time=datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")):
        with SqliteHelper(self.db_path) as db:
            sql = """
                INSERT INTO run_record (id,start_ts,end_ts,step,threads,config,status,result,create_time,update_time)
                VALUES (?,?,?,?,?,?,?,?,?,?)
            """
            create_result = db.execute_sql(sql,(run_id,start_ts,end_ts,step,threads,config,status,result,create_time,create_time))
            if not create_result:
                logger.warning(f"{run_id}-{start_ts}-{end_ts}插入启动记录数据失败")
            return create_result
    
    def get_last_run_record(self):
        with SqliteHelper(self.db_path) as db:
            sql = """
                SELECT * FROM run_record WHERE status = 'success' ORDER BY end_ts DESC LIMIT 1
            """
            res = db.execute_sql(sql)
            if not res:
                return None
            result = db.fetch_one_dict()
            return result
    
    def update_run_record(self,id,status,result):
        with SqliteHelper(self.db_path) as db:
            sql = """
                UPDATE run_record SET status=?,result=?,update_time=? WHERE id=?
            """
            update_result = db.execute_sql(sql,(status,result,datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S"),id))
            return update_result

def is_ipaddress(address):
    try:
        ip = ipaddress.ip_address(address)
        return True
    except ValueError:
        return False

def is_chinese(text):
    """
    判断输入的字符串是否含有中文
    :param text: 输入的字符串
    :return: 如果字符串是中文返回True，否则返回False
    """
    if not isinstance(text,str):
        # None或其他情况
        return  False
    for char in text:
        if '\u4e00' <= char <= '\u9fff':
            return True
    return False

# 给定开始时间及结束时间，按时间步长生成时间范围内按步长生成的时间段
def gen_ts_range(start_ts, end_ts, step=None):
    current = start_ts
    while current < end_ts:
        if step is None:
            step = end_ts - start_ts
        end_time = current + step
        # next_time = min(s_ts-1, end_ts)
        yield current, end_time-1
        current = end_time

# 获取时间戳当前分钟的正分钟时刻
def get_current_min_ts(now = None):
    if now is None:
        now = datetime.datetime.now()
    return now.timestamp() - now.second

def get_previous_dates(input_date_str, period):
    """
    根据输入的日期返回前一天、上周同一天、上个月同一天或前一年同一天的同一时间点。

    :param input_date_str: 输入的日期字符串，格式为'%Y-%m-%d %H:%M:%S'
    :param period: 比较的周期，可以是'day'、'week'、'month'或'year'
    :return: 对应周期的前一天、上周同一天、上个月或前一年同一天的同一时间点的日期字符串
    """
    # 将输入的日期字符串转换为datetime对象
    input_date = datetime.datetime.strptime(input_date_str, '%Y-%m-%d %H:%M:%S')

    if period == 'day':
        # 返回前一天的同一时间点
        previous_date = input_date - datetime.timedelta(days=1)
    elif period == 'week':
        # 返回上周同一天的同一时间点
        previous_date = input_date - datetime.timedelta(weeks=1)
    elif period == 'month':
        # 返回上个月同一天的同一时间点
        year = input_date.year if input_date.month > 1 else input_date.year - 1
        month = input_date.month - 1 if input_date.month > 1 else 12
        day = input_date.day
        # 如果输入日期是2月29日，且年份不是闰年，则返回空字符串
        if month == 2 and day == 29 and not ((year % 4 == 0 and year % 100 != 0) or year % 400 == 0):
            return ''
        # 如果上个月没有同一天，则返回空字符串
        if day == 31 and month in [4, 6, 9, 11]:
            return ''
        previous_date = input_date.replace(year=year, month=month, day=day)
    elif period == 'year':
        # 返回去年同一天的同一时间点
        year = input_date.year - 1
        month = input_date.month
        day = input_date.day
        # 如果输入日期是2月29日，且年份不是闰年，则返回空字符串
        if month == 2 and day == 29 and not ((year % 4 == 0 and year % 100 != 0) or year % 400 == 0):
            return ''
        previous_date = input_date.replace(year=year, month=month, day=day)
    else:
        raise ValueError("Invalid period. Choose 'day', 'week', 'month' or 'year'.")

    # 格式化datetime对象为日期字符串
    return previous_date.strftime('%Y-%m-%d %H:%M:%S')

def get_previous_dates_ts(input_ts_str, period):
    """
    根据输入的时间戳返回前一天、上周同一天、上个月或前一年同一天的同一时间点时间戳。

    :param input_ts_str: 输入的时间戳
    :param period: 比较的周期，可以是'day'、'week'、'month'或'year'
    :return: 对应周期的前一天、上周同一天、上个月或前一年同一天的同一时间点的时间戳
    """
    # 将输入的日期字符串转换为datetime对象
    time_format = "%Y-%m-%d %H:%M:%S"
    date = get_previous_dates(datetime.datetime.fromtimestamp(input_ts_str).strftime(time_format), period)
    return int(datetime.datetime.strptime(date, time_format).timestamp())

def calculate_sequential(this_period,last_period):
    # 计算环比
    if last_period == 0:
        return 0
    return (this_period - last_period) / last_period * 100

def general_requests(url,method,headers,data=None,params=None,json=None,verify=False):
    if method == 'get':
        response = requests.get(url, headers=headers, params=params,verify=verify)
    elif method == 'post':
        response = requests.post(url, headers=headers, data=data, params=params, json=json,verify=verify)
    elif method == 'delete':
        response = requests.delete(url, headers=headers, data=data, params=params, json=json,verify=verify)
    else:
        raise ValueError("Invalid method. Choose 'get' or 'post'.")
    if response.status_code >= 400:
        raise ValueError(f"{url}Request failed with status code {response.status_code}")
    return response.content.decode("utf-8")

def node_generator(node_list):
    while True:
        for node in node_list:
            yield node

if __name__ == '__main__':
    # from datetime import  datetime
    # print(datetime.fromtimestamp(get_current_min_ts()).strftime("%Y%m%d%H%M%S"))
    # for et,st in  gen_ts_range(1560000000, 1560000030):
    #     print(et)
    #     print(st)
    date = '2024-01-31 15:00:00'
    print(get_previous_dates(date, 'day'))
    print(get_previous_dates(date, 'week'))
    print(get_previous_dates(date, 'month'))
    print(get_previous_dates(date, 'year'))