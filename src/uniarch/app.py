import os
import time
import yaml
import asyncio
import pandas as pd
import numpy as np
from fastapi import FastAPI
from fastapi.responses import FileResponse
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
from typing import Any, Generic, Optional, TypeVar
from data_handlers import get_unit_system, match_report_files,process_single_json_file,save_department_report,merge_single_json_file,json_to_dataframe,zip_excels,gen_evaluate_report,json_to_dataframe_sp,save_department_score_report,merge_single_system_hour_json_file,network_field_decision,make_hour_dataframe,get_system_catalogcount
from log import logger
from main import load_env
from scheduler import start_scheduler, shutdown_scheduler, daily_summary_task, monthly_summary_task, daily_inspection_task
from contextlib import asynccontextmanager
from uniserver import UniserverApiManager
from uuid import uuid4
from collections import defaultdict
from concurrent.futures import ThreadPoolExecutor

load_env(f'{os.path.dirname(__file__)}/.env')

T = TypeVar("T")

class ReportOutputBase(BaseModel):
    date_start: str
    date_end: str
    system: list = []

    class Config:
        from_attributes = True

class CheckOutputBase(ReportOutputBase):
    dimension: str
    byte_str: str = ""
    
class MonthlyReportOutput(CheckOutputBase):
    algrithm: str = "percent_score_calculator"

class TriggerMonthlySummary(BaseModel):
    day_delta: int = 1
    algrithm: str = "percent_score_calculator"

class ResponseModel(Generic[T]):
    def __init__(
        self, *, code: int = 200, data: Optional[T] = None, message: str = "Success",success: bool = True
    ):
        self.code = code
        self.data = data
        self.message = message
        self.success = success

    def dict(self):
        return {"code": self.code, "data": self.data, "msg": self.message, "success": self.success}
    


with open(f'{os.path.dirname(__file__)}/report_settings.yaml', 'r', encoding='utf-8') as file:
    report_config = yaml.safe_load(file)

with open(f'{os.path.dirname(__file__)}/settings.yaml', 'r', encoding='utf-8') as file:
    uniarch_config = yaml.safe_load(file)

def success(*, code: int = 200, data: Any = None, message: str = "Success",success = True) -> dict:
    if isinstance(data, BaseModel):
        data = data.model_dump()
    return ResponseModel(code=code, data=data, message=message,success=success).dict()


def error(*, code: int = 400, message: str = "Error",success = False) -> dict:
    return ResponseModel(code=code, message=message,success=success).dict()

@asynccontextmanager
async def lifespan(app: FastAPI):
    # 启动调度器
    start_scheduler()
    logger.info("应用已启动")
    yield
    # 关闭调度器
    await shutdown_scheduler()
    logger.info("应用已关闭")

app = FastAPI(lifespan=lifespan)

app.add_middleware(
    CORSMiddleware,
    allow_origins=['*'],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

@app.get("/unit_system_list", description="获取单位系统列表")
async def get_unit_system_list():
    try:
        uniserver_obj = UniserverApiManager()
        unit_system_list = get_unit_system(uniserver_obj)
        return success(data=unit_system_list,message="获取成功")
    except Exception as e:
        logger.error(f"获取单位系统列表异常{str(e)}",exc_info=True)
        return error(code=500, message="获取单位系统列表异常")

@app.post("/download_report", description="下载报告")
async def download_report(output_param:CheckOutputBase):
    try:
        system_data = defaultdict(lambda: defaultdict(lambda: defaultdict(lambda: {
        'UNIT_NAME': '',
        'SYSTEM_CODE': '',
        'SYSTEM_VISIT_TOTAL': 0,
        'SYSTEM_VISIT_TOTAL_BYTES': 0.0,
        'DATABASE_VISIT_TOTAL': 0.0,
        'DATABASE_VISIT_TOTAL_BYTES': 0.0,
        'CPU_ACTIVE_MINUTES': 0,
        'DATABASE_OPR':0,
        'DATABASE_PACKAGES_TOTAL': 0.0,
        'DATABASE_BYTES_TOTAL': 0.0,
        'DATABASE_ACK_PACKAGES_TOTAL' : 0.0,
        'HOST_NUM':0,
        }))) 
        MAX_CONCURRENT_READS = report_config['MAX_ASYNC_CONCURRENT_READS']
        semaphore = asyncio.Semaphore(MAX_CONCURRENT_READS)
        task_id = str(uuid4())
        date = f"{output_param.date_start}-{output_param.date_end}"
        logger.info(f"{task_id}开始生成报告{date}|{output_param.system}")
        gen_time = time.strftime("%Y%m%d%H%M%S", time.localtime())
        save_path = os.path.join(uniarch_config["general_config"]["RESULT_PATH"], 
                                f"{report_config['file_name']}/{gen_time}")
        if not os.path.exists(save_path):
            os.makedirs(save_path)
        json_files = await match_report_files(task_id,report_config['arch_data_path'],date)
        if not json_files:
            return success(message="没有报告数据")
        batch_size = report_config['JSON_FILE_BATCH_SIZE']
        for i in range(0, len(json_files), batch_size):
            logger.info(f"{task_id}正在读取 {i}-{i+batch_size}批数据")
            # 分批处理避免内存溢出
            batch = json_files[i:i+batch_size]
            local_system_data_list = await asyncio.gather(*(process_single_json_file(
                                                            semaphore,
                                                            p, 
                                                            ) for p in batch))
            logger.info(f"{task_id}正在聚合 {i}-{i+batch_size}批数据")
            for local_system_data in local_system_data_list:
                if local_system_data is None:
                    continue
                await merge_single_system_hour_json_file(system_data,local_system_data,report_config['host_cpu_avg_threashold'])
        network_field_decision_config = await network_field_decision(system_data)
        hour_report_df = await make_hour_dataframe(system_data,output_param.system,report_config['ratio'],network_field_decision_config,"MB")
        if hour_report_df.empty:
            logger.info(f"{task_id}没有报告数据")
            return error(message="没有报告数据")
        if output_param.dimension == "day":
            # 通过透视按天聚合
            pivot_df = pd.pivot_table(hour_report_df, index=["日期","一级单位","系统编号","系统名称"],values=["数据库操作量","系统访问总量","系统数据流量(MB)","数据库访问量","数据库访问流量(MB)","虚拟CPU(vCPU)活跃累计时长"],aggfunc="sum").reset_index()
        elif output_param.dimension == "month":
            system_catalogcount_dict = get_system_catalogcount()
            def get_catalogcount(row):
                return system_catalogcount_dict.get(row["一级单位"],{}).get(row["系统编号"],0)
            # 通过透视按月聚合
            pivot_df = pd.pivot_table(hour_report_df, index=["一级单位","系统编号","系统名称"],values=["数据库操作量","系统访问总量","系统数据流量(MB)","数据库访问量","数据库访问流量(MB)","虚拟CPU(vCPU)活跃累计时长"],aggfunc="sum").reset_index()
            pivot_df["公共数据资源目录编目"] = pivot_df.apply(get_catalogcount, axis=1)
            pivot_df['日期(年月)'] = output_param.date_start[:6]
            # 选择月维度时，输出评分报告及各系统月度异常数据评估结果
            month_evaluate_score_df = gen_evaluate_report(pivot_df)
        department_list = hour_report_df['一级单位'].unique()
        file_list = []
        logger.info(f"{task_id}保存各单位表格")
        with ThreadPoolExecutor() as executor:
            futures = [executor.submit(save_department_score_report,
                                       "",
                                       save_path,
                                       date,
                                       hour_report_df if output_param.dimension == "hour" else pivot_df,
                                       None if output_param.dimension != "month" else month_evaluate_score_df)]
            futures.extend([executor.submit(save_department_score_report,
                                            department,
                                            save_path,
                                            date,
                                            hour_report_df if output_param.dimension == "hour" else pivot_df,
                                            None if output_param.dimension != "month" else month_evaluate_score_df) 
                                            for department in department_list])
            department_report_list = [future.result() for future in futures]
        file_list.extend(department_report_list)
        logger.info(f"{task_id}进行压缩")
        if zip_excels(file_list,f"{save_path}.zip"):
            file_path =  f"{save_path}.zip"
        else:
            file_path =  None
        logger.info(f"{task_id}生成完成")
        if file_path:
            return FileResponse(path = file_path,
                                filename = os.path.basename(file_path),)
        else:
            return success(message="没有报告数据")
    except Exception as e:
        logger.error(f"生成报告异常{str(e)}",exc_info=True)
        return error(code=500, message="生成报告异常")

@app.post("/download_day_to_hour_report", description="下载按天放大小时报告")
async def download_day_to_hour_report(output_param:CheckOutputBase):
    try:
        system_data = defaultdict(lambda: defaultdict(lambda: defaultdict(lambda: {
        'UNIT_NAME': '',
        'SYSTEM_CODE': '',
        'SYSTEM_VISIT_TOTAL': 0,
        'SYSTEM_VISIT_TOTAL_BYTES': 0.0,
        'DATABASE_VISIT_TOTAL': 0.0,
        'DATABASE_VISIT_TOTAL_BYTES': 0.0,
        'CPU_ACTIVE_MINUTES': 0,
        'DATABASE_OPR':0,
        'DATABASE_PACKAGES_TOTAL': 0.0,
        'DATABASE_BYTES_TOTAL': 0.0,
        'DATABASE_ACK_PACKAGES_TOTAL' : 0.0,
        'HOST_NUM':0,
        }))) 
        MAX_CONCURRENT_READS = report_config['MAX_ASYNC_CONCURRENT_READS']
        semaphore = asyncio.Semaphore(MAX_CONCURRENT_READS)
        task_id = str(uuid4())
        date = f"{output_param.date_start}-{output_param.date_end}"
        logger.info(f"{task_id}开始生成报告{date}|{output_param.system}")
        gen_time = time.strftime("%Y%m%d%H%M%S", time.localtime())
        save_path = os.path.join(uniarch_config["general_config"]["RESULT_PATH"], 
                                f"{report_config['file_name']}/{gen_time}")
        if not os.path.exists(save_path):
            os.makedirs(save_path)
        json_files = await match_report_files(task_id,report_config['arch_data_path'],date)
        if not json_files:
            return success(message="没有报告数据")
        batch_size = report_config['JSON_FILE_BATCH_SIZE']
        for i in range(0, len(json_files), batch_size):
            logger.info(f"{task_id}正在读取 {i}-{i+batch_size}批数据")
            # 分批处理避免内存溢出
            batch = json_files[i:i+batch_size]
            local_system_data_list = await asyncio.gather(*(process_single_json_file(
                                                            semaphore,
                                                            p, 
                                                            ) for p in batch))
            logger.info(f"{task_id}正在聚合 {i}-{i+batch_size}批数据")
            for local_system_data in local_system_data_list:
                if local_system_data is None:
                    continue
                # 按小时合并数据
                await merge_single_system_hour_json_file(system_data,local_system_data,report_config['host_cpu_avg_threashold'])

        network_field_decision_config = await network_field_decision(system_data)
        report_df = await make_hour_dataframe(system_data,output_param.system,report_config['ratio'],network_field_decision_config,output_param.byte_str)
        if report_df.empty:
            logger.info(f"{task_id}没有报告数据")
            return error(message="没有报告数据")
        department_list = report_df['一级单位'].unique()
        file_list = []
        logger.info(f"{task_id}保存各单位表格")
        with ThreadPoolExecutor() as executor:
            futures = [executor.submit(save_department_report,"",save_path,date,report_df)]
            futures.extend([executor.submit(save_department_report, department,save_path,date,report_df) for department in department_list])
            department_report_list = [future.result() for future in futures]
        file_list.extend(department_report_list)
        logger.info(f"{task_id}进行压缩")
        if zip_excels(file_list,f"{save_path}.zip"):
            file_path =  f"{save_path}.zip"
        else:
            file_path =  None
        logger.info(f"{task_id}生成完成")
        if file_path:
            return FileResponse(path = file_path,
                                filename = os.path.basename(file_path),)
        else:
            return success(message="没有报告数据")
    except Exception as e:
        logger.error(f"生成报告异常{str(e)}",exc_info=True)
        return error(code=500, message="生成报告异常")

@app.post("/check_report", description="检查报告")
async def check_report(output_param:CheckOutputBase):
    try:
        system_data = defaultdict(lambda: defaultdict(lambda: {
        'UNIT_NAME': '',
        'SYSTEM_CODE': '',
        'SYSTEM_VISIT_TOTAL': 0,
        'SYSTEM_VISIT_TOTAL_BYTES': 0.0,
        'DATABASE_VISIT_TOTAL': 0,
        'DATABASE_VISIT_TOTAL_BYTES': 0.0,
        'CPU_ACTIVE_MINUTES': 0,
        'DATABASE_OPR':0,
        'DATABASE_PACKAGES_TOTAL': 0.0,
        'DATABASE_BYTES_TOTAL': 0.0,
        'DATABASE_ACK_PACKAGES_TOTAL' : 0,
        'HOST_NUM':0,
        })) 
        MAX_CONCURRENT_READS = report_config['MAX_ASYNC_CONCURRENT_READS']
        semaphore = asyncio.Semaphore(MAX_CONCURRENT_READS)
        task_id = str(uuid4())
        date = f"{output_param.date_start}-{output_param.date_end}"
        logger.info(f"{task_id}开始生成报告{date}|{output_param.system}")
        gen_time = time.strftime("%Y%m%d%H%M%S", time.localtime())
        save_path = os.path.join(uniarch_config["general_config"]["RESULT_PATH"], 
                                f"{report_config['file_name']}/{gen_time}")
        if not os.path.exists(save_path):
            os.makedirs(save_path)
        json_files = await match_report_files(task_id,report_config['arch_data_path'],date)
        if not json_files:
            return success(message="没有报告数据")
        batch_size = report_config['JSON_FILE_BATCH_SIZE']
        for i in range(0, len(json_files), batch_size):
            logger.info(f"{task_id}正在读取 {i}-{i+batch_size}批数据")
            # 分批处理避免内存溢出
            batch = json_files[i:i+batch_size]
            local_system_data_list = await asyncio.gather(*(process_single_json_file(
                                                            semaphore,
                                                            p, 
                                                            ) for p in batch))
            logger.info(f"{task_id}正在聚合 {i}-{i+batch_size}批数据")
            for local_system_data in local_system_data_list:
                if local_system_data is None:
                    continue
                await merge_single_json_file(system_data,local_system_data,output_param.dimension,report_config['host_cpu_avg_threashold'])
        report_df = await json_to_dataframe(system_data,output_param.system,output_param.dimension,report_config['ratio'],output_param.byte_str)
        department_list = report_df['一级单位'].unique()
        file_list = []
        logger.info(f"{task_id}保存各单位表格")
        with ThreadPoolExecutor() as executor:
            futures = [executor.submit(save_department_report,"",save_path,date,report_df)]
            futures.extend([executor.submit(save_department_report, department,save_path,date,report_df) for department in department_list])
            department_report_list = [future.result() for future in futures]
        # department_report_list = await asyncio.gather(*(async_save_department_report(
        #                                                 semaphore,
        #                                                 department, 
        #                                                 save_path,
        #                                                 date,
        #                                                 report_df
        #                                                 ) for department in department_list))
        file_list.extend(department_report_list)
        logger.info(f"{task_id}进行压缩")
        if zip_excels(file_list,f"{save_path}.zip"):
            file_path =  f"{save_path}.zip"
        else:
            file_path =  None
        logger.info(f"{task_id}生成完成")
        if file_path:
            return FileResponse(path = file_path,
                                filename = os.path.basename(file_path),)
        else:
            return success(message="没有报告数据")
    except Exception as e:
        logger.error(f"生成报告异常{str(e)}",exc_info=True)
        return error(code=500, message="生成报告异常")

@app.post("/download_month_report", description="下载月度评分报告")
async def download_month_report(output_param:MonthlyReportOutput):
    try:
        system_data = defaultdict(lambda: defaultdict(lambda: defaultdict(lambda: {
        'UNIT_NAME': '',
        'SYSTEM_CODE': '',
        'SYSTEM_VISIT_TOTAL': 0,
        'SYSTEM_VISIT_TOTAL_BYTES': 0.0,
        'DATABASE_VISIT_TOTAL': 0.0,
        'DATABASE_VISIT_TOTAL_BYTES': 0.0,
        'CPU_ACTIVE_MINUTES': 0,
        'DATABASE_OPR':0,
        'DATABASE_PACKAGES_TOTAL': 0.0,
        'DATABASE_BYTES_TOTAL': 0.0,
        'DATABASE_ACK_PACKAGES_TOTAL' : 0.0,
        'HOST_NUM':0,
        }))) 
        MAX_CONCURRENT_READS = report_config['MAX_ASYNC_CONCURRENT_READS']
        semaphore = asyncio.Semaphore(MAX_CONCURRENT_READS)
        task_id = str(uuid4())
        date = f"{output_param.date_start}-{output_param.date_end}"
        logger.info(f"{task_id}开始生成月度评分报告{date}|{output_param.system}")
        gen_time = time.strftime("%Y%m%d%H%M%S", time.localtime())
        save_path = os.path.join(uniarch_config["general_config"]["RESULT_PATH"], 
                                f"{report_config['file_name']}/{gen_time}")
        if not os.path.exists(save_path):
            os.makedirs(save_path)
        json_files = await match_report_files(task_id,report_config['arch_data_path'],date)
        if not json_files:
            return success(message="没有报告数据")
        batch_size = report_config['JSON_FILE_BATCH_SIZE']
        for i in range(0, len(json_files), batch_size):
            logger.info(f"{task_id}正在读取 {i}-{i+batch_size}批数据")
            # 分批处理避免内存溢出
            batch = json_files[i:i+batch_size]
            local_system_data_list = await asyncio.gather(*(process_single_json_file(
                                                            semaphore,
                                                            p, 
                                                            ) for p in batch))
            logger.info(f"{task_id}正在聚合 {i}-{i+batch_size}批数据")
            for local_system_data in local_system_data_list:
                if local_system_data is None:
                    continue
                await merge_single_system_hour_json_file(system_data,local_system_data,report_config['host_cpu_avg_threashold'])
        network_field_decision_config = await network_field_decision(system_data)
        report_df = await make_hour_dataframe(system_data,output_param.system,report_config['ratio'],network_field_decision_config,"MB")
        if report_df.empty:
            logger.info(f"{task_id}没有报告数据")
            return error(message="没有报告数据")
        report_df = pd.pivot_table(report_df, index=["一级单位","系统编号","系统名称"],values=["系统访问总量","系统数据流量(MB)","数据库访问量","数据库访问流量(MB)","虚拟CPU(vCPU)活跃累计时长"],aggfunc="sum").reset_index()
        system_catalogcount_dict = get_system_catalogcount()
        def get_catalogcount(row):
            return system_catalogcount_dict.get(row["一级单位"],{}).get(row["系统编号"],0)
        report_df["公共数据资源目录编目"] = report_df.apply(get_catalogcount, axis=1)
        report_df['日期(年月)'] = output_param.date_start[:6]
        month_df = gen_evaluate_report(report_df,output_param.algrithm)
        # month_file_path = os.path.join(save_path,f"{date}_月度评分报告.xlsx")
        # month_df.to_excel(month_file_path)
        department_list = month_df['一级单位'].unique()
        file_list = []
        logger.info(f"{task_id}保存各单位表格")
        with ThreadPoolExecutor() as executor:
            futures = [executor.submit(save_department_score_report,"",save_path,date,month_df,report_df)]
            futures.extend([executor.submit(save_department_score_report, department,save_path,date,month_df,report_df) for department in department_list])
            department_report_list = [future.result() for future in futures]
        file_list.extend(department_report_list)
        logger.info(f"{task_id}进行压缩")
        if zip_excels(file_list,f"{save_path}.zip"):
            file_path =  f"{save_path}.zip"
        else:
            file_path =  None
        logger.info(f"{task_id}生成完成")
        if file_path:
            return FileResponse(path = file_path,
                                filename = os.path.basename(file_path),)
        else:
            return success(message="没有报告数据")
    except Exception as e:
        logger.error(f"生成阅读报告报告异常{str(e)}",exc_info=True)
        return error(code=500, message="生成报告异常")

@app.post("/download_month_report_sp", description="下载特殊月度评分报告")
async def download_month_report_sp(output_param:MonthlyReportOutput):
    try:
        system_data = defaultdict(lambda: defaultdict(lambda: defaultdict(lambda: {
        'UNIT_NAME': '',
        'SYSTEM_CODE': '',
        'SYSTEM_VISIT_TOTAL': 0,
        'SYSTEM_VISIT_TOTAL_BYTES': 0.0,
        'DATABASE_VISIT_TOTAL': 0.0,
        'DATABASE_VISIT_TOTAL_BYTES': 0.0,
        'CPU_ACTIVE_MINUTES': 0,
        'DATABASE_OPR':0,
        'DATABASE_PACKAGES_TOTAL': 0.0,
        'DATABASE_BYTES_TOTAL': 0.0,
        'DATABASE_ACK_PACKAGES_TOTAL' : 0.0,
        'HOST_NUM':0,
        }))) 
        MAX_CONCURRENT_READS = report_config['MAX_ASYNC_CONCURRENT_READS']
        semaphore = asyncio.Semaphore(MAX_CONCURRENT_READS)
        task_id = str(uuid4())
        date = f"{output_param.date_start}-{output_param.date_end}"
        logger.info(f"{task_id}开始生成月度评分报告{date}|{output_param.system}")
        gen_time = time.strftime("%Y%m%d%H%M%S", time.localtime())
        save_path = os.path.join(uniarch_config["general_config"]["RESULT_PATH"], 
                                f"{report_config['file_name']}/{gen_time}")
        if not os.path.exists(save_path):
            os.makedirs(save_path)
        json_files = await match_report_files(task_id,report_config['arch_data_path'],date)
        if not json_files:
            return success(message="没有报告数据")
        batch_size = report_config['JSON_FILE_BATCH_SIZE']
        for i in range(0, len(json_files), batch_size):
            logger.info(f"{task_id}正在读取 {i}-{i+batch_size}批数据")
            # 分批处理避免内存溢出
            batch = json_files[i:i+batch_size]
            local_system_data_list = await asyncio.gather(*(process_single_json_file(
                                                            semaphore,
                                                            p, 
                                                            ) for p in batch))
            logger.info(f"{task_id}正在聚合 {i}-{i+batch_size}批数据")
            for local_system_data in local_system_data_list:
                if local_system_data is None:
                    continue
                await merge_single_system_hour_json_file(system_data,local_system_data,report_config['host_cpu_avg_threashold'])
        network_field_decision_config = await network_field_decision(system_data)
        report_df = await make_hour_dataframe(system_data,output_param.system,report_config['ratio'],network_field_decision_config,"MB")
        if report_df.empty:
            logger.info(f"{task_id}没有报告数据")
            return error(message="没有报告数据")
        def custom_agg(x):
            return np.sum(x) * 31 / 15
        report_df = pd.pivot_table(report_df, index=["一级单位","系统编号","系统名称"],values=["系统访问总量","系统数据流量(MB)","数据库访问量","数据库访问流量(MB)","虚拟CPU(vCPU)活跃累计时长"],aggfunc=custom_agg).reset_index()
        system_catalogcount_dict = get_system_catalogcount("/root/uniarch/uniserver/src/uniarch/zhuji.csv")
        def get_catalogcount(row):
            return system_catalogcount_dict.get(row["一级单位"],{}).get(row["系统编号"],0)
        report_df["公共数据资源目录编目"] = report_df.apply(get_catalogcount, axis=1)
        report_df['日期(年月)'] = output_param.date_start[:6]
        month_df = gen_evaluate_report(report_df,output_param.algrithm)
        # month_file_path = os.path.join(save_path,f"{date}_月度评分报告.xlsx")
        # month_df.to_excel(month_file_path)
        department_list = month_df['一级单位'].unique()
        file_list = []
        logger.info(f"{task_id}保存各单位表格")
        with ThreadPoolExecutor() as executor:
            futures = [executor.submit(save_department_score_report,"",save_path,date,month_df,report_df)]
            futures.extend([executor.submit(save_department_score_report, department,save_path,date,month_df,report_df) for department in department_list])
            department_report_list = [future.result() for future in futures]
        file_list.extend(department_report_list)
        logger.info(f"{task_id}进行压缩")
        if zip_excels(file_list,f"{save_path}.zip"):
            file_path =  f"{save_path}.zip"
        else:
            file_path =  None
        logger.info(f"{task_id}生成完成")
        if file_path:
            return FileResponse(path = file_path,
                                filename = os.path.basename(file_path),)
        else:
            return success(message="没有报告数据")
    except Exception as e:
        logger.error(f"生成月度评分报告报告异常{str(e)}",exc_info=True)
        return error(code=500, message="生成月度评分报告异常")


@app.post("/download_month_report_sp_day", description="下载特殊每日聚合月度评分报告")
async def download_month_report_sp_day(output_param:MonthlyReportOutput):
    try:
        system_data = defaultdict(lambda: defaultdict(lambda: {
        'UNIT_NAME': '',
        'SYSTEM_CODE': '',
        'SYSTEM_VISIT_TOTAL': 0,
        'SYSTEM_VISIT_TOTAL_BYTES': 0.0,
        'DATABASE_VISIT_TOTAL': 0,
        'DATABASE_VISIT_TOTAL_BYTES': 0.0,
        'CPU_ACTIVE_MINUTES': 0,
        'DATABASE_OPR':0,
        'DATABASE_PACKAGES_TOTAL': 0.0,
        'DATABASE_BYTES_TOTAL': 0.0,
        'DATABASE_ACK_PACKAGES_TOTAL' : 0,
        'HOST_NUM':0,
        })) 
        MAX_CONCURRENT_READS = report_config['MAX_ASYNC_CONCURRENT_READS']
        semaphore = asyncio.Semaphore(MAX_CONCURRENT_READS)
        task_id = str(uuid4())
        date = f"{output_param.date_start}-{output_param.date_end}"
        logger.info(f"{task_id}开始生成月度评分报告{date}|{output_param.system}")
        gen_time = time.strftime("%Y%m%d%H%M%S", time.localtime())
        save_path = os.path.join(uniarch_config["general_config"]["RESULT_PATH"], 
                                f"{report_config['file_name']}/{gen_time}")
        if not os.path.exists(save_path):
            os.makedirs(save_path)
        json_files = await match_report_files(task_id,report_config['arch_data_path'],date)
        if not json_files:
            return success(message="没有报告数据")
        batch_size = report_config['JSON_FILE_BATCH_SIZE']
        for i in range(0, len(json_files), batch_size):
            logger.info(f"{task_id}正在读取 {i}-{i+batch_size}批数据")
            # 分批处理避免内存溢出
            batch = json_files[i:i+batch_size]
            local_system_data_list = await asyncio.gather(*(process_single_json_file(
                                                            semaphore,
                                                            p, 
                                                            ) for p in batch))
            logger.info(f"{task_id}正在聚合 {i}-{i+batch_size}批数据")
            for local_system_data in local_system_data_list:
                if local_system_data is None:
                    continue
                await merge_single_json_file(system_data,local_system_data,"day",report_config['host_cpu_avg_threashold'])
        report_df_total = await json_to_dataframe(system_data,output_param.system,"day",report_config['ratio'],"MB")
        def custom_agg(x):
            return np.sum(x) * 31 / 15
        # 每日维度数据合并
        # report_df_total = pd.concat(report_df_list)
        # 透视数据
        report_df = pd.pivot_table(report_df_total, index=["一级单位", "系统编号","系统名称"], values=["系统访问总量","系统数据流量(MB)","数据库访问量","数据库访问流量(MB)","虚拟CPU(vCPU)活跃累计时长"], aggfunc=custom_agg).reset_index()
        system_catalogcount_dict = get_system_catalogcount()
        def get_catalogcount(row):
            return system_catalogcount_dict.get(row["一级单位"],{}).get(row["系统编号"],0)
        # 通过透视按月聚合
        report_df["公共数据资源目录编目"] = report_df.apply(get_catalogcount, axis=1)
        report_df['日期(年月)'] = output_param.date_start[:6]
        month_df = gen_evaluate_report(report_df,output_param.algrithm)
        # month_file_path = os.path.join(save_path,f"{date}_月度评分报告.xlsx")
        # month_df.to_excel(month_file_path)
        department_list = month_df['一级单位'].unique()
        file_list = []
        logger.info(f"{task_id}保存各单位表格")
        with ThreadPoolExecutor() as executor:
            futures = [executor.submit(save_department_score_report,"",save_path,date,month_df,report_df)]
            futures.extend([executor.submit(save_department_score_report, department,save_path,date,month_df,report_df) for department in department_list])
            department_report_list = [future.result() for future in futures]
        file_list.extend(department_report_list)
        logger.info(f"{task_id}进行压缩")
        if zip_excels(file_list,f"{save_path}.zip"):
            file_path =  f"{save_path}.zip"
        else:
            file_path =  None
        logger.info(f"{task_id}生成完成")
        if file_path:
            return FileResponse(path = file_path,
                                filename = os.path.basename(file_path),)
        else:
            return success(message="没有报告数据")
    except Exception as e:
        logger.error(f"生成月度评分报告报告异常{str(e)}",exc_info=True)
        return error(code=500, message="生成月度评分报告异常")


@app.post("/trigger-daily-summary/{day_delta}",description="手动触发每日任务")
async def manual_trigger_daily(day_delta:int):
    """手动触发每日任务"""
    await daily_summary_task(day_delta)
    return {"status": "每日任务已手动执行"}

@app.post("/trigger-monthly-summary")
async def manual_trigger_monthly(trigger_param:TriggerMonthlySummary):
    """手动触发每月任务"""
    await monthly_summary_task(trigger_param.day_delta,trigger_param.algrithm)
    return {"status": "每月任务已手动执行"}

@app.post("/trigger-daily-inspection-task")
async def manual_trigger_daily_inspection_task():
    """手动触发每月任务"""
    await daily_inspection_task()
    return {"status": "巡检任务已手动执行"}

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=19878)
    # generate_excel_report(r"D:\20250611report\效能报告\20250612162905.zip", r"D:\20250611report\效能报告\20250612162905.zip", "20250611")
    # generate_excel_report()