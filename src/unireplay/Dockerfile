FROM registry.jxit.net.cn:5000/ubuntu:22.04 AS builder

RUN apt -y update && \
    apt -y install gcc libpcap-dev

WORKDIR /unireplay

COPY . /unireplay

RUN gcc -g -O2 -o sendpacket sendpacket.c -lpcap

FROM registry.jxit.net.cn:5000/ubuntu:22.04

RUN apt-get -y update && \
    apt-get -y install curl wget iproute2 psmisc libpcap0.8 && \
    apt-get clean && \ 
    rm -rf /var/lib/apt/lists/*

WORKDIR /unireplay

COPY --from=builder /unireplay/sendpacket /usr/local/bin/sendpacket
