###
# ------------------------------------------------------------------------
# OWASP ModSecurity Core Rule Set ver.3.2.0
# Copyright (c) 2006-2019 Trustwave and contributors. All rights reserved.
#
# The OWASP ModSecurity Core Rule Set is distributed under
# Apache Software License (ASL) version 2
# Please see the enclosed LICENSE file for full details.
# ------------------------------------------------------------------------
#
# RFI Attacks
#

#
# -= Paranoia Level 0 (empty) =- (apply unconditionally)
#



SecRule TX:EXECUTING_PARANOIA_LEVEL "@lt 1" "id:931011,phase:1,pass,nolog,skipAfter:END-REQUEST-931-APPLICATION-ATTACK-RFI"
SecRule TX:EXECUTING_PARANOIA_LEVEL "@lt 1" "id:931012,phase:2,pass,nolog,skipAfter:END-REQUEST-931-APPLICATION-ATTACK-RFI"
#
# -= Paranoia Level 1 (default) =- (apply only when tx.executing_paranoia_level is sufficiently high: 1 or higher)
#

# -=[ Rule Logic ]=-
# These rules look for common types of Remote File Inclusion (RFI) attack methods.
#	- URL Contains an IP Address
#	- The PHP "include()" Function
#	- RFI Data Ends with Question Mark(s) (?)
#	- RFI Host Doesn't Match Local Host
#
# -=[ References ]=-
# http://projects.webappsec.org/Remote-File-Inclusion
# http://tacticalwebappsec.blogspot.com/2009/06/generic-remote-file-inclusion-attack.html
#
SecRule ARGS "@rx ^(?i:file|ftps?|https?):\/\/(?:\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3})" \
    "id:931100,\
    phase:2,\
    block,\
    capture,\
    t:none,\
    msg:'可能的RFI (Remote File Inclusion)攻击:URL参数使用IP地址',\
    logdata:'Matched Data: %{TX.0} found within %{MATCHED_VAR_NAME}: %{MATCHED_VAR}',\
    tag:'application-multi',\
    tag:'language-multi',\
    tag:'platform-multi',\
    tag:'attack-rfi',\
    tag:'OWASP_CRS',\
    tag:'OWASP_CRS/WEB_ATTACK/RFI',\
    ctl:auditLogParts=+E,\
    ver:'OWASP_CRS/3.2.0',\
    severity:'CRITICAL',\
    setvar:'tx.rfi_score=+%{tx.critical_anomaly_score}',\
    setvar:'tx.anomaly_score_pl1=+%{tx.critical_anomaly_score}'"

SecRule QUERY_STRING|REQUEST_BODY "@rx (?i)(?:\binclude\s*\([^)]*|mosConfig_absolute_path|_CONF\[path\]|_SERVER\[DOCUMENT_ROOT\]|GALLERY_BASEDIR|path\[docroot\]|appserv_root|config\[root_dir\])=(?:file|ftps?|https?):\/\/" \
    "id:931110,\
    phase:2,\
    block,\
    capture,\
    t:none,t:urlDecodeUni,\
    msg:'可能的远程文件包含 (RFI) 攻击：与 URL 负载一起使用的常见 RFI 易受攻击的参数名称',\
    logdata:'Matched Data: %{TX.0} found within %{MATCHED_VAR_NAME}: %{MATCHED_VAR}',\
    tag:'application-multi',\
    tag:'language-multi',\
    tag:'platform-multi',\
    tag:'attack-rfi',\
    tag:'OWASP_CRS',\
    tag:'OWASP_CRS/WEB_ATTACK/RFI',\
    ctl:auditLogParts=+E,\
    ver:'OWASP_CRS/3.2.0',\
    severity:'CRITICAL',\
    setvar:'tx.rfi_score=+%{tx.critical_anomaly_score}',\
    setvar:'tx.anomaly_score_pl1=+%{tx.critical_anomaly_score}'"

SecRule ARGS "@rx ^(?i:file|ftps?|https?).*?\?+$" \
    "id:931120,\
    phase:2,\
    block,\
    capture,\
    t:none,\
    msg:'可能的远程文件包含 (RFI) 攻击：使用带有尾随问号字符 (?) 的 URL 有效负载',\
    logdata:'Matched Data: %{TX.0} found within %{MATCHED_VAR_NAME}: %{MATCHED_VAR}',\
    tag:'application-multi',\
    tag:'language-multi',\
    tag:'platform-multi',\
    tag:'attack-rfi',\
    tag:'OWASP_CRS',\
    tag:'OWASP_CRS/WEB_ATTACK/RFI',\
    ctl:auditLogParts=+E,\
    ver:'OWASP_CRS/3.2.0',\
    severity:'CRITICAL',\
    setvar:'tx.rfi_score=+%{tx.critical_anomaly_score}',\
    setvar:'tx.anomaly_score_pl1=+%{tx.critical_anomaly_score}'"



SecRule TX:EXECUTING_PARANOIA_LEVEL "@lt 2" "id:931013,phase:1,pass,nolog,skipAfter:END-REQUEST-931-APPLICATION-ATTACK-RFI"
SecRule TX:EXECUTING_PARANOIA_LEVEL "@lt 2" "id:931014,phase:2,pass,nolog,skipAfter:END-REQUEST-931-APPLICATION-ATTACK-RFI"
#
# -= Paranoia Level 2 =- (apply only when tx.executing_paranoia_level is sufficiently high: 2 or higher)
#

SecRule ARGS "@rx ^(?i:file|ftps?|https?)://(.*)$" \
    "id:931130,\
    phase:2,\
    block,\
    capture,\
    t:none,\
    msg:'可能的远程文件包含 (RFI) 攻击：域外引用/链接',\
    logdata:'Matched Data: %{TX.0} found within %{MATCHED_VAR_NAME}: %{MATCHED_VAR}',\
    tag:'application-multi',\
    tag:'language-multi',\
    tag:'platform-multi',\
    tag:'attack-rfi',\
    tag:'OWASP_CRS',\
    tag:'OWASP_CRS/WEB_ATTACK/RFI',\
    tag:'paranoia-level/2',\
    ctl:auditLogParts=+E,\
    ver:'OWASP_CRS/3.2.0',\
    severity:'CRITICAL',\
    setvar:'tx.rfi_parameter_%{MATCHED_VAR_NAME}=%{tx.1}',\
    chain"
    SecRule TX:/rfi_parameter_.*/ "!@beginsWith %{request_headers.host}" \
        "setvar:'tx.rfi_score=+%{tx.critical_anomaly_score}',\
        setvar:'tx.anomaly_score_pl2=+%{tx.critical_anomaly_score}'"



SecRule TX:EXECUTING_PARANOIA_LEVEL "@lt 3" "id:931015,phase:1,pass,nolog,skipAfter:END-REQUEST-931-APPLICATION-ATTACK-RFI"
SecRule TX:EXECUTING_PARANOIA_LEVEL "@lt 3" "id:931016,phase:2,pass,nolog,skipAfter:END-REQUEST-931-APPLICATION-ATTACK-RFI"
#
# -= Paranoia Level 3 =- (apply only when tx.executing_paranoia_level is sufficiently high: 3 or higher)
#



SecRule TX:EXECUTING_PARANOIA_LEVEL "@lt 4" "id:931017,phase:1,pass,nolog,skipAfter:END-REQUEST-931-APPLICATION-ATTACK-RFI"
SecRule TX:EXECUTING_PARANOIA_LEVEL "@lt 4" "id:931018,phase:2,pass,nolog,skipAfter:END-REQUEST-931-APPLICATION-ATTACK-RFI"
#
# -= Paranoia Level 4 =- (apply only when tx.executing_paranoia_level is sufficiently high: 4 or higher)
#



#
# -= Paranoia Levels Finished =-
#
SecMarker "END-REQUEST-931-APPLICATION-ATTACK-RFI"
