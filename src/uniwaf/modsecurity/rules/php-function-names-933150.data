__halt_compiler
apache_child_terminate
base64_decode
bzdecompress
call_user_func
call_user_func_array
call_user_method
call_user_method_array
convert_uudecode
file_get_contents
file_put_contents
fsockopen
get_class_methods
get_class_vars
get_defined_constants
get_defined_functions
get_defined_vars
gzdecode
gzinflate
gzuncompress
include_once
invokeargs
pcntl_exec
pcntl_fork
pfsockopen
posix_getcwd
posix_getpwuid
posix_getuid
posix_uname
ReflectionFunction
require_once
shell_exec
str_rot13
sys_get_temp_dir
wp_remote_fopen
wp_remote_get
wp_remote_head
wp_remote_post
wp_remote_request
wp_safe_remote_get
wp_safe_remote_head
wp_safe_remote_post
wp_safe_remote_request
zlib_decode
