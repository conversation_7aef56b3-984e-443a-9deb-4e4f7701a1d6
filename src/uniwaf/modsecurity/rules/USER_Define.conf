# 检测各种 URL 封装尝试
SecRule REQUEST_FILENAME|ARGS_NAMES|ARGS|REQUEST_HEADERS|!REQUEST_HEADERS:Referer "@rx (?i)(?:(?:\bhttp|\bftp|\bphp|\bdata|\bexpect|\binput|\bphar|\bzip|\bglob|\bfile|\bldap|\bssh2|\bsftp|\bdict|\bgopher):\/\/|(?:\.\.\/|\.\.\\))" \
    "id:1300,\
    phase:2,\
    block,\
    t:none,t:urlDecodeUni,t:lowercase,\
    msg:'URL Wrapper Attempt Detected',\
    logdata:'Matched Data: %{MATCHED_VAR} found within %{MATCHED_VAR_NAME}',\
    severity:'CRITICAL',\
    setvar:'tx.url_wrapper_score=+%{tx.critical_anomaly_score}',\
    setvar:'tx.anomaly_score=+%{tx.critical_anomaly_score}'"

# 检测双重编码的 URL 封装尝试
SecRule REQUEST_FILENAME|ARGS_NAMES|ARGS|REQUEST_HEADERS|!REQUEST_HEADERS:Referer "@rx (?i)(?:%(?:25)?(?:2f|3a|5c))" \
    "id:1301,\
    phase:2,\
    block,\
    t:none,t:urlDecodeUni,\
    msg:'Double Encoded URL Wrapper Attempt Detected',\
    logdata:'Matched Data: %{MATCHED_VAR} found within %{MATCHED_VAR_NAME}',\
    severity:'CRITICAL',\
    setvar:'tx.url_wrapper_score=+%{tx.critical_anomaly_score}',\
    setvar:'tx.anomaly_score=+%{tx.critical_anomaly_score}'"

# 检测 base64 编码的 URL 封装尝试
SecRule REQUEST_FILENAME|ARGS_NAMES|ARGS|REQUEST_HEADERS|!REQUEST_HEADERS:Referer "@rx (?i)(?:aHR0c|ZnRw|cGhw|ZGF0Y|ZXhwZWN0|aW5wdXQ|cGhhc|emlw|Z2xvY|ZmlsZ|bGRhc|c3NoM|c2Z0c|ZGljd|Z29waGVy)(?:[0-9a-zA-Z+/]{4})*(?:[0-9a-zA-Z+/]{2}==|[0-9a-zA-Z+/]{3}=)?" \
    "id:1302,\
    phase:2,\
    block,\
    t:none,\
    msg:'Base64 Encoded URL Wrapper Attempt Detected',\
    logdata:'Matched Data: %{MATCHED_VAR} found within %{MATCHED_VAR_NAME}',\
    severity:'CRITICAL',\
    setvar:'tx.url_wrapper_score=+%{tx.critical_anomaly_score}',\
    setvar:'tx.anomaly_score=+%{tx.critical_anomaly_score}'"

# 检测 URL 参数中的 IP 地址
SecRule ARGS "@rx (?:https?|ftp)://(?:\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3})" \
    "id:1400,\
    phase:2,\
    block,\
    t:none,\
    msg:'IP Address in URL - Possible RFI/LFI',\
    logdata:'Matched Data: %{MATCHED_VAR} found within %{MATCHED_VAR_NAME}',\
    severity:'CRITICAL',\
    setvar:'tx.rfi_score=+%{tx.critical_anomaly_score}',\
    setvar:'tx.anomaly_score=+%{tx.critical_anomaly_score}'"

# 检测 URL 路径中的 IP 地址
SecRule REQUEST_URI "@rx (?:https?|ftp)://(?:\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3})" \
    "id:1401,\
    phase:1,\
    block,\
    t:none,\
    msg:'IP Address in URL Path - Possible RFI/LFI',\
    logdata:'Matched Data: %{MATCHED_VAR} found within %{MATCHED_VAR_NAME}',\
    severity:'CRITICAL',\
    setvar:'tx.rfi_score=+%{tx.critical_anomaly_score}',\
    setvar:'tx.anomaly_score=+%{tx.critical_anomaly_score}'"

# 检测编码的 IP 地址
SecRule ARGS|REQUEST_URI "@rx (?:https?|ftp)://(?:%(?:25)?(?:30|31|32|33|34|35|36|37|38|39))" \
    "chain,\
    id:1402,\
    phase:2,\
    block,\
    t:none,t:urlDecodeUni,\
    msg:'Encoded IP Address in URL - Possible RFI/LFI',\
    logdata:'Matched Data: %{MATCHED_VAR} found within %{MATCHED_VAR_NAME}',\
    severity:'CRITICAL'"
SecRule MATCHED_VAR "@rx (?:\d{1,3}\.){3}\d{1,3}" \
    "t:none,\
    setvar:'tx.rfi_score=+%{tx.critical_anomaly_score}',\
    setvar:'tx.anomaly_score=+%{tx.critical_anomaly_score}'"

# 检测十六进制编码的 IP 地址
SecRule ARGS|REQUEST_URI "@rx (?:https?|ftp)://0x[a-fA-F0-9]{8}" \
    "id:1403,\
    phase:2,\
    block,\
    t:none,t:urlDecodeUni,t:lowercase,\
    msg:'Hexadecimal IP Address in URL - Possible RFI/LFI',\
    logdata:'Matched Data: %{MATCHED_VAR} found within %{MATCHED_VAR_NAME}',\
    severity:'CRITICAL',\
    setvar:'tx.rfi_score=+%{tx.critical_anomaly_score}',\
    setvar:'tx.anomaly_score=+%{tx.critical_anomaly_score}'"

# 检测八进制编码的 IP 地址
SecRule ARGS|REQUEST_URI "@rx (?:https?|ftp)://0[0-7]{10,11}" \
    "id:1404,\
    phase:2,\
    block,\
    t:none,t:urlDecodeUni,\
    msg:'Octal IP Address in URL - Possible RFI/LFI',\
    logdata:'Matched Data: %{MATCHED_VAR} found within %{MATCHED_VAR_NAME}',\
    severity:'CRITICAL',\
    setvar:'tx.rfi_score=+%{tx.critical_anomaly_score}',\
    setvar:'tx.anomaly_score=+%{tx.critical_anomaly_score}'"

# 阻止利用本地文件包含漏洞进行攻击 LFI
SecRule REQUEST_FILENAME|ARGS_NAMES|ARGS|XML:/* "(?:\.\./|\.\.\\)" \
    "id:1100,phase:2,t:none,t:urlDecodeUni,block,msg:'LFI Attack Detected',logdata:'%{MATCHED_VAR}'"

SecRule REQUEST_FILENAME|ARGS_NAMES|ARGS|XML:/* "(?:/etc/passwd|/etc/shadow|/proc/self/environ|/proc/version)" \
    "id:1101,phase:2,t:none,t:lowercase,block,msg:'Common LFI Files Detected',logdata:'%{MATCHED_VAR}'"

SecRule REQUEST_FILENAME "\.(?:php|asp|aspx|jsp)$" \
    "id:1102,phase:2,t:none,t:lowercase,pass,nolog,chain"
SecRule REQUEST_FILENAME "!(?:/index\.php|/admin\.php)" \
    "t:none,block,msg:'Restricted File Extension Detected'"

SecRule REQUEST_URI|REQUEST_HEADERS|ARGS|ARGS_NAMES "@contains %00" \
    "id:1103,phase:2,block,msg:'Null Byte Injection Detected',logdata:'%{MATCHED_VAR}'"

SecRule REQUEST_URI|REQUEST_HEADERS|ARGS|ARGS_NAMES "@rx (?:\.{2}[/\]|\w+\.{2}/)" \
    "id:1104,phase:2,block,msg:'Directory Traversal Attempt',logdata:'%{MATCHED_VAR}'"

SecRule ARGS_NAMES "@rx ^(?:file|path|filepath)$" \
    "chain,phase:2,t:none,block,msg:'File Parameter Length Exceeded',id:1105"
SecRule ARGS "@gt 256" "t:length"

# 阻止利用远程文件包含漏洞进行攻击 RFI
SecRule ARGS "@rx ^(?:https?|ftp)://" \
    "id:1200,phase:2,t:none,block,msg:'Standard RFI Attack Detected',logdata:'%{MATCHED_VAR}'"

SecRule ARGS "@rx ^(?:http|https|ftp|php|data|expect|input|phar|zip|glob|file)://" \
    "id:1201,phase:2,t:none,t:lowercase,block,msg:'Protocol Wrapper RFI Attempt',logdata:'%{MATCHED_VAR}'"

SecRule ARGS "@rx (?:\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3})" \
    "chain,phase:2,t:none,block,msg:'IP Address in Args - Possible RFI',id:1202"
SecRule ARGS "@contains ://" "t:none"

SecRule ARGS|ARGS_NAMES "@rx (?:%(?:2f|5c)|(?:2e){2})" \
    "id:1203,phase:2,t:none,t:urlDecodeUni,block,msg:'Encoded RFI Attack Detected',logdata:'%{MATCHED_VAR}'"

SecRule ARGS_NAMES "@rx ^(?:include|require)(?:_once)?$" \
    "chain,phase:2,t:none,t:lowercase,block,msg:'File Inclusion Function with External URL',id:1204"
SecRule ARGS "@rx ^(?:https?|ftp)://" "t:none"

SecRule ARGS "@contains %00" \
    "id:1205,phase:2,t:none,t:urlDecodeUni,block,msg:'Null Byte Injection Detected',logdata:'%{MATCHED_VAR}'"

SecRule ARGS "@gt 256" \
    "chain,phase:2,t:none,block,msg:'Overly Long URL - Possible RFI',id:1206"
SecRule ARGS "@rx ^(?:https?|ftp)://" "t:none"

SecRule ARGS "@rx (?:\.php|\.asp|\.aspx|\.jsp)$" \
    "chain,phase:2,t:none,t:lowercase,block,msg:'Suspicious File Extension in Remote URL',id:1207"
SecRule ARGS "@rx ^(?:https?|ftp)://" "t:none"

# 阻止远程命令执行（RCE）攻击的方法
SecRule ARGS|ARGS_NAMES|REQUEST_COOKIES|REQUEST_COOKIES_NAMES|REQUEST_BODY|REQUEST_HEADERS|XML:/*|!REQUEST_HEADERS:Referer "@rx (?:;|\{|\||\|\||&|&&|\n|\r|\$\(|\$\(\(|`|\${|<\(|>\(|\(\s*\))\s*(?:{|\s*\(\s*|\w+=(?:[^\s]*|\$.*|\$.*|<.*|>.*|\'.*\'|\".*\")\s+|.*\s*\|\s*|\.|.*\s+--[^\s]+|\/[\w\-]+\/[\w\-]+\s*|[^\s]+\s*\$\s*\()" \
    "id:6000,\
    phase:2,\
    block,\
    capture,\
    t:none,t:urlDecodeUni,\
    msg:'Remote Command Execution: Suspicious Command Pattern',\
    logdata:'Matched Data: %{TX.0} found within %{MATCHED_VAR_NAME}: %{MATCHED_VAR}',\
    severity:'CRITICAL'"

SecRule REQUEST_URI|ARGS|ARGS_NAMES|REQUEST_BODY "@rx (?:/etc/passwd|/etc/shadow|/proc/self/environ|/proc/version)" \
    "id:6002,\
    phase:2,\
    block,\
    t:none,t:lowercase,t:urlDecodeUni,\
    msg:'Attempt to access system files',\
    logdata:'Matched Data: %{MATCHED_VAR} found within %{MATCHED_VAR_NAME}',\
    severity:'CRITICAL'"

SecRule ARGS|ARGS_NAMES|REQUEST_COOKIES|REQUEST_COOKIES_NAMES|REQUEST_BODY|REQUEST_HEADERS|XML:/*|!REQUEST_HEADERS:Referer "@rx \$\{.*\}" \
    "id:6004,\
    phase:2,\
    block,\
    capture,\
    t:none,t:urlDecodeUni,\
    msg:'Remote Command Execution: Environment Variable Access Attempt',\
    logdata:'Matched Data: %{TX.0} found within %{MATCHED_VAR_NAME}: %{MATCHED_VAR}',\
    severity:'CRITICAL'"

SecRule ARGS|ARGS_NAMES|REQUEST_COOKIES|REQUEST_COOKIES_NAMES|REQUEST_BODY|REQUEST_HEADERS|XML:/*|!REQUEST_HEADERS:Referer "@rx `.*`" \
    "id:6005,\
    phase:2,\
    block,\
    capture,\
    t:none,t:urlDecodeUni,\
    msg:'Remote Command Execution: Backtick Execution Attempt',\
    logdata:'Matched Data: %{TX.0} found within %{MATCHED_VAR_NAME}: %{MATCHED_VAR}',\
    severity:'CRITICAL'"

# 阻止违反 HTTP 协议的恶意访问
SecRule &REQUEST_HEADERS:Host "@eq 0" \
    "id:7002,\
    phase:1,\
    block,\
    msg:'Request missing a Host header',\
    severity:'WARNING'"

SecRule &REQUEST_HEADERS:Accept "@gt 1" \
    "id:7003,\
    phase:1,\
    block,\
    msg:'Multiple Accept headers',\
    severity:'WARNING'"

SecRule REQUEST_HEADERS:Content-Length "!@rx ^(?:0|[1-9][0-9]{0,5})$" \
    "id:7004,\
    phase:1,\
    block,\
    msg:'Abnormally large Content-Length',\
    severity:'WARNING'"

SecRule REQUEST_LINE "@gt 2048" \
    "id:7005,\
    phase:1,\
    block,\
    msg:'Request line too long',\
    severity:'WARNING'"

SecRule REQUEST_HEADERS "@rx \x00" \
    "id:7006,\
    phase:1,\
    block,\
    msg:'NULL byte in request headers',\
    severity:'CRITICAL'"

SecRule REQUEST_HEADERS "@rx [\n\r]" \
    "id:7007,\
    phase:1,\
    block,\
    msg:'Carriage return or line feed in request headers',\
    severity:'CRITICAL'"

SecRule REQUEST_URI "@rx %(?!2[0-9A-Fa-f]|3[0-9A-Fa-f]|[0-9A-Fa-f]{2})" \
    "id:7008,\
    phase:1,\
    block,\
    msg:'Invalid URL encoding',\
    severity:'WARNING'"

# 阻止利用远程代理感染漏洞进行攻击
SecRule REQUEST_HEADERS:X-Forwarded-For|REQUEST_HEADERS:X-Forwarded-Host|REQUEST_HEADERS:X-Client-IP|REQUEST_HEADERS:X-Real-IP|REQUEST_HEADERS:Proxy "@rx ." \
    "id:8000,\
    phase:1,\
    block,\
    msg:'Proxy header detected',\
    logdata:'%{MATCHED_VAR_NAME}: %{MATCHED_VAR}',\
    severity:'WARNING'"

SecRule REQUEST_HEADERS:Via "@rx ." \
    "id:8001,\
    phase:1,\
    block,\
    msg:'Via header detected',\
    logdata:'%{MATCHED_VAR_NAME}: %{MATCHED_VAR}',\
    severity:'WARNING'"

SecRule REQUEST_METHOD "@streq CONNECT" \
    "id:8002,\
    phase:1,\
    block,\
    msg:'CONNECT method not allowed',\
    severity:'CRITICAL'"

SecRule REQUEST_HEADERS:User-Agent "@rx (?:proxy|wget|curl|libwww-perl|python-requests)" \
    "id:8003,\
    phase:1,\
    block,\
    msg:'Suspicious User-Agent detected',\
    logdata:'%{MATCHED_VAR_NAME}: %{MATCHED_VAR}',\
    severity:'WARNING'"

SecRule REQUEST_HEADERS:Max-Forwards "@rx ." \
    "id:8004,\
    phase:1,\
    block,\
    msg:'Max-Forwards header detected',\
    logdata:'%{MATCHED_VAR_NAME}: %{MATCHED_VAR}',\
    severity:'WARNING'"

SecRule REQUEST_HEADERS:Forwarded "@rx ." \
    "id:8005,\
    phase:1,\
    block,\
    msg:'Forwarded header detected',\
    logdata:'%{MATCHED_VAR_NAME}: %{MATCHED_VAR}',\
    severity:'WARNING'"

SecRule REQUEST_HEADERS:Client-IP "@rx ." \
    "id:8006,\
    phase:1,\
    block,\
    msg:'Client-IP header detected',\
    logdata:'%{MATCHED_VAR_NAME}: %{MATCHED_VAR}',\
    severity:'WARNING'"

SecRule REQUEST_HEADERS:X-Forwarded-Server "@rx ." \
    "id:8007,\
    phase:1,\
    block,\
    msg:'X-Forwarded-Server header detected',\
    logdata:'%{MATCHED_VAR_NAME}: %{MATCHED_VAR}',\
    severity:'WARNING'"

SecRule &ENV:HTTP_PROXY "@gt 0" \
    "id:8008,\
    phase:1,\
    block,\
    msg:'HTTP_PROXY environment variable set',\
    severity:'CRITICAL'"

# 阻止利用Session会话ID不变的漏洞进行攻击
SecRule REQUEST_COOKIES:JSESSIONID "@rx ^[a-zA-Z0-9]{32,128}$" \
    "id:9001,\
    phase:2,\
    pass,\
    nolog,\
    setvar:'tx.session_id=%{MATCHED_VAR}'"

SecRule REQUEST_COOKIES:PHPSESSID "@rx ^[a-zA-Z0-9]{32,128}$" \
    "id:9002,\
    phase:2,\
    pass,\
    nolog,\
    setvar:'tx.session_id=%{MATCHED_VAR}'"

SecRule REQUEST_COOKIES:ASPSESSIONID "@rx ^[a-zA-Z0-9]{32,128}$" \
    "id:9003,\
    phase:2,\
    pass,\
    nolog,\
    setvar:'tx.session_id=%{MATCHED_VAR}'"

SecRule REQUEST_COOKIES:SID "@rx ^[a-zA-Z0-9]{32,128}$" \
    "id:9004,\
    phase:2,\
    pass,\
    nolog,\
    setvar:'tx.session_id=%{MATCHED_VAR}'"

SecRule TX:SESSION_ID "!@beginsWith %{SESSION.OLD_SESSION_ID}" \
    "id:9005,\
    phase:2,\
    block,\
    msg:'Possible session fixation attempt',\
    logdata:'Old: %{SESSION.OLD_SESSION_ID}, New: %{TX.SESSION_ID}',\
    severity:'CRITICAL'"

# 阻止黑客扫描网站
SecRule &GLOBAL:ip_block "@eq 0" \
    "id:1000001,\
    phase:1,\
    nolog,\
    pass,\
    initcol:global=global,\
    initcol:ip=%{REMOTE_ADDR}_%{TX.ua_hash}"

SecRule IP:REQUEST_COUNT "@gt 100" \
    "id:1000002,\
    phase:1,\
    deny,\
    status:429,\
    setvar:global.ip_block=%{REMOTE_ADDR},\
    expirevar:global.ip_block=60,\
    log,\
    msg:'Potential scanning activity detected'"

SecRule REQUEST_HEADERS:User-Agent "@pmFromFile scanners-user-agents.data" \
    "id:1000003,\
    phase:1,\
    deny,\
    status:403,\
    log,\
    msg:'Scanner user agent detected'"

SecRule REQUEST_URI "@rx \.(git|svn|htaccess|htpasswd|sql|bak)$" \
    "id:1000004,\
    phase:1,\
    deny,\
    status:403,\
    log,\
    msg:'Access to sensitive file attempted'"

SecRule REQUEST_URI "@contains ../" \
    "id:1000005,\
    phase:1,\
    deny,\
    status:403,\
    log,\
    msg:'Directory traversal attempt detected'"

SecRule &REQUEST_HEADERS:Acunetix-Product "!@eq 0" \
    "id:1000007,\
    phase:1,\
    deny,\
    status:403,\
    log,\
    msg:'Acunetix scanner detected'"

SecRule GEO:COUNTRY_CODE "!@within US CA MX" \
    "id:1000008,\
    phase:1,\
    deny,\
    status:403,\
    log,\
    msg:'Access from unauthorized country'"

# 阻止源代码和错误信息泄露
SecRule &RESPONSE_HEADERS:Server "@eq 1" \
    "id:1100001,\
    phase:3,\
    pass,\
    nolog,\
    setenv:RESPONSE_HEADERS:Server"

SecRule REQUEST_FILENAME "@rx \.(php|inc|sql|bak|config|xml|ini)$" \
    "id:1100002,\
    phase:1,\
    deny,\
    status:403,\
    log,\
    msg:'Attempted access to potentially sensitive file'"

SecRule RESPONSE_BODY "@rx (?i:mysql error|sql syntax|odbc driver|olledb provider|jdbc driver)" \
    "id:1100003,\
    phase:4,\
    deny,\
    status:200,\
    log,\
    msg:'Database error message detected in response'"

SecRule RESPONSE_BODY "@rx <title>Index of /" \
    "id:1100004,\
    phase:4,\
    deny,\
    status:403,\
    log,\
    msg:'Directory listing detected'"

SecRule RESPONSE_BODY "@rx (?:<\?php|<%=|<%@|<asp:|<%)" \
    "id:1100005,\
    phase:4,\
    deny,\
    status:500,\
    log,\
    msg:'Potential source code disclosure'"

SecRule RESPONSE_BODY "@rx (?:stack trace:|at \w+\.|\w+\.java:\d+)" \
    "id:1100007,\
    phase:4,\
    deny,\
    status:500,\
    log,\
    msg:'Potential stack trace disclosure'"

SecRule RESPONSE_BODY "@rx (?i:powered by|built with|running on) [a-z0-9\.\-]+ [0-9\.]+" \
    "id:1100008,\
    phase:4,\
    deny,\
    status:200,\
    log,\
    msg:'Software version information detected'"

SecRule REQUEST_URI "@beginsWith /admin/ /config/ /includes/" \
    "id:1100009,\
    phase:1,\
    deny,\
    status:403,\
    log,\
    msg:'Attempted access to restricted directory'"

# 蜜罐项目黑名单
SecRule REMOTE_ADDR "@ipMatch *************,10.0.0.0/24" \
    "id:1200001,\
    phase:1,\
    deny,\
    log,\
    msg:'Access from blacklisted IP'"

SecRule REQUEST_HEADERS:User-Agent "@contains badbot" \
    "id:1200002,\
    phase:1,\
    deny,\
    log,\
    msg:'Blacklisted User-Agent detected'"

SecRule REQUEST_URI "@beginsWith /admin" \
    "id:1200003,\
    phase:1,\
    deny,\
    log,\
    msg:'Access to restricted URI'"

SecRule ARGS_NAMES "@contains passwd" \
    "id:1200004,\
    phase:2,\
    deny,\
    log,\
    msg:'Suspicious parameter name detected'"

SecRule REQUEST_METHOD "!@within GET POST HEAD PUT" \
    "id:1200005,\
    phase:1,\
    deny,\
    log,\
    msg:'Unauthorized HTTP method'"

SecRule REQUEST_FILENAME "@endsWith .php" \
    "id:1200006,\
    phase:1,\
    deny,\
    log,\
    msg:'Access to PHP files not allowed'"

SecRule REQUEST_URI "@rx (?:/etc/passwd|/bin/sh)" \
    "id:1200007,\
    phase:1,\
    deny,\
    log,\
    msg:'Potential system file access attempt'"

SecRule REQUEST_URI "@contains admin" \
    "chain,\
    id:1200008,\
    phase:1,\
    deny,\
    log,\
    msg:'Suspicious admin access attempt'"
    SecRule &ARGS "@ge 5" "chain"
    SecRule ARGS_NAMES "@contains password"

SecRule REMOTE_ADDR "@ipMatch *************" \
    "id:1200009,\
    phase:1,\
    deny,\
    status:403,\
    log,\
    msg:'Access denied for blacklisted IP',\
    setvar:'tx.msg=Access from your IP is not allowed.'"

