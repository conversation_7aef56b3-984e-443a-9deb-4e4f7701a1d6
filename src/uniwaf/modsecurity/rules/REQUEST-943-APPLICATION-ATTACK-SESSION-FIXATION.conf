###
# ------------------------------------------------------------------------
# OWASP ModSecurity Core Rule Set ver.3.2.0
# Copyright (c) 2006-2019 Trustwave and contributors. All rights reserved.
#
# The OWASP ModSecurity Core Rule Set is distributed under
# Apache Software License (ASL) version 2
# Please see the enclosed LICENSE file for full details.
# ------------------------------------------------------------------------

#
# -= Paranoia Level 0 (empty) =- (apply unconditionally)
#



SecRule TX:EXECUTING_PARANOIA_LEVEL "@lt 1" "id:943011,phase:1,pass,nolog,skipAfter:END-REQUEST-943-APPLICATION-ATTACK-SESSION-FIXATION"
SecRule TX:EXECUTING_PARANOIA_LEVEL "@lt 1" "id:943012,phase:2,pass,nolog,skipAfter:END-REQUEST-943-APPLICATION-ATTACK-SESSION-FIXATION"
#
# -= Paranoia Level 1 (default) =- (apply only when tx.executing_paranoia_level is sufficiently high: 1 or higher)
#

#
# Session fixation
#
# -=[ References ]=-
# http://projects.webappsec.org/Session-Fixation
# http://projects.webappsec.org/w/page/13246960/Session%20Fixation
# http://capec.mitre.org/data/definitions/61.html
#
SecRule REQUEST_COOKIES|!REQUEST_COOKIES:/__utm/|REQUEST_COOKIES_NAMES|ARGS_NAMES|ARGS|XML:/* "@rx (?i:\.cookie\b.*?;\W*?(?:expires|domain)\W*?=|\bhttp-equiv\W+set-cookie\b)" \
    "id:943100,\
    phase:2,\
    block,\
    capture,\
    t:none,t:urlDecodeUni,\
    msg:'可能的会话固定攻击:在HTML中设置Cookie值',\
    logdata:'Matched Data: %{TX.0} found within %{MATCHED_VAR_NAME}: %{MATCHED_VAR}',\
    tag:'application-multi',\
    tag:'language-multi',\
    tag:'platform-multi',\
    tag:'attack-fixation',\
    tag:'OWASP_CRS',\
    tag:'OWASP_CRS/WEB_ATTACK/SESSION_FIXATION',\
    tag:'WASCTC/WASC-37',\
    tag:'CAPEC-61',\
    ctl:auditLogParts=+E,\
    ver:'OWASP_CRS/3.2.0',\
    severity:'CRITICAL',\
    setvar:'tx.session_fixation_score=+%{tx.critical_anomaly_score}',\
    setvar:'tx.anomaly_score_pl1=+%{tx.critical_anomaly_score}'"


SecRule ARGS_NAMES "@rx ^(?:jsessionid|aspsessionid|asp\.net_sessionid|phpsession|phpsessid|weblogicsession|session_id|session-id|cfid|cftoken|cfsid|jservsession|jwsession)$" \
    "id:943110,\
    phase:2,\
    block,\
    capture,\
    t:none,t:urlDecodeUni,t:lowercase,\
    msg:'可能的会话固定攻击:SessionID参数名称与域外引用',\
    logdata:'Matched Data: %{TX.0} found within %{MATCHED_VAR_NAME}: %{MATCHED_VAR}',\
    tag:'application-multi',\
    tag:'language-multi',\
    tag:'platform-multi',\
    tag:'attack-fixation',\
    tag:'OWASP_CRS',\
    tag:'OWASP_CRS/WEB_ATTACK/SESSION_FIXATION',\
    tag:'WASCTC/WASC-37',\
    tag:'CAPEC-61',\
    ctl:auditLogParts=+E,\
    ver:'OWASP_CRS/3.2.0',\
    severity:'CRITICAL',\
    chain"
    SecRule REQUEST_HEADERS:Referer "@rx ^(?:ht|f)tps?://(.*?)\/" \
        "capture,\
        chain"
        SecRule TX:1 "!@endsWith %{request_headers.host}" \
            "setvar:'tx.session_fixation_score=+%{tx.critical_anomaly_score}',\
            setvar:'tx.anomaly_score_pl1=+%{tx.critical_anomaly_score}'"


SecRule ARGS_NAMES "@rx ^(?:jsessionid|aspsessionid|asp\.net_sessionid|phpsession|phpsessid|weblogicsession|session_id|session-id|cfid|cftoken|cfsid|jservsession|jwsession)$" \
    "id:943120,\
    phase:2,\
    block,\
    capture,\
    t:none,t:urlDecodeUni,t:lowercase,\
    msg:'可能的会话固定攻击:无引用的SessionID参数名',\
    logdata:'Matched Data: %{TX.0} found within %{MATCHED_VAR_NAME}: %{MATCHED_VAR}',\
    tag:'application-multi',\
    tag:'language-multi',\
    tag:'platform-multi',\
    tag:'attack-fixation',\
    tag:'OWASP_CRS',\
    tag:'OWASP_CRS/WEB_ATTACK/SESSION_FIXATION',\
    tag:'WASCTC/WASC-37',\
    tag:'CAPEC-61',\
    ctl:auditLogParts=+E,\
    ver:'OWASP_CRS/3.2.0',\
    severity:'CRITICAL',\
    chain"
    SecRule &REQUEST_HEADERS:Referer "@eq 0" \
        "setvar:'tx.session_fixation_score=+%{tx.critical_anomaly_score}',\
        setvar:'tx.anomaly_score_pl1=+%{tx.critical_anomaly_score}'"




SecRule TX:EXECUTING_PARANOIA_LEVEL "@lt 2" "id:943013,phase:1,pass,nolog,skipAfter:END-REQUEST-943-APPLICATION-ATTACK-SESSION-FIXATION"
SecRule TX:EXECUTING_PARANOIA_LEVEL "@lt 2" "id:943014,phase:2,pass,nolog,skipAfter:END-REQUEST-943-APPLICATION-ATTACK-SESSION-FIXATION"
#
# -= Paranoia Level 2 =- (apply only when tx.executing_paranoia_level is sufficiently high: 2 or higher)
#



SecRule TX:EXECUTING_PARANOIA_LEVEL "@lt 3" "id:943015,phase:1,pass,nolog,skipAfter:END-REQUEST-943-APPLICATION-ATTACK-SESSION-FIXATION"
SecRule TX:EXECUTING_PARANOIA_LEVEL "@lt 3" "id:943016,phase:2,pass,nolog,skipAfter:END-REQUEST-943-APPLICATION-ATTACK-SESSION-FIXATION"
#
# -= Paranoia Level 3 =- (apply only when tx.executing_paranoia_level is sufficiently high: 3 or higher)
#



SecRule TX:EXECUTING_PARANOIA_LEVEL "@lt 4" "id:943017,phase:1,pass,nolog,skipAfter:END-REQUEST-943-APPLICATION-ATTACK-SESSION-FIXATION"
SecRule TX:EXECUTING_PARANOIA_LEVEL "@lt 4" "id:943018,phase:2,pass,nolog,skipAfter:END-REQUEST-943-APPLICATION-ATTACK-SESSION-FIXATION"
#
# -= Paranoia Level 4 =- (apply only when tx.executing_paranoia_level is sufficiently high: 4 or higher)
#



#
# -= Paranoia Levels Finished =-
#
SecMarker "END-REQUEST-943-APPLICATION-ATTACK-SESSION-FIXATION"
