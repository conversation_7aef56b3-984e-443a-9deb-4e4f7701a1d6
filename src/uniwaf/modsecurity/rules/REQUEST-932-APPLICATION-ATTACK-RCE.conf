###
# ------------------------------------------------------------------------
# OWASP ModSecurity Core Rule Set ver.3.2.0
# Copyright (c) 2006-2019 Trustwave and contributors. All rights reserved.
#
# The OWASP ModSecurity Core Rule Set is distributed under
# Apache Software License (ASL) version 2
# Please see the enclosed LICENSE file for full details.
# ------------------------------------------------------------------------

#
# -= Paranoia Level 0 (empty) =- (apply unconditionally)
#



SecRule TX:EXECUTING_PARANOIA_LEVEL "@lt 1" "id:932011,phase:1,pass,nolog,skipAfter:END-REQUEST-932-APPLICATION-ATTACK-RCE"
SecRule TX:EXECUTING_PARANOIA_LEVEL "@lt 1" "id:932012,phase:2,pass,nolog,skipAfter:END-REQUEST-932-APPLICATION-ATTACK-RCE"
#
# -= Paranoia Level 1 (default) =- (apply only when tx.executing_paranoia_level is sufficiently high: 1 or higher)
#


# [ Unix command injection ]
#
# This rule detects Unix command injections.
# A command injection takes a form such as:
#
#   foo.jpg;uname -a
#   foo.jpg||uname -a
#
# The vulnerability exists when an application executes a shell command
# without proper input escaping/validation.
#
# This rule is also triggered by an Oracle WebLogic Remote Command Execution exploit:
# [ Oracle WebLogic vulnerability CVE-2017-10271 - Exploit tested: https://www.exploit-db.com/exploits/43458 ]
#
# To prevent false positives, we look for a 'starting sequence' that
# precedes a command in shell syntax, such as: ; | & $( ` <( >(
# Anatomy of the regexp with examples of patterns caught:
#
# 1. Starting tokens
#
# ;        ;ifconfig
# \{       {ifconfig}
# \|       |ifconfig
# \|\|     ||ifconfig
# &        &ifconfig
# &&       &&ifconfig
# \n       ;\nifconfig
# \r       ;\rifconfig
# \$\(     $(ifconfig)
# $\(\(    $((ifconfig))
# `        `ifconfig`
# \${      ${ifconfig}
# <\(      <( ifconfig )
# >\(      >( ifconfig )
# \(\s*\)  a() ( ifconfig; ); a
#
# 2. Command prefixes
#
# {        { ifconfig }
# \s*\(\s* ( ifconfig )
# \w+=(?:[^\s]*|\$.*|\$.*|<.*|>.*|\'.*\'|\".*\")\s+   VARNAME=xyz ifconfig
# !\s*     ! ifconfig
# \$       $ifconfig
#
# 3. Quoting
#
# '        'ifconfig'
# \"       "ifconfig"
#
# 4. Paths
#
# [\?\*\[\]\(\)\-\|+\w'\"\./\\\\]+/   /sbin/ifconfig, /s?in/./ifconfig, /s[a-b]in/ifconfig etc.
#
# This rule is case-sensitive to prevent FP ("Cat" vs. "cat").
#
# An effort was made to combat evasions by shell quoting (e.g. 'ls',
# 'l'"s", \l\s are all valid). ModSecurity has a t:cmdLine
# transformation built-in to deal with this, but unfortunately, it
# replaces ';' characters and lowercases the payload, which is less
# useful for this case. However, emulating the transformation makes
# the regexp more complex.
#
# To rebuild the word list regexp:
#   cd util/regexp-assemble
#   cat regexp-932100.txt | ./regexp-cmdline.py unix | ./regexp-assemble.pl
#
# Then insert the assembled regexp into this template:
#
# SecRule REQUEST_COOKIES|!REQUEST_COOKIES:/__utm/|REQUEST_COOKIES_NAMES|ARGS_NAMES|ARGS|XML:/* "@rx (?:;|\{|\||\|\||&|&&|\n|\r|\$\(|\$\(\(|`|\${|<\(|>\(|\(\s*\))\s*(?:{|\s*\(\s*|\w+=(?:[^\s]*|\$.*|\$.*|<.*|>.*|\'.*\'|\".*\")\s+|!\s*|\$)*\s*(?:'|\")*(?:[\?\*\[\]\(\)\-\|+\w'\"\./\\\\]+/)?[\\\\'\"]*
# [regexp assembled from util/regexp-assemble/regexp-932100.txt]
# \b" \
#
# This is the base Rule to prevent Unix Command Injection
# Please refer other rules 932105,932106 to know more.
#
#  .932100
#  ├── 932105
#  ├── 932106
#
SecRule REQUEST_COOKIES|!REQUEST_COOKIES:/__utm/|REQUEST_COOKIES_NAMES|ARGS_NAMES|ARGS|XML:/* "@rx (?:;|\{|\||\|\||&|&&|\n|\r|\$\(|\$\(\(|`|\${|<\(|>\(|\(\s*\))\s*(?:{|\s*\(\s*|\w+=(?:[^\s]*|\$.*|\$.*|<.*|>.*|\'.*\'|\".*\")\s+|!\s*|\$)*\s*(?:'|\")*(?:[\?\*\[\]\(\)\-\|+\w'\"\./\\\\]+/)?[\\\\'\"]*(?:l[\\\\'\"]*(?:w[\\\\'\"]*p[\\\\'\"]*-[\\\\'\"]*(?:d[\\\\'\"]*(?:o[\\\\'\"]*w[\\\\'\"]*n[\\\\'\"]*l[\\\\'\"]*o[\\\\'\"]*a[\\\\'\"]*d|u[\\\\'\"]*m[\\\\'\"]*p)|r[\\\\'\"]*e[\\\\'\"]*q[\\\\'\"]*u[\\\\'\"]*e[\\\\'\"]*s[\\\\'\"]*t|m[\\\\'\"]*i[\\\\'\"]*r[\\\\'\"]*r[\\\\'\"]*o[\\\\'\"]*r)|s(?:[\\\\'\"]*(?:b[\\\\'\"]*_[\\\\'\"]*r[\\\\'\"]*e[\\\\'\"]*l[\\\\'\"]*e[\\\\'\"]*a[\\\\'\"]*s[\\\\'\"]*e|c[\\\\'\"]*p[\\\\'\"]*u|m[\\\\'\"]*o[\\\\'\"]*d|p[\\\\'\"]*c[\\\\'\"]*i|u[\\\\'\"]*s[\\\\'\"]*b|-[\\\\'\"]*F|h[\\\\'\"]*w|o[\\\\'\"]*f))?|z[\\\\'\"]*(?:(?:[ef][\\\\'\"]*)?g[\\\\'\"]*r[\\\\'\"]*e[\\\\'\"]*p|c[\\\\'\"]*(?:a[\\\\'\"]*t|m[\\\\'\"]*p)|m[\\\\'\"]*(?:o[\\\\'\"]*r[\\\\'\"]*e|a)|d[\\\\'\"]*i[\\\\'\"]*f[\\\\'\"]*f|l[\\\\'\"]*e[\\\\'\"]*s[\\\\'\"]*s)|e[\\\\'\"]*s[\\\\'\"]*s[\\\\'\"]*(?:(?:f[\\\\'\"]*i[\\\\'\"]*l|p[\\\\'\"]*i[\\\\'\"]*p)[\\\\'\"]*e|e[\\\\'\"]*c[\\\\'\"]*h[\\\\'\"]*o|(?:\s|<|>).*)|a[\\\\'\"]*s[\\\\'\"]*t[\\\\'\"]*(?:l[\\\\'\"]*o[\\\\'\"]*g(?:[\\\\'\"]*i[\\\\'\"]*n)?|c[\\\\'\"]*o[\\\\'\"]*m[\\\\'\"]*m|(?:\s|<|>).*)|o[\\\\'\"]*(?:c[\\\\'\"]*a[\\\\'\"]*(?:t[\\\\'\"]*e|l)[\\\\'\"]*(?:\s|<|>).*|g[\\\\'\"]*n[\\\\'\"]*a[\\\\'\"]*m[\\\\'\"]*e)|d[\\\\'\"]*(?:c[\\\\'\"]*o[\\\\'\"]*n[\\\\'\"]*f[\\\\'\"]*i[\\\\'\"]*g|d[\\\\'\"]*(?:\s|<|>).*)|f[\\\\'\"]*t[\\\\'\"]*p(?:[\\\\'\"]*g[\\\\'\"]*e[\\\\'\"]*t)?|(?:[np]|y[\\\\'\"]*n[\\\\'\"]*x)[\\\\'\"]*(?:\s|<|>).*)|b[\\\\'\"]*(?:z[\\\\'\"]*(?:(?:[ef][\\\\'\"]*)?g[\\\\'\"]*r[\\\\'\"]*e[\\\\'\"]*p|d[\\\\'\"]*i[\\\\'\"]*f[\\\\'\"]*f|l[\\\\'\"]*e[\\\\'\"]*s[\\\\'\"]*s|m[\\\\'\"]*o[\\\\'\"]*r[\\\\'\"]*e|c[\\\\'\"]*a[\\\\'\"]*t|i[\\\\'\"]*p[\\\\'\"]*2)|s[\\\\'\"]*d[\\\\'\"]*(?:c[\\\\'\"]*a[\\\\'\"]*t|i[\\\\'\"]*f[\\\\'\"]*f|t[\\\\'\"]*a[\\\\'\"]*r)|a[\\\\'\"]*(?:t[\\\\'\"]*c[\\\\'\"]*h[\\\\'\"]*(?:\s|<|>).*|s[\\\\'\"]*h)|r[\\\\'\"]*e[\\\\'\"]*a[\\\\'\"]*k[\\\\'\"]*s[\\\\'\"]*w|u[\\\\'\"]*i[\\\\'\"]*l[\\\\'\"]*t[\\\\'\"]*i[\\\\'\"]*n)|c[\\\\'\"]*(?:o[\\\\'\"]*(?:m[\\\\'\"]*(?:p[\\\\'\"]*r[\\\\'\"]*e[\\\\'\"]*s[\\\\'\"]*s|m[\\\\'\"]*a[\\\\'\"]*n[\\\\'\"]*d)[\\\\'\"]*(?:\s|<|>).*|p[\\\\'\"]*r[\\\\'\"]*o[\\\\'\"]*c)|h[\\\\'\"]*(?:d[\\\\'\"]*i[\\\\'\"]*r[\\\\'\"]*(?:\s|<|>).*|f[\\\\'\"]*l[\\\\'\"]*a[\\\\'\"]*g[\\\\'\"]*s|a[\\\\'\"]*t[\\\\'\"]*t[\\\\'\"]*r|m[\\\\'\"]*o[\\\\'\"]*d)|r[\\\\'\"]*o[\\\\'\"]*n[\\\\'\"]*t[\\\\'\"]*a[\\\\'\"]*b|(?:[cp]|a[\\\\'\"]*t)[\\\\'\"]*(?:\s|<|>).*|u[\\\\'\"]*r[\\\\'\"]*l|s[\\\\'\"]*h)|f[\\\\'\"]*(?:i(?:[\\\\'\"]*(?:l[\\\\'\"]*e[\\\\'\"]*(?:t[\\\\'\"]*e[\\\\'\"]*s[\\\\'\"]*t|(?:\s|<|>).*)|n[\\\\'\"]*d[\\\\'\"]*(?:\s|<|>).*))?|t[\\\\'\"]*p[\\\\'\"]*(?:s[\\\\'\"]*t[\\\\'\"]*a[\\\\'\"]*t[\\\\'\"]*s|w[\\\\'\"]*h[\\\\'\"]*o|(?:\s|<|>).*)|u[\\\\'\"]*n[\\\\'\"]*c[\\\\'\"]*t[\\\\'\"]*i[\\\\'\"]*o[\\\\'\"]*n|(?:e[\\\\'\"]*t[\\\\'\"]*c[\\\\'\"]*h|c)[\\\\'\"]*(?:\s|<|>).*|o[\\\\'\"]*r[\\\\'\"]*e[\\\\'\"]*a[\\\\'\"]*c[\\\\'\"]*h|g[\\\\'\"]*r[\\\\'\"]*e[\\\\'\"]*p)|e[\\\\'\"]*(?:n[\\\\'\"]*(?:v(?:[\\\\'\"]*-[\\\\'\"]*u[\\\\'\"]*p[\\\\'\"]*d[\\\\'\"]*a[\\\\'\"]*t[\\\\'\"]*e)?|d[\\\\'\"]*(?:i[\\\\'\"]*f|s[\\\\'\"]*w))|x[\\\\'\"]*(?:p[\\\\'\"]*(?:a[\\\\'\"]*n[\\\\'\"]*d|o[\\\\'\"]*r[\\\\'\"]*t|r)|e[\\\\'\"]*c[\\\\'\"]*(?:\s|<|>).*)|c[\\\\'\"]*h[\\\\'\"]*o[\\\\'\"]*(?:\s|<|>).*|g[\\\\'\"]*r[\\\\'\"]*e[\\\\'\"]*p|s[\\\\'\"]*a[\\\\'\"]*c|v[\\\\'\"]*a[\\\\'\"]*l)|h[\\\\'\"]*(?:t[\\\\'\"]*(?:d[\\\\'\"]*i[\\\\'\"]*g[\\\\'\"]*e[\\\\'\"]*s[\\\\'\"]*t|p[\\\\'\"]*a[\\\\'\"]*s[\\\\'\"]*s[\\\\'\"]*w[\\\\'\"]*d)|o[\\\\'\"]*s[\\\\'\"]*t[\\\\'\"]*(?:n[\\\\'\"]*a[\\\\'\"]*m[\\\\'\"]*e|i[\\\\'\"]*d)|(?:e[\\\\'\"]*a[\\\\'\"]*d|u[\\\\'\"]*p)[\\\\'\"]*(?:\s|<|>).*|i[\\\\'\"]*s[\\\\'\"]*t[\\\\'\"]*o[\\\\'\"]*r[\\\\'\"]*y)|i[\\\\'\"]*(?:p[\\\\'\"]*(?:(?:6[\\\\'\"]*)?t[\\\\'\"]*a[\\\\'\"]*b[\\\\'\"]*l[\\\\'\"]*e[\\\\'\"]*s|c[\\\\'\"]*o[\\\\'\"]*n[\\\\'\"]*f[\\\\'\"]*i[\\\\'\"]*g)|r[\\\\'\"]*b(?:[\\\\'\"]*(?:1(?:[\\\\'\"]*[89])?|2[\\\\'\"]*[012]))?|f[\\\\'\"]*c[\\\\'\"]*o[\\\\'\"]*n[\\\\'\"]*f[\\\\'\"]*i[\\\\'\"]*g|d[\\\\'\"]*(?:\s|<|>).*)|g[\\\\'\"]*(?:(?:e[\\\\'\"]*t[\\\\'\"]*f[\\\\'\"]*a[\\\\'\"]*c[\\\\'\"]*l|r[\\\\'\"]*e[\\\\'\"]*p|c[\\\\'\"]*c|i[\\\\'\"]*t)[\\\\'\"]*(?:\s|<|>).*|z[\\\\'\"]*(?:c[\\\\'\"]*a[\\\\'\"]*t|i[\\\\'\"]*p)|u[\\\\'\"]*n[\\\\'\"]*z[\\\\'\"]*i[\\\\'\"]*p|d[\\\\'\"]*b)|a[\\\\'\"]*(?:(?:l[\\\\'\"]*i[\\\\'\"]*a[\\\\'\"]*s|w[\\\\'\"]*k)[\\\\'\"]*(?:\s|<|>).*|d[\\\\'\"]*d[\\\\'\"]*u[\\\\'\"]*s[\\\\'\"]*e[\\\\'\"]*r|p[\\\\'\"]*t[\\\\'\"]*-[\\\\'\"]*g[\\\\'\"]*e[\\\\'\"]*t|r[\\\\'\"]*(?:c[\\\\'\"]*h[\\\\'\"]*(?:\s|<|>).*|p))|d[\\\\'\"]*(?:h[\\\\'\"]*c[\\\\'\"]*l[\\\\'\"]*i[\\\\'\"]*e[\\\\'\"]*n[\\\\'\"]*t|(?:i[\\\\'\"]*f[\\\\'\"]*f|u)[\\\\'\"]*(?:\s|<|>).*|(?:m[\\\\'\"]*e[\\\\'\"]*s|p[\\\\'\"]*k)[\\\\'\"]*g|o[\\\\'\"]*(?:a[\\\\'\"]*s|n[\\\\'\"]*e)|a[\\\\'\"]*s[\\\\'\"]*h)|m[\\\\'\"]*(?:(?:k[\\\\'\"]*d[\\\\'\"]*i[\\\\'\"]*r|o[\\\\'\"]*r[\\\\'\"]*e)[\\\\'\"]*(?:\s|<|>).*|a[\\\\'\"]*i[\\\\'\"]*l[\\\\'\"]*(?:x[\\\\'\"]*(?:\s|<|>).*|q)|l[\\\\'\"]*o[\\\\'\"]*c[\\\\'\"]*a[\\\\'\"]*t[\\\\'\"]*e)|j[\\\\'\"]*(?:(?:a[\\\\'\"]*v[\\\\'\"]*a|o[\\\\'\"]*b[\\\\'\"]*s)[\\\\'\"]*(?:\s|<|>).*|e[\\\\'\"]*x[\\\\'\"]*e[\\\\'\"]*c)|k[\\\\'\"]*i[\\\\'\"]*l[\\\\'\"]*l[\\\\'\"]*(?:a[\\\\'\"]*l[\\\\'\"]*l|(?:\s|<|>).*)|(?:G[\\\\'\"]*E[\\\\'\"]*T[\\\\'\"]*(?:\s|<|>)|\.\s).*|7[\\\\'\"]*z(?:[\\\\'\"]*[ar])?)\b" \
    "id:932100,\
    phase:2,\
    block,\
    capture,\
    t:none,\
    msg:'远程命令执行:Unix命令注入',\
    logdata:'Matched Data: %{TX.0} found within %{MATCHED_VAR_NAME}: %{MATCHED_VAR}',\
    tag:'application-multi',\
    tag:'language-shell',\
    tag:'platform-unix',\
    tag:'attack-rce',\
    tag:'OWASP_CRS',\
    tag:'OWASP_CRS/WEB_ATTACK/COMMAND_INJECTION',\
    tag:'WASCTC/WASC-31',\
    tag:'OWASP_TOP_10/A1',\
    tag:'PCI/6.5.2',\
    ctl:auditLogParts=+E,\
    ver:'OWASP_CRS/3.2.0',\
    severity:'CRITICAL',\
    setvar:'tx.rce_score=+%{tx.critical_anomaly_score}',\
    setvar:'tx.anomaly_score_pl1=+%{tx.critical_anomaly_score}'"

# Apache 2.2 requires configuration file lines to be under 8kB.
# Therefore, some remaining commands have been split off to a separate rule.
# For explanation of this rule, see rule 932100.
#
# To rebuild the word list regexp:
#   cd util/regexp-assemble
#   cat regexp-932105.txt | ./regexp-cmdline.py unix | ./regexp-assemble.pl
#
# Then insert the assembled regexp into this template:
#
# SecRule REQUEST_COOKIES|!REQUEST_COOKIES:/__utm/|REQUEST_COOKIES_NAMES|ARGS_NAMES|ARGS|XML:/* "@rx (?:;|\{|\||\|\||&|&&|\n|\r|\$\(|\$\(\(|`|\${|<\(|>\(|\(\s*\))\s*(?:{|\s*\(\s*|\w+=(?:[^\s]*|\$.*|\$.*|<.*|>.*|\'.*\'|\".*\")\s+|!\s*|\$)*\s*(?:'|\")*(?:[\?\*\[\]\(\)\-\|+\w'\"\./\\\\]+/)?[\\\\'\"]*
# [regexp assembled from util/regexp-assemble/regexp-932105.txt]
# \b" \
#
SecRule REQUEST_COOKIES|!REQUEST_COOKIES:/__utm/|REQUEST_COOKIES_NAMES|ARGS_NAMES|ARGS|XML:/* "@rx (?:;|\{|\||\|\||&|&&|\n|\r|\$\(|\$\(\(|`|\${|<\(|>\(|\(\s*\))\s*(?:{|\s*\(\s*|\w+=(?:[^\s]*|\$.*|\$.*|<.*|>.*|\'.*\'|\".*\")\s+|!\s*|\$)*\s*(?:'|\")*(?:[\?\*\[\]\(\)\-\|+\w'\"\./\\\\]+/)?[\\\\'\"]*(?:s[\\\\'\"]*(?:e[\\\\'\"]*(?:t[\\\\'\"]*(?:(?:f[\\\\'\"]*a[\\\\'\"]*c[\\\\'\"]*l[\\\\'\"]*)?(?:\s|<|>).*|e[\\\\'\"]*n[\\\\'\"]*v|s[\\\\'\"]*i[\\\\'\"]*d)|n[\\\\'\"]*d[\\\\'\"]*m[\\\\'\"]*a[\\\\'\"]*i[\\\\'\"]*l|d[\\\\'\"]*(?:\s|<|>).*)|h[\\\\'\"]*(?:\.[\\\\'\"]*d[\\\\'\"]*i[\\\\'\"]*s[\\\\'\"]*t[\\\\'\"]*r[\\\\'\"]*i[\\\\'\"]*b|u[\\\\'\"]*t[\\\\'\"]*d[\\\\'\"]*o[\\\\'\"]*w[\\\\'\"]*n|(?:\s|<|>).*)|o[\\\\'\"]*(?:(?:u[\\\\'\"]*r[\\\\'\"]*c[\\\\'\"]*e|r[\\\\'\"]*t)[\\\\'\"]*(?:\s|<|>).*|c[\\\\'\"]*a[\\\\'\"]*t)|c[\\\\'\"]*(?:h[\\\\'\"]*e[\\\\'\"]*d|p[\\\\'\"]*(?:\s|<|>).*)|t[\\\\'\"]*r[\\\\'\"]*i[\\\\'\"]*n[\\\\'\"]*g[\\\\'\"]*s|(?:l[\\\\'\"]*e[\\\\'\"]*e|f[\\\\'\"]*t)[\\\\'\"]*p|y[\\\\'\"]*s[\\\\'\"]*c[\\\\'\"]*t[\\\\'\"]*l|u[\\\\'\"]*(?:(?:\s|<|>).*|d[\\\\'\"]*o)|d[\\\\'\"]*i[\\\\'\"]*f[\\\\'\"]*f|s[\\\\'\"]*h|v[\\\\'\"]*n)|p[\\\\'\"]*(?:k[\\\\'\"]*(?:g(?:(?:[\\\\'\"]*_)?[\\\\'\"]*i[\\\\'\"]*n[\\\\'\"]*f[\\\\'\"]*o)?|e[\\\\'\"]*x[\\\\'\"]*e[\\\\'\"]*c|i[\\\\'\"]*l[\\\\'\"]*l)|t[\\\\'\"]*a[\\\\'\"]*r(?:[\\\\'\"]*(?:d[\\\\'\"]*i[\\\\'\"]*f[\\\\'\"]*f|g[\\\\'\"]*r[\\\\'\"]*e[\\\\'\"]*p))?|a[\\\\'\"]*(?:t[\\\\'\"]*c[\\\\'\"]*h[\\\\'\"]*(?:\s|<|>).*|s[\\\\'\"]*s[\\\\'\"]*w[\\\\'\"]*d)|r[\\\\'\"]*i[\\\\'\"]*n[\\\\'\"]*t[\\\\'\"]*(?:e[\\\\'\"]*n[\\\\'\"]*v|f[\\\\'\"]*(?:\s|<|>).*)|y[\\\\'\"]*t[\\\\'\"]*h[\\\\'\"]*o[\\\\'\"]*n(?:[\\\\'\"]*(?:3(?:[\\\\'\"]*m)?|2))?|e[\\\\'\"]*r[\\\\'\"]*(?:l(?:[\\\\'\"]*(?:s[\\\\'\"]*h|5))?|m[\\\\'\"]*s)|(?:g[\\\\'\"]*r[\\\\'\"]*e|f[\\\\'\"]*t)[\\\\'\"]*p|(?:u[\\\\'\"]*s[\\\\'\"]*h|o[\\\\'\"]*p)[\\\\'\"]*d|h[\\\\'\"]*p(?:[\\\\'\"]*[57])?|i[\\\\'\"]*n[\\\\'\"]*g|s[\\\\'\"]*(?:\s|<|>).*)|n[\\\\'\"]*(?:c[\\\\'\"]*(?:\.[\\\\'\"]*(?:t[\\\\'\"]*r[\\\\'\"]*a[\\\\'\"]*d[\\\\'\"]*i[\\\\'\"]*t[\\\\'\"]*i[\\\\'\"]*o[\\\\'\"]*n[\\\\'\"]*a[\\\\'\"]*l|o[\\\\'\"]*p[\\\\'\"]*e[\\\\'\"]*n[\\\\'\"]*b[\\\\'\"]*s[\\\\'\"]*d)|(?:\s|<|>).*|a[\\\\'\"]*t)|e[\\\\'\"]*t[\\\\'\"]*(?:k[\\\\'\"]*i[\\\\'\"]*t[\\\\'\"]*-[\\\\'\"]*f[\\\\'\"]*t[\\\\'\"]*p|(?:s[\\\\'\"]*t|c)[\\\\'\"]*a[\\\\'\"]*t|(?:\s|<|>).*)|s[\\\\'\"]*(?:l[\\\\'\"]*o[\\\\'\"]*o[\\\\'\"]*k[\\\\'\"]*u[\\\\'\"]*p|t[\\\\'\"]*a[\\\\'\"]*t)|(?:a[\\\\'\"]*n[\\\\'\"]*o|i[\\\\'\"]*c[\\\\'\"]*e)[\\\\'\"]*(?:\s|<|>).*|(?:o[\\\\'\"]*h[\\\\'\"]*u|m[\\\\'\"]*a)[\\\\'\"]*p|p[\\\\'\"]*i[\\\\'\"]*n[\\\\'\"]*g)|r[\\\\'\"]*(?:e[\\\\'\"]*(?:(?:p[\\\\'\"]*(?:l[\\\\'\"]*a[\\\\'\"]*c[\\\\'\"]*e|e[\\\\'\"]*a[\\\\'\"]*t)|n[\\\\'\"]*a[\\\\'\"]*m[\\\\'\"]*e)[\\\\'\"]*(?:\s|<|>).*|a[\\\\'\"]*l[\\\\'\"]*p[\\\\'\"]*a[\\\\'\"]*t[\\\\'\"]*h)|m[\\\\'\"]*(?:(?:d[\\\\'\"]*i[\\\\'\"]*r[\\\\'\"]*)?(?:\s|<|>).*|u[\\\\'\"]*s[\\\\'\"]*e[\\\\'\"]*r)|u[\\\\'\"]*b[\\\\'\"]*y(?:[\\\\'\"]*(?:1(?:[\\\\'\"]*[89])?|2[\\\\'\"]*[012]))?|(?:a[\\\\'\"]*r|c[\\\\'\"]*p|p[\\\\'\"]*m)[\\\\'\"]*(?:\s|<|>).*|n[\\\\'\"]*a[\\\\'\"]*n[\\\\'\"]*o|o[\\\\'\"]*u[\\\\'\"]*t[\\\\'\"]*e|s[\\\\'\"]*y[\\\\'\"]*n[\\\\'\"]*c)|t[\\\\'\"]*(?:c[\\\\'\"]*(?:p[\\\\'\"]*(?:t[\\\\'\"]*r[\\\\'\"]*a[\\\\'\"]*c[\\\\'\"]*e[\\\\'\"]*r[\\\\'\"]*o[\\\\'\"]*u[\\\\'\"]*t[\\\\'\"]*e|i[\\\\'\"]*n[\\\\'\"]*g)|s[\\\\'\"]*h)|r[\\\\'\"]*a[\\\\'\"]*c[\\\\'\"]*e[\\\\'\"]*r[\\\\'\"]*o[\\\\'\"]*u[\\\\'\"]*t[\\\\'\"]*e(?:[\\\\'\"]*6)?|e[\\\\'\"]*(?:l[\\\\'\"]*n[\\\\'\"]*e[\\\\'\"]*t|e[\\\\'\"]*(?:\s|<|>).*)|i[\\\\'\"]*m[\\\\'\"]*e[\\\\'\"]*(?:o[\\\\'\"]*u[\\\\'\"]*t|(?:\s|<|>).*)|a[\\\\'\"]*(?:i[\\\\'\"]*l(?:[\\\\'\"]*f)?|r[\\\\'\"]*(?:\s|<|>).*)|o[\\\\'\"]*(?:u[\\\\'\"]*c[\\\\'\"]*h[\\\\'\"]*(?:\s|<|>).*|p))|u[\\\\'\"]*(?:n[\\\\'\"]*(?:l[\\\\'\"]*(?:i[\\\\'\"]*n[\\\\'\"]*k[\\\\'\"]*(?:\s|<|>).*|z[\\\\'\"]*m[\\\\'\"]*a)|c[\\\\'\"]*o[\\\\'\"]*m[\\\\'\"]*p[\\\\'\"]*r[\\\\'\"]*e[\\\\'\"]*s[\\\\'\"]*s|a[\\\\'\"]*m[\\\\'\"]*e|r[\\\\'\"]*a[\\\\'\"]*r|s[\\\\'\"]*e[\\\\'\"]*t|z[\\\\'\"]*i[\\\\'\"]*p|x[\\\\'\"]*z)|s[\\\\'\"]*e[\\\\'\"]*r[\\\\'\"]*(?:(?:a[\\\\'\"]*d|m[\\\\'\"]*o)[\\\\'\"]*d|d[\\\\'\"]*e[\\\\'\"]*l)|l[\\\\'\"]*i[\\\\'\"]*m[\\\\'\"]*i[\\\\'\"]*t[\\\\'\"]*(?:\s|<|>).*)|m[\\\\'\"]*(?:y[\\\\'\"]*s[\\\\'\"]*q[\\\\'\"]*l(?:[\\\\'\"]*(?:d[\\\\'\"]*u[\\\\'\"]*m[\\\\'\"]*p(?:[\\\\'\"]*s[\\\\'\"]*l[\\\\'\"]*o[\\\\'\"]*w)?|h[\\\\'\"]*o[\\\\'\"]*t[\\\\'\"]*c[\\\\'\"]*o[\\\\'\"]*p[\\\\'\"]*y|a[\\\\'\"]*d[\\\\'\"]*m[\\\\'\"]*i[\\\\'\"]*n|s[\\\\'\"]*h[\\\\'\"]*o[\\\\'\"]*w))?|(?:(?:o[\\\\'\"]*u[\\\\'\"]*n|u[\\\\'\"]*t)[\\\\'\"]*t|v)[\\\\'\"]*(?:\s|<|>).*)|x[\\\\'\"]*(?:z[\\\\'\"]*(?:(?:[ef][\\\\'\"]*)?g[\\\\'\"]*r[\\\\'\"]*e[\\\\'\"]*p|d[\\\\'\"]*(?:i[\\\\'\"]*f[\\\\'\"]*f|e[\\\\'\"]*c)|c[\\\\'\"]*(?:a[\\\\'\"]*t|m[\\\\'\"]*p)|l[\\\\'\"]*e[\\\\'\"]*s[\\\\'\"]*s|m[\\\\'\"]*o[\\\\'\"]*r[\\\\'\"]*e|(?:\s|<|>).*)|a[\\\\'\"]*r[\\\\'\"]*g[\\\\'\"]*s|t[\\\\'\"]*e[\\\\'\"]*r[\\\\'\"]*m|x[\\\\'\"]*d[\\\\'\"]*(?:\s|<|>).*)|z[\\\\'\"]*(?:(?:[ef][\\\\'\"]*)?g[\\\\'\"]*r[\\\\'\"]*e[\\\\'\"]*p|c[\\\\'\"]*(?:a[\\\\'\"]*t|m[\\\\'\"]*p)|d[\\\\'\"]*i[\\\\'\"]*f[\\\\'\"]*f|i[\\\\'\"]*p[\\\\'\"]*(?:\s|<|>).*|l[\\\\'\"]*e[\\\\'\"]*s[\\\\'\"]*s|m[\\\\'\"]*o[\\\\'\"]*r[\\\\'\"]*e|r[\\\\'\"]*u[\\\\'\"]*n|s[\\\\'\"]*h)|o[\\\\'\"]*(?:p[\\\\'\"]*e[\\\\'\"]*n[\\\\'\"]*s[\\\\'\"]*s[\\\\'\"]*l|n[\\\\'\"]*i[\\\\'\"]*n[\\\\'\"]*t[\\\\'\"]*r)|w[\\\\'\"]*(?:h[\\\\'\"]*o[\\\\'\"]*(?:a[\\\\'\"]*m[\\\\'\"]*i|(?:\s|<|>).*)|g[\\\\'\"]*e[\\\\'\"]*t|3[\\\\'\"]*m)|v[\\\\'\"]*i[\\\\'\"]*(?:m[\\\\'\"]*(?:\s|<|>).*|g[\\\\'\"]*r|p[\\\\'\"]*w)|y[\\\\'\"]*u[\\\\'\"]*m)\b" \
    "id:932105,\
    phase:2,\
    block,\
    capture,\
    t:none,\
    msg:'远程命令执行:Unix命令注入',\
    logdata:'Matched Data: %{TX.0} found within %{MATCHED_VAR_NAME}: %{MATCHED_VAR}',\
    tag:'application-multi',\
    tag:'language-shell',\
    tag:'platform-unix',\
    tag:'attack-rce',\
    tag:'OWASP_CRS',\
    tag:'OWASP_CRS/WEB_ATTACK/COMMAND_INJECTION',\
    tag:'WASCTC/WASC-31',\
    tag:'OWASP_TOP_10/A1',\
    tag:'PCI/6.5.2',\
    ctl:auditLogParts=+E,\
    ver:'OWASP_CRS/3.2.0',\
    severity:'CRITICAL',\
    setvar:'tx.rce_score=+%{tx.critical_anomaly_score}',\
    setvar:'tx.anomaly_score_pl1=+%{tx.critical_anomaly_score}'"


# [ Windows command injection ]
#
# This rule detects Windows shell command injections.
# If you are not running Windows, it is safe to disable this rule.
#
# A command injection takes a form such as:
#
#   foo.jpg&ver /r
#   foo.jpg|ver /r
#
# The vulnerability exists when an application executes a shell command
# without proper input escaping/validation.
#
# To prevent false positives, we look for a 'starting sequence' that
# precedes a command in CMD syntax, such as: ; | & `
#
# Anatomy of the regexp:
#
# 1. Starting tokens
#
# ;        ;cmd
# \{       {cmd
# \|       |cmd
# \|\|     ||cmd
# &        &cmd
# &&       &&cmd
# \n       \ncmd
# \r       \rcmd
# `        `cmd
#
# 2. Command prefixes
#
# (        (cmd)
# ,        ,cmd
# @        @cmd
# '        'cmd'
# "        "cmd"
# \s       spacing+cmd
#
# 3. Paths
#
# [\w'\"\./]+/                          /path/cmd
# [\\\\'\"\^]*\w[\\\\'\"\^]*:.*\\\\     C:\Program Files\cmd
# [\^\.\w '\"/\\\\]*\\\\)?[\"\^]*       \\net\share\dir\cmd
#
# 4. Quoting
#
# \"       "cmd"
# \^       ^cmd
#
# 5. Extension/switches
#
# \.[\"\^]*\w+      cmd.com, cmd.exe, etc.
# /b                cmd/h
#
# An effort is made to combat evasions by CMD syntax; for example,
# the following strings are valid: c^md, @cmd, "c"md. ModSecurity
# has a t:cmdLine transformation built-in to deal with some of these,
# but unfortunately, that transformation replaces ';' characters (so
# we cannot match on the start of a command) and '\' characters (so we
# have trouble matching paths). This makes the regexp more complex.
#
# This rule is case-insensitive.
#
# To rebuild the word list regexp:
#   cd util/regexp-assemble
#   cat regexp-932110.txt | ./regexp-cmdline.py windows | ./regexp-assemble.pl
#
# Then insert the assembled regexp into this template:
#
# SecRule REQUEST_COOKIES|!REQUEST_COOKIES:/__utm/|REQUEST_COOKIES_NAMES|ARGS_NAMES|ARGS|XML:/* "@rx (?i)(?:;|\{|\||\|\||&|&&|\n|\r|`)\s*[\(,@\'\"\s]*(?:[\w'\"\./]+/|[\\\\'\"\^]*\w[\\\\'\"\^]*:.*\\\\|[\^\.\w '\"/\\\\]*\\\\)?[\"\^]*
# [regexp assembled from util/regexp-assemble/regexp-932110.txt]
# (?:\.[\"\^]*\w+)?\b" \
#
SecRule REQUEST_COOKIES|!REQUEST_COOKIES:/__utm/|REQUEST_COOKIES_NAMES|ARGS_NAMES|ARGS|XML:/* "@rx (?i)(?:;|\{|\||\|\||&|&&|\n|\r|`)\s*[\(,@\'\"\s]*(?:[\w'\"\./]+/|[\\\\'\"\^]*\w[\\\\'\"\^]*:.*\\\\|[\^\.\w '\"/\\\\]*\\\\)?[\"\^]*(?:m[\"\^]*(?:y[\"\^]*s[\"\^]*q[\"\^]*l(?:[\"\^]*(?:d[\"\^]*u[\"\^]*m[\"\^]*p(?:[\"\^]*s[\"\^]*l[\"\^]*o[\"\^]*w)?|h[\"\^]*o[\"\^]*t[\"\^]*c[\"\^]*o[\"\^]*p[\"\^]*y|a[\"\^]*d[\"\^]*m[\"\^]*i[\"\^]*n|s[\"\^]*h[\"\^]*o[\"\^]*w))?|s[\"\^]*(?:i[\"\^]*(?:n[\"\^]*f[\"\^]*o[\"\^]*3[\"\^]*2|e[\"\^]*x[\"\^]*e[\"\^]*c)|c[\"\^]*o[\"\^]*n[\"\^]*f[\"\^]*i[\"\^]*g|g[\"\^]*(?:[\s,;]|\.|/|<|>).*|t[\"\^]*s[\"\^]*c)|o[\"\^]*(?:u[\"\^]*n[\"\^]*t[\"\^]*(?:(?:[\s,;]|\.|/|<|>).*|v[\"\^]*o[\"\^]*l)|v[\"\^]*e[\"\^]*u[\"\^]*s[\"\^]*e[\"\^]*r|[dr][\"\^]*e[\"\^]*(?:[\s,;]|\.|/|<|>).*)|k[\"\^]*(?:d[\"\^]*i[\"\^]*r[\"\^]*(?:[\s,;]|\.|/|<|>).*|l[\"\^]*i[\"\^]*n[\"\^]*k)|d[\"\^]*(?:s[\"\^]*c[\"\^]*h[\"\^]*e[\"\^]*d|(?:[\s,;]|\.|/|<|>).*)|a[\"\^]*p[\"\^]*i[\"\^]*s[\"\^]*e[\"\^]*n[\"\^]*d|b[\"\^]*s[\"\^]*a[\"\^]*c[\"\^]*l[\"\^]*i|e[\"\^]*a[\"\^]*s[\"\^]*u[\"\^]*r[\"\^]*e|m[\"\^]*s[\"\^]*y[\"\^]*s)|d[\"\^]*(?:i[\"\^]*(?:s[\"\^]*k[\"\^]*(?:(?:m[\"\^]*g[\"\^]*m|p[\"\^]*a[\"\^]*r)[\"\^]*t|s[\"\^]*h[\"\^]*a[\"\^]*d[\"\^]*o[\"\^]*w)|r[\"\^]*(?:(?:[\s,;]|\.|/|<|>).*|u[\"\^]*s[\"\^]*e)|f[\"\^]*f[\"\^]*(?:[\s,;]|\.|/|<|>).*)|e[\"\^]*(?:l[\"\^]*(?:p[\"\^]*r[\"\^]*o[\"\^]*f|t[\"\^]*r[\"\^]*e[\"\^]*e|(?:[\s,;]|\.|/|<|>).*)|v[\"\^]*(?:m[\"\^]*g[\"\^]*m[\"\^]*t|c[\"\^]*o[\"\^]*n)|(?:f[\"\^]*r[\"\^]*a|b[\"\^]*u)[\"\^]*g)|s[\"\^]*(?:a[\"\^]*(?:c[\"\^]*l[\"\^]*s|d[\"\^]*d)|q[\"\^]*u[\"\^]*e[\"\^]*r[\"\^]*y|m[\"\^]*o[\"\^]*(?:v[\"\^]*e|d)|g[\"\^]*e[\"\^]*t|r[\"\^]*m)|(?:r[\"\^]*i[\"\^]*v[\"\^]*e[\"\^]*r[\"\^]*q[\"\^]*u[\"\^]*e[\"\^]*r|o[\"\^]*s[\"\^]*k[\"\^]*e)[\"\^]*y|(?:c[\"\^]*o[\"\^]*m[\"\^]*c[\"\^]*n[\"\^]*f|x[\"\^]*d[\"\^]*i[\"\^]*a)[\"\^]*g|a[\"\^]*t[\"\^]*e[\"\^]*(?:[\s,;]|\.|/|<|>).*|n[\"\^]*s[\"\^]*s[\"\^]*t[\"\^]*a[\"\^]*t)|c[\"\^]*(?:o[\"\^]*(?:m[\"\^]*(?:p[\"\^]*(?:(?:a[\"\^]*c[\"\^]*t[\"\^]*)?(?:[\s,;]|\.|/|<|>).*|m[\"\^]*g[\"\^]*m[\"\^]*t)|e[\"\^]*x[\"\^]*p)|n[\"\^]*(?:2[\"\^]*p|v[\"\^]*e)[\"\^]*r[\"\^]*t|p[\"\^]*y)|l[\"\^]*(?:e[\"\^]*a[\"\^]*(?:n[\"\^]*m[\"\^]*g[\"\^]*r|r[\"\^]*m[\"\^]*e[\"\^]*m)|u[\"\^]*s[\"\^]*t[\"\^]*e[\"\^]*r)|h[\"\^]*(?:k[\"\^]*(?:n[\"\^]*t[\"\^]*f[\"\^]*s|d[\"\^]*s[\"\^]*k)|d[\"\^]*i[\"\^]*r[\"\^]*(?:[\s,;]|\.|/|<|>).*)|s[\"\^]*(?:c[\"\^]*(?:r[\"\^]*i[\"\^]*p[\"\^]*t|c[\"\^]*m[\"\^]*d)|v[\"\^]*d[\"\^]*e)|e[\"\^]*r[\"\^]*t[\"\^]*(?:u[\"\^]*t[\"\^]*i[\"\^]*l|r[\"\^]*e[\"\^]*q)|a[\"\^]*(?:l[\"\^]*l[\"\^]*(?:[\s,;]|\.|/|<|>).*|c[\"\^]*l[\"\^]*s)|m[\"\^]*d(?:[\"\^]*k[\"\^]*e[\"\^]*y)?|i[\"\^]*p[\"\^]*h[\"\^]*e[\"\^]*r|u[\"\^]*r[\"\^]*l)|f[\"\^]*(?:o[\"\^]*r[\"\^]*(?:m[\"\^]*a[\"\^]*t[\"\^]*(?:[\s,;]|\.|/|<|>).*|f[\"\^]*i[\"\^]*l[\"\^]*e[\"\^]*s|e[\"\^]*a[\"\^]*c[\"\^]*h)|i[\"\^]*n[\"\^]*d[\"\^]*(?:(?:[\s,;]|\.|/|<|>).*|s[\"\^]*t[\"\^]*r)|s[\"\^]*(?:m[\"\^]*g[\"\^]*m[\"\^]*t|u[\"\^]*t[\"\^]*i[\"\^]*l)|t[\"\^]*(?:p[\"\^]*(?:[\s,;]|\.|/|<|>).*|y[\"\^]*p[\"\^]*e)|r[\"\^]*e[\"\^]*e[\"\^]*d[\"\^]*i[\"\^]*s[\"\^]*k|c[\"\^]*(?:[\s,;]|\.|/|<|>).*|g[\"\^]*r[\"\^]*e[\"\^]*p)|n[\"\^]*(?:e[\"\^]*t[\"\^]*(?:s[\"\^]*(?:t[\"\^]*a[\"\^]*t|v[\"\^]*c|h)|(?:[\s,;]|\.|/|<|>).*|c[\"\^]*a[\"\^]*t|d[\"\^]*o[\"\^]*m)|t[\"\^]*(?:b[\"\^]*a[\"\^]*c[\"\^]*k[\"\^]*u[\"\^]*p|r[\"\^]*i[\"\^]*g[\"\^]*h[\"\^]*t[\"\^]*s)|(?:s[\"\^]*l[\"\^]*o[\"\^]*o[\"\^]*k[\"\^]*u|m[\"\^]*a)[\"\^]*p|c[\"\^]*(?:(?:[\s,;]|\.|/|<|>).*|a[\"\^]*t)|b[\"\^]*t[\"\^]*s[\"\^]*t[\"\^]*a[\"\^]*t)|e[\"\^]*(?:x[\"\^]*p[\"\^]*(?:a[\"\^]*n[\"\^]*d[\"\^]*(?:[\s,;]|\.|/|<|>).*|l[\"\^]*o[\"\^]*r[\"\^]*e[\"\^]*r)|v[\"\^]*e[\"\^]*n[\"\^]*t[\"\^]*(?:c[\"\^]*r[\"\^]*e[\"\^]*a[\"\^]*t[\"\^]*e|v[\"\^]*w[\"\^]*r)|n[\"\^]*d[\"\^]*l[\"\^]*o[\"\^]*c[\"\^]*a[\"\^]*l|g[\"\^]*r[\"\^]*e[\"\^]*p|r[\"\^]*a[\"\^]*s[\"\^]*e|c[\"\^]*h[\"\^]*o)|g[\"\^]*(?:a[\"\^]*t[\"\^]*h[\"\^]*e[\"\^]*r[\"\^]*n[\"\^]*e[\"\^]*t[\"\^]*w[\"\^]*o[\"\^]*r[\"\^]*k[\"\^]*i[\"\^]*n[\"\^]*f[\"\^]*o|p[\"\^]*(?:(?:r[\"\^]*e[\"\^]*s[\"\^]*u[\"\^]*l|e[\"\^]*d[\"\^]*i)[\"\^]*t|u[\"\^]*p[\"\^]*d[\"\^]*a[\"\^]*t[\"\^]*e)|i[\"\^]*t[\"\^]*(?:[\s,;]|\.|/|<|>).*|e[\"\^]*t[\"\^]*m[\"\^]*a[\"\^]*c)|i[\"\^]*(?:r[\"\^]*b(?:[\"\^]*(?:1(?:[\"\^]*[89])?|2[\"\^]*[012]))?|f[\"\^]*m[\"\^]*e[\"\^]*m[\"\^]*b[\"\^]*e[\"\^]*r|p[\"\^]*c[\"\^]*o[\"\^]*n[\"\^]*f[\"\^]*i[\"\^]*g|n[\"\^]*e[\"\^]*t[\"\^]*c[\"\^]*p[\"\^]*l|c[\"\^]*a[\"\^]*c[\"\^]*l[\"\^]*s)|a[\"\^]*(?:d[\"\^]*(?:d[\"\^]*u[\"\^]*s[\"\^]*e[\"\^]*r[\"\^]*s|m[\"\^]*o[\"\^]*d[\"\^]*c[\"\^]*m[\"\^]*d)|r[\"\^]*p[\"\^]*(?:[\s,;]|\.|/|<|>).*|t[\"\^]*t[\"\^]*r[\"\^]*i[\"\^]*b|s[\"\^]*s[\"\^]*o[\"\^]*c|z[\"\^]*m[\"\^]*a[\"\^]*n)|l[\"\^]*(?:o[\"\^]*g[\"\^]*(?:e[\"\^]*v[\"\^]*e[\"\^]*n[\"\^]*t|t[\"\^]*i[\"\^]*m[\"\^]*e|m[\"\^]*a[\"\^]*n|o[\"\^]*f[\"\^]*f)|a[\"\^]*b[\"\^]*e[\"\^]*l[\"\^]*(?:[\s,;]|\.|/|<|>).*|u[\"\^]*s[\"\^]*r[\"\^]*m[\"\^]*g[\"\^]*r)|b[\"\^]*(?:(?:c[\"\^]*d[\"\^]*(?:b[\"\^]*o[\"\^]*o|e[\"\^]*d[\"\^]*i)|r[\"\^]*o[\"\^]*w[\"\^]*s[\"\^]*t[\"\^]*a)[\"\^]*t|i[\"\^]*t[\"\^]*s[\"\^]*a[\"\^]*d[\"\^]*m[\"\^]*i[\"\^]*n|o[\"\^]*o[\"\^]*t[\"\^]*c[\"\^]*f[\"\^]*g)|h[\"\^]*(?:o[\"\^]*s[\"\^]*t[\"\^]*n[\"\^]*a[\"\^]*m[\"\^]*e|d[\"\^]*w[\"\^]*w[\"\^]*i[\"\^]*z)|j[\"\^]*a[\"\^]*v[\"\^]*a[\"\^]*(?:[\s,;]|\.|/|<|>).*|7[\"\^]*z(?:[\"\^]*[ar])?)(?:\.[\"\^]*\w+)?\b" \
    "id:932110,\
    phase:2,\
    block,\
    capture,\
    t:none,\
    msg:'远程命令执行:Windows命令注入',\
    logdata:'Matched Data: %{TX.0} found within %{MATCHED_VAR_NAME}: %{MATCHED_VAR}',\
    tag:'application-multi',\
    tag:'language-shell',\
    tag:'platform-windows',\
    tag:'attack-rce',\
    tag:'OWASP_CRS',\
    tag:'OWASP_CRS/WEB_ATTACK/COMMAND_INJECTION',\
    tag:'WASCTC/WASC-31',\
    tag:'OWASP_TOP_10/A1',\
    tag:'PCI/6.5.2',\
    ctl:auditLogParts=+E,\
    ver:'OWASP_CRS/3.2.0',\
    severity:'CRITICAL',\
    setvar:'tx.rce_score=+%{tx.critical_anomaly_score}',\
    setvar:'tx.anomaly_score_pl1=+%{tx.critical_anomaly_score}'"

# Apache 2.2 requires configuration file lines to be under 8kB.
# Therefore, some remaining commands have been split off to a separate rule.
# For explanation of this rule, see rule 932110.
#
# This rule is also triggered by an Oracle WebLogic Remote Command Execution exploit:
# [ Oracle WebLogic vulnerability CVE-2017-10271 - Exploit tested: https://www.exploit-db.com/exploits/43458 ]
#
# To rebuild the word list regexp:
#   cd util/regexp-assemble
#   cat regexp-932115.txt | ./regexp-cmdline.py windows | ./regexp-assemble.pl
#
# Then insert the assembled regexp into this template:
#
# SecRule REQUEST_COOKIES|!REQUEST_COOKIES:/__utm/|REQUEST_COOKIES_NAMES|ARGS_NAMES|ARGS|XML:/* "@rx (?i)(?:;|\{|\||\|\||&|&&|\n|\r|`)\s*[\(,@\'\"\s]*(?:[\w'\"\./]+/|[\\\\'\"\^]*\w[\\\\'\"\^]*:.*\\\\|[\^\.\w '\"/\\\\]*\\\\)?[\"\^]*
# [regexp assembled from util/regexp-assemble/regexp-932110.txt]
# (?:\.[\"\^]*\w+)?\b" \
#
SecRule REQUEST_COOKIES|!REQUEST_COOKIES:/__utm/|REQUEST_COOKIES_NAMES|ARGS_NAMES|ARGS|XML:/* "@rx (?i)(?:;|\{|\||\|\||&|&&|\n|\r|`)\s*[\(,@\'\"\s]*(?:[\w'\"\./]+/|[\\\\'\"\^]*\w[\\\\'\"\^]*:.*\\\\|[\^\.\w '\"/\\\\]*\\\\)?[\"\^]*(?:s[\"\^]*(?:y[\"\^]*s[\"\^]*(?:t[\"\^]*e[\"\^]*m[\"\^]*(?:p[\"\^]*r[\"\^]*o[\"\^]*p[\"\^]*e[\"\^]*r[\"\^]*t[\"\^]*i[\"\^]*e[\"\^]*s[\"\^]*(?:d[\"\^]*a[\"\^]*t[\"\^]*a[\"\^]*e[\"\^]*x[\"\^]*e[\"\^]*c[\"\^]*u[\"\^]*t[\"\^]*i[\"\^]*o[\"\^]*n[\"\^]*p[\"\^]*r[\"\^]*e[\"\^]*v[\"\^]*e[\"\^]*n[\"\^]*t[\"\^]*i[\"\^]*o[\"\^]*n|(?:p[\"\^]*e[\"\^]*r[\"\^]*f[\"\^]*o[\"\^]*r[\"\^]*m[\"\^]*a[\"\^]*n[\"\^]*c|h[\"\^]*a[\"\^]*r[\"\^]*d[\"\^]*w[\"\^]*a[\"\^]*r)[\"\^]*e|a[\"\^]*d[\"\^]*v[\"\^]*a[\"\^]*n[\"\^]*c[\"\^]*e[\"\^]*d)|i[\"\^]*n[\"\^]*f[\"\^]*o)|k[\"\^]*e[\"\^]*y|d[\"\^]*m)|h[\"\^]*(?:o[\"\^]*(?:w[\"\^]*(?:g[\"\^]*r[\"\^]*p|m[\"\^]*b[\"\^]*r)[\"\^]*s|r[\"\^]*t[\"\^]*c[\"\^]*u[\"\^]*t)|e[\"\^]*l[\"\^]*l[\"\^]*r[\"\^]*u[\"\^]*n[\"\^]*a[\"\^]*s|u[\"\^]*t[\"\^]*d[\"\^]*o[\"\^]*w[\"\^]*n|r[\"\^]*p[\"\^]*u[\"\^]*b[\"\^]*w|a[\"\^]*r[\"\^]*e|i[\"\^]*f[\"\^]*t)|e[\"\^]*(?:t[\"\^]*(?:(?:x[\"\^]*)?(?:[\s,;]|\.|/|<|>).*|l[\"\^]*o[\"\^]*c[\"\^]*a[\"\^]*l)|c[\"\^]*p[\"\^]*o[\"\^]*l|l[\"\^]*e[\"\^]*c[\"\^]*t)|c[\"\^]*(?:h[\"\^]*t[\"\^]*a[\"\^]*s[\"\^]*k[\"\^]*s|l[\"\^]*i[\"\^]*s[\"\^]*t)|u[\"\^]*b[\"\^]*(?:i[\"\^]*n[\"\^]*a[\"\^]*c[\"\^]*l|s[\"\^]*t)|t[\"\^]*a[\"\^]*r[\"\^]*t[\"\^]*(?:[\s,;]|\.|/|<|>).*|i[\"\^]*g[\"\^]*v[\"\^]*e[\"\^]*r[\"\^]*i[\"\^]*f|l[\"\^]*(?:e[\"\^]*e[\"\^]*p|m[\"\^]*g[\"\^]*r)|o[\"\^]*r[\"\^]*t|f[\"\^]*c|v[\"\^]*n)|p[\"\^]*(?:s[\"\^]*(?:s[\"\^]*(?:h[\"\^]*u[\"\^]*t[\"\^]*d[\"\^]*o[\"\^]*w[\"\^]*n|e[\"\^]*r[\"\^]*v[\"\^]*i[\"\^]*c[\"\^]*e|u[\"\^]*s[\"\^]*p[\"\^]*e[\"\^]*n[\"\^]*d)|l[\"\^]*(?:o[\"\^]*g[\"\^]*(?:g[\"\^]*e[\"\^]*d[\"\^]*o[\"\^]*n|l[\"\^]*i[\"\^]*s[\"\^]*t)|i[\"\^]*s[\"\^]*t)|p[\"\^]*(?:a[\"\^]*s[\"\^]*s[\"\^]*w[\"\^]*d|i[\"\^]*n[\"\^]*g)|g[\"\^]*e[\"\^]*t[\"\^]*s[\"\^]*i[\"\^]*d|e[\"\^]*x[\"\^]*e[\"\^]*c|f[\"\^]*i[\"\^]*l[\"\^]*e|i[\"\^]*n[\"\^]*f[\"\^]*o|k[\"\^]*i[\"\^]*l[\"\^]*l)|o[\"\^]*(?:w[\"\^]*e[\"\^]*r[\"\^]*(?:s[\"\^]*h[\"\^]*e[\"\^]*l[\"\^]*l(?:[\"\^]*_[\"\^]*i[\"\^]*s[\"\^]*e)?|c[\"\^]*f[\"\^]*g)|r[\"\^]*t[\"\^]*q[\"\^]*r[\"\^]*y|p[\"\^]*d)|r[\"\^]*(?:i[\"\^]*n[\"\^]*t[\"\^]*(?:(?:[\s,;]|\.|/|<|>).*|b[\"\^]*r[\"\^]*m)|n[\"\^]*(?:c[\"\^]*n[\"\^]*f[\"\^]*g|m[\"\^]*n[\"\^]*g[\"\^]*r)|o[\"\^]*m[\"\^]*p[\"\^]*t)|a[\"\^]*t[\"\^]*h[\"\^]*(?:p[\"\^]*i[\"\^]*n[\"\^]*g|(?:[\s,;]|\.|/|<|>).*)|e[\"\^]*r[\"\^]*(?:l(?:[\"\^]*(?:s[\"\^]*h|5))?|f[\"\^]*m[\"\^]*o[\"\^]*n)|y[\"\^]*t[\"\^]*h[\"\^]*o[\"\^]*n(?:[\"\^]*(?:3(?:[\"\^]*m)?|2))?|k[\"\^]*g[\"\^]*m[\"\^]*g[\"\^]*r|h[\"\^]*p(?:[\"\^]*[57])?|u[\"\^]*s[\"\^]*h[\"\^]*d|i[\"\^]*n[\"\^]*g)|r[\"\^]*(?:e[\"\^]*(?:(?:p[\"\^]*l[\"\^]*a[\"\^]*c[\"\^]*e|n(?:[\"\^]*a[\"\^]*m[\"\^]*e)?|s[\"\^]*e[\"\^]*t)[\"\^]*(?:[\s,;]|\.|/|<|>).*|g[\"\^]*(?:s[\"\^]*v[\"\^]*r[\"\^]*3[\"\^]*2|e[\"\^]*d[\"\^]*i[\"\^]*t|(?:[\s,;]|\.|/|<|>).*|i[\"\^]*n[\"\^]*i)|c[\"\^]*(?:d[\"\^]*i[\"\^]*s[\"\^]*c|o[\"\^]*v[\"\^]*e[\"\^]*r)|k[\"\^]*e[\"\^]*y[\"\^]*w[\"\^]*i[\"\^]*z)|u[\"\^]*(?:n[\"\^]*(?:d[\"\^]*l[\"\^]*l[\"\^]*3[\"\^]*2|a[\"\^]*s)|b[\"\^]*y[\"\^]*(?:1(?:[\"\^]*[89])?|2[\"\^]*[012]))|a[\"\^]*(?:s[\"\^]*(?:p[\"\^]*h[\"\^]*o[\"\^]*n[\"\^]*e|d[\"\^]*i[\"\^]*a[\"\^]*l)|r[\"\^]*(?:[\s,;]|\.|/|<|>).*)|m[\"\^]*(?:(?:d[\"\^]*i[\"\^]*r[\"\^]*)?(?:[\s,;]|\.|/|<|>).*|t[\"\^]*s[\"\^]*h[\"\^]*a[\"\^]*r[\"\^]*e)|o[\"\^]*(?:u[\"\^]*t[\"\^]*e[\"\^]*(?:[\s,;]|\.|/|<|>).*|b[\"\^]*o[\"\^]*c[\"\^]*o[\"\^]*p[\"\^]*y)|s[\"\^]*(?:t[\"\^]*r[\"\^]*u[\"\^]*i|y[\"\^]*n[\"\^]*c)|d[\"\^]*(?:[\s,;]|\.|/|<|>).*)|t[\"\^]*(?:a[\"\^]*(?:s[\"\^]*k[\"\^]*(?:k[\"\^]*i[\"\^]*l[\"\^]*l|l[\"\^]*i[\"\^]*s[\"\^]*t|s[\"\^]*c[\"\^]*h[\"\^]*d|m[\"\^]*g[\"\^]*r)|k[\"\^]*e[\"\^]*o[\"\^]*w[\"\^]*n)|(?:i[\"\^]*m[\"\^]*e[\"\^]*o[\"\^]*u|p[\"\^]*m[\"\^]*i[\"\^]*n[\"\^]*i|e[\"\^]*l[\"\^]*n[\"\^]*e|l[\"\^]*i[\"\^]*s)[\"\^]*t|s[\"\^]*(?:d[\"\^]*i[\"\^]*s[\"\^]*c[\"\^]*o|s[\"\^]*h[\"\^]*u[\"\^]*t[\"\^]*d)[\"\^]*n|y[\"\^]*p[\"\^]*e[\"\^]*(?:p[\"\^]*e[\"\^]*r[\"\^]*f|(?:[\s,;]|\.|/|<|>).*)|r[\"\^]*(?:a[\"\^]*c[\"\^]*e[\"\^]*r[\"\^]*t|e[\"\^]*e))|w[\"\^]*(?:i[\"\^]*n[\"\^]*(?:d[\"\^]*i[\"\^]*f[\"\^]*f|m[\"\^]*s[\"\^]*d[\"\^]*p|v[\"\^]*a[\"\^]*r|r[\"\^]*[ms])|u[\"\^]*(?:a[\"\^]*(?:u[\"\^]*c[\"\^]*l[\"\^]*t|p[\"\^]*p)|s[\"\^]*a)|s[\"\^]*c[\"\^]*(?:r[\"\^]*i[\"\^]*p[\"\^]*t|u[\"\^]*i)|e[\"\^]*v[\"\^]*t[\"\^]*u[\"\^]*t[\"\^]*i[\"\^]*l|m[\"\^]*i[\"\^]*(?:m[\"\^]*g[\"\^]*m[\"\^]*t|c)|a[\"\^]*i[\"\^]*t[\"\^]*f[\"\^]*o[\"\^]*r|h[\"\^]*o[\"\^]*a[\"\^]*m[\"\^]*i|g[\"\^]*e[\"\^]*t)|u[\"\^]*(?:s[\"\^]*(?:e[\"\^]*r[\"\^]*a[\"\^]*c[\"\^]*c[\"\^]*o[\"\^]*u[\"\^]*n[\"\^]*t[\"\^]*c[\"\^]*o[\"\^]*n[\"\^]*t[\"\^]*r[\"\^]*o[\"\^]*l[\"\^]*s[\"\^]*e[\"\^]*t[\"\^]*t[\"\^]*i[\"\^]*n[\"\^]*g[\"\^]*s|r[\"\^]*s[\"\^]*t[\"\^]*a[\"\^]*t)|n[\"\^]*(?:r[\"\^]*a[\"\^]*r|z[\"\^]*i[\"\^]*p))|q[\"\^]*(?:u[\"\^]*e[\"\^]*r[\"\^]*y[\"\^]*(?:[\s,;]|\.|/|<|>).*|p[\"\^]*r[\"\^]*o[\"\^]*c[\"\^]*e[\"\^]*s[\"\^]*s|w[\"\^]*i[\"\^]*n[\"\^]*s[\"\^]*t[\"\^]*a|g[\"\^]*r[\"\^]*e[\"\^]*p)|o[\"\^]*(?:d[\"\^]*b[\"\^]*c[\"\^]*(?:a[\"\^]*d[\"\^]*3[\"\^]*2|c[\"\^]*o[\"\^]*n[\"\^]*f)|p[\"\^]*e[\"\^]*n[\"\^]*f[\"\^]*i[\"\^]*l[\"\^]*e[\"\^]*s)|v[\"\^]*(?:o[\"\^]*l[\"\^]*(?:[\s,;]|\.|/|<|>).*|e[\"\^]*r[\"\^]*i[\"\^]*f[\"\^]*y)|x[\"\^]*c[\"\^]*(?:a[\"\^]*c[\"\^]*l[\"\^]*s|o[\"\^]*p[\"\^]*y)|z[\"\^]*i[\"\^]*p[\"\^]*(?:[\s,;]|\.|/|<|>).*)(?:\.[\"\^]*\w+)?\b" \
    "id:932115,\
    phase:2,\
    block,\
    capture,\
    t:none,\
    msg:'远程命令执行:Windows命令注入',\
    logdata:'Matched Data: %{TX.0} found within %{MATCHED_VAR_NAME}: %{MATCHED_VAR}',\
    tag:'application-multi',\
    tag:'language-shell',\
    tag:'platform-windows',\
    tag:'attack-rce',\
    tag:'OWASP_CRS',\
    tag:'OWASP_CRS/WEB_ATTACK/COMMAND_INJECTION',\
    tag:'WASCTC/WASC-31',\
    tag:'OWASP_TOP_10/A1',\
    tag:'PCI/6.5.2',\
    ctl:auditLogParts=+E,\
    ver:'OWASP_CRS/3.2.0',\
    severity:'CRITICAL',\
    setvar:'tx.rce_score=+%{tx.critical_anomaly_score}',\
    setvar:'tx.anomaly_score_pl1=+%{tx.critical_anomaly_score}'"


# [ Windows PowerShell, cmdlets and options ]
#
# Detect some common PowerShell commands, cmdlets and options.
# These commands should be relatively uncommon in normal text, but
# potentially useful for code injection.
#
# If you are not running Windows, it is safe to disable this rule.
#
# https://technet.microsoft.com/en-us/magazine/ff714569.aspx
# https://msdn.microsoft.com/en-us/powershell/scripting/core-powershell/console/powershell.exe-command-line-help
#
SecRule REQUEST_COOKIES|!REQUEST_COOKIES:/__utm/|REQUEST_COOKIES_NAMES|ARGS_NAMES|ARGS|XML:/* "@pmFromFile windows-powershell-commands.data" \
    "id:932120,\
    phase:2,\
    block,\
    capture,\
    t:none,t:urlDecodeUni,t:cmdLine,t:lowercase,\
    msg:'远程命令执行:Windows PowerShell命令发现',\
    logdata:'Matched Data: %{TX.0} found within %{MATCHED_VAR_NAME}: %{MATCHED_VAR}',\
    tag:'application-multi',\
    tag:'language-shell',\
    tag:'language-powershell',\
    tag:'platform-windows',\
    tag:'attack-rce',\
    tag:'OWASP_CRS',\
    tag:'OWASP_CRS/WEB_ATTACK/COMMAND_INJECTION',\
    tag:'WASCTC/WASC-31',\
    tag:'OWASP_TOP_10/A1',\
    tag:'PCI/6.5.2',\
    ctl:auditLogParts=+E,\
    ver:'OWASP_CRS/3.2.0',\
    severity:'CRITICAL',\
    setvar:'tx.rce_score=+%{tx.critical_anomaly_score}',\
    setvar:'tx.anomaly_score_pl1=+%{tx.critical_anomaly_score}'"


# [ Unix shell expressions ]
#
# Detects the following patterns which are common in Unix shell scripts
# and oneliners:
#
# $(foo)    Command substitution
# ${foo}    Parameter expansion
# <(foo)    Process substitution
# >(foo)    Process substitution
# $((foo))  Arithmetic expansion
#
# Regexp generated from util/regexp-assemble/regexp-932130.data using Regexp::Assemble.
# See https://coreruleset.org/20190826/optimizing-regular-expressions/ for usage.
#
SecRule REQUEST_COOKIES|!REQUEST_COOKIES:/__utm/|REQUEST_COOKIES_NAMES|ARGS_NAMES|ARGS|XML:/* "@rx (?:\$(?:\((?:\(.*\)|.*)\)|\{.*\})|[<>]\(.*\))" \
    "id:932130,\
    phase:2,\
    block,\
    capture,\
    t:none,t:urlDecodeUni,t:cmdLine,\
    msg:'远程命令执行:发现Unix Shell表达式',\
    logdata:'Matched Data: %{TX.0} found within %{MATCHED_VAR_NAME}: %{MATCHED_VAR}',\
    tag:'application-multi',\
    tag:'language-shell',\
    tag:'platform-unix',\
    tag:'attack-rce',\
    tag:'OWASP_CRS',\
    tag:'OWASP_CRS/WEB_ATTACK/COMMAND_INJECTION',\
    tag:'WASCTC/WASC-31',\
    tag:'OWASP_TOP_10/A1',\
    tag:'PCI/6.5.2',\
    ctl:auditLogParts=+E,\
    ver:'OWASP_CRS/3.2.0',\
    severity:'CRITICAL',\
    setvar:'tx.rce_score=+%{tx.critical_anomaly_score}',\
    setvar:'tx.anomaly_score_pl1=+%{tx.critical_anomaly_score}'"


# [ Windows FOR, IF commands ]
#
# This rule detects Windows command shell FOR and IF commands.
# If you are not running Windows, it is safe to disable this rule.
#
# Examples:
#
#   FOR              %a IN (set) DO
#   FOR /D           %a IN (dirs) DO
#   FOR /F "options" %a IN (text|"text") DO
#   FOR /L           %a IN (start,step,end) DO
#   FOR /R C:\dir    %A IN (set) DO
#
#   IF [/I] [NOT] EXIST filename | DEFINED define | ERRORLEVEL n | CMDEXTVERSION n
#   IF [/I] [NOT] item1   [==|EQU|NEQ|LSS|LEQ|GTR|GEQ] item2
#   IF [/I] [NOT] (item1) [==|EQU|NEQ|LSS|LEQ|GTR|GEQ] (item2)
#
# http://ss64.com/nt/if.html
# http://ss64.com/nt/for.html
#
# Regexp generated from util/regexp-assemble/regexp-932140.data using Regexp::Assemble.
# See https://coreruleset.org/20190826/optimizing-regular-expressions/ for usage.
#
SecRule REQUEST_COOKIES|!REQUEST_COOKIES:/__utm/|REQUEST_COOKIES_NAMES|ARGS_NAMES|ARGS|XML:/* "@rx \b(?:if(?:/i)?(?: not)?(?: exist\b| defined\b| errorlevel\b| cmdextversion\b|(?: |\().*(?:\bgeq\b|\bequ\b|\bneq\b|\bleq\b|\bgtr\b|\blss\b|==))|for(?:/[dflr].*)? %+[^ ]+ in\(.*\)\s?do)" \
    "id:932140,\
    phase:2,\
    block,\
    capture,\
    t:none,t:urlDecodeUni,t:cmdLine,\
    msg:'远程命令执行：发现 Windows FOR/IF 命令',\
    logdata:'Matched Data: %{TX.0} found within %{MATCHED_VAR_NAME}: %{MATCHED_VAR}',\
    tag:'application-multi',\
    tag:'language-shell',\
    tag:'platform-windows',\
    tag:'attack-rce',\
    tag:'OWASP_CRS',\
    tag:'OWASP_CRS/WEB_ATTACK/COMMAND_INJECTION',\
    tag:'WASCTC/WASC-31',\
    tag:'OWASP_TOP_10/A1',\
    tag:'PCI/6.5.2',\
    ctl:auditLogParts=+E,\
    ver:'OWASP_CRS/3.2.0',\
    severity:'CRITICAL',\
    setvar:'tx.rce_score=+%{tx.critical_anomaly_score}',\
    setvar:'tx.anomaly_score_pl1=+%{tx.critical_anomaly_score}'"


# [ Unix direct remote command execution ]
#
# Detects Unix commands at the start of a parameter (direct RCE).
# Example: foo=wget%20www.example.com
#
# This case is different from command injection (rule 932100), where a
# command string is appended (injected) to a regular parameter, and then
# passed to a shell unescaped.
#
# This rule is also triggered by an Oracle WebLogic Remote Command Execution exploit:
# [ Oracle WebLogic vulnerability CVE-2017-10271 - Exploit tested: https://www.exploit-db.com/exploits/43458 ]
#
# Due to a higher risk of false positives, the following changes have been
# made relative to rule 932100:
# 1) the set of commands is smaller
# 2) we require a trailing space (denoting command parameters) or command
#    separator character after the command
#
# To rebuild the word list regexp:
#   cd util/regexp-assemble
#   cat regexp-932150.txt | ./regexp-cmdline.py unix | ./regexp-assemble.pl
#
# Then insert the assembled regexp into this template:
#
# SecRule REQUEST_COOKIES|!REQUEST_COOKIES:/__utm/|REQUEST_COOKIES_NAMES|ARGS_NAMES|ARGS|XML:/* "@rx (?:^|=)\s*(?:{|\s*\(\s*|\w+=(?:[^\s]*|\$.*|\$.*|<.*|>.*|\'.*\'|\".*\")\s+|!\s*|\$)*\s*(?:'|\")*(?:[\?\*\[\]\(\)\-\|+\w'\"\./\\\\]+/)?[\\\\'\"]*
# [regexp assembled from util/regexp-assemble/regexp-932150.txt]
# [\\\\'\"]*(?:\s|;|\||&|<|>)" \
#
SecRule REQUEST_COOKIES|!REQUEST_COOKIES:/__utm/|REQUEST_COOKIES_NAMES|ARGS_NAMES|ARGS|XML:/* "@rx (?:^|=)\s*(?:{|\s*\(\s*|\w+=(?:[^\s]*|\$.*|\$.*|<.*|>.*|\'.*\'|\".*\")\s+|!\s*|\$)*\s*(?:'|\")*(?:[\?\*\[\]\(\)\-\|+\w'\"\./\\\\]+/)?[\\\\'\"]*(?:l[\\\\'\"]*(?:s(?:[\\\\'\"]*(?:b[\\\\'\"]*_[\\\\'\"]*r[\\\\'\"]*e[\\\\'\"]*l[\\\\'\"]*e[\\\\'\"]*a[\\\\'\"]*s[\\\\'\"]*e|c[\\\\'\"]*p[\\\\'\"]*u|m[\\\\'\"]*o[\\\\'\"]*d|p[\\\\'\"]*c[\\\\'\"]*i|u[\\\\'\"]*s[\\\\'\"]*b|-[\\\\'\"]*F|o[\\\\'\"]*f))?|z[\\\\'\"]*(?:(?:[ef][\\\\'\"]*)?g[\\\\'\"]*r[\\\\'\"]*e[\\\\'\"]*p|c[\\\\'\"]*(?:a[\\\\'\"]*t|m[\\\\'\"]*p)|m[\\\\'\"]*(?:o[\\\\'\"]*r[\\\\'\"]*e|a)|d[\\\\'\"]*i[\\\\'\"]*f[\\\\'\"]*f|l[\\\\'\"]*e[\\\\'\"]*s[\\\\'\"]*s)|e[\\\\'\"]*s[\\\\'\"]*s[\\\\'\"]*(?:(?:f[\\\\'\"]*i[\\\\'\"]*l|p[\\\\'\"]*i[\\\\'\"]*p)[\\\\'\"]*e|e[\\\\'\"]*c[\\\\'\"]*h[\\\\'\"]*o)|a[\\\\'\"]*s[\\\\'\"]*t[\\\\'\"]*(?:l[\\\\'\"]*o[\\\\'\"]*g(?:[\\\\'\"]*i[\\\\'\"]*n)?|c[\\\\'\"]*o[\\\\'\"]*m[\\\\'\"]*m)|w[\\\\'\"]*p(?:[\\\\'\"]*-[\\\\'\"]*d[\\\\'\"]*o[\\\\'\"]*w[\\\\'\"]*n[\\\\'\"]*l[\\\\'\"]*o[\\\\'\"]*a[\\\\'\"]*d)?|f[\\\\'\"]*t[\\\\'\"]*p(?:[\\\\'\"]*g[\\\\'\"]*e[\\\\'\"]*t)?|y[\\\\'\"]*n[\\\\'\"]*x)|s[\\\\'\"]*(?:e[\\\\'\"]*(?:t[\\\\'\"]*(?:e[\\\\'\"]*n[\\\\'\"]*v|s[\\\\'\"]*i[\\\\'\"]*d)|n[\\\\'\"]*d[\\\\'\"]*m[\\\\'\"]*a[\\\\'\"]*i[\\\\'\"]*l|d)|h(?:[\\\\'\"]*\.[\\\\'\"]*d[\\\\'\"]*i[\\\\'\"]*s[\\\\'\"]*t[\\\\'\"]*r[\\\\'\"]*i[\\\\'\"]*b)?|o[\\\\'\"]*(?:u[\\\\'\"]*r[\\\\'\"]*c[\\\\'\"]*e|c[\\\\'\"]*a[\\\\'\"]*t)|t[\\\\'\"]*r[\\\\'\"]*i[\\\\'\"]*n[\\\\'\"]*g[\\\\'\"]*s|y[\\\\'\"]*s[\\\\'\"]*c[\\\\'\"]*t[\\\\'\"]*l|c[\\\\'\"]*(?:h[\\\\'\"]*e[\\\\'\"]*d|p)|d[\\\\'\"]*i[\\\\'\"]*f[\\\\'\"]*f|f[\\\\'\"]*t[\\\\'\"]*p|u[\\\\'\"]*d[\\\\'\"]*o|s[\\\\'\"]*h|v[\\\\'\"]*n)|p[\\\\'\"]*(?:t[\\\\'\"]*a[\\\\'\"]*r(?:[\\\\'\"]*(?:d[\\\\'\"]*i[\\\\'\"]*f[\\\\'\"]*f|g[\\\\'\"]*r[\\\\'\"]*e[\\\\'\"]*p))?|y[\\\\'\"]*t[\\\\'\"]*h[\\\\'\"]*o[\\\\'\"]*n(?:[\\\\'\"]*(?:3(?:[\\\\'\"]*m)?|2))?|k[\\\\'\"]*(?:e[\\\\'\"]*x[\\\\'\"]*e[\\\\'\"]*c|i[\\\\'\"]*l[\\\\'\"]*l)|r[\\\\'\"]*i[\\\\'\"]*n[\\\\'\"]*t[\\\\'\"]*e[\\\\'\"]*n[\\\\'\"]*v|(?:g[\\\\'\"]*r[\\\\'\"]*e|f[\\\\'\"]*t)[\\\\'\"]*p|e[\\\\'\"]*r[\\\\'\"]*l(?:[\\\\'\"]*5)?|h[\\\\'\"]*p(?:[\\\\'\"]*[57])?|i[\\\\'\"]*n[\\\\'\"]*g|o[\\\\'\"]*p[\\\\'\"]*d)|n[\\\\'\"]*(?:c(?:[\\\\'\"]*(?:\.[\\\\'\"]*(?:t[\\\\'\"]*r[\\\\'\"]*a[\\\\'\"]*d[\\\\'\"]*i[\\\\'\"]*t[\\\\'\"]*i[\\\\'\"]*o[\\\\'\"]*n[\\\\'\"]*a[\\\\'\"]*l|o[\\\\'\"]*p[\\\\'\"]*e[\\\\'\"]*n[\\\\'\"]*b[\\\\'\"]*s[\\\\'\"]*d)|a[\\\\'\"]*t))?|e[\\\\'\"]*t[\\\\'\"]*(?:k[\\\\'\"]*i[\\\\'\"]*t[\\\\'\"]*-[\\\\'\"]*f[\\\\'\"]*t[\\\\'\"]*p|(?:s[\\\\'\"]*t|c)[\\\\'\"]*a[\\\\'\"]*t)|o[\\\\'\"]*h[\\\\'\"]*u[\\\\'\"]*p|p[\\\\'\"]*i[\\\\'\"]*n[\\\\'\"]*g|s[\\\\'\"]*t[\\\\'\"]*a[\\\\'\"]*t)|t[\\\\'\"]*(?:c[\\\\'\"]*(?:p[\\\\'\"]*(?:t[\\\\'\"]*r[\\\\'\"]*a[\\\\'\"]*c[\\\\'\"]*e[\\\\'\"]*r[\\\\'\"]*o[\\\\'\"]*u[\\\\'\"]*t[\\\\'\"]*e|i[\\\\'\"]*n[\\\\'\"]*g)|s[\\\\'\"]*h)|r[\\\\'\"]*a[\\\\'\"]*c[\\\\'\"]*e[\\\\'\"]*r[\\\\'\"]*o[\\\\'\"]*u[\\\\'\"]*t[\\\\'\"]*e(?:[\\\\'\"]*6)?|i[\\\\'\"]*m[\\\\'\"]*e(?:[\\\\'\"]*o[\\\\'\"]*u[\\\\'\"]*t)?|a[\\\\'\"]*(?:i[\\\\'\"]*l(?:[\\\\'\"]*f)?|r)|e[\\\\'\"]*l[\\\\'\"]*n[\\\\'\"]*e[\\\\'\"]*t)|r[\\\\'\"]*(?:e[\\\\'\"]*(?:p[\\\\'\"]*(?:l[\\\\'\"]*a[\\\\'\"]*c[\\\\'\"]*e|e[\\\\'\"]*a[\\\\'\"]*t)|a[\\\\'\"]*l[\\\\'\"]*p[\\\\'\"]*a[\\\\'\"]*t[\\\\'\"]*h|n[\\\\'\"]*a[\\\\'\"]*m[\\\\'\"]*e)|u[\\\\'\"]*b[\\\\'\"]*y(?:[\\\\'\"]*(?:1(?:[\\\\'\"]*[89])?|2[\\\\'\"]*[012]))?|m[\\\\'\"]*(?:u[\\\\'\"]*s[\\\\'\"]*e|d[\\\\'\"]*i)[\\\\'\"]*r|n[\\\\'\"]*a[\\\\'\"]*n[\\\\'\"]*o|s[\\\\'\"]*y[\\\\'\"]*n[\\\\'\"]*c|c[\\\\'\"]*p)|b[\\\\'\"]*(?:z[\\\\'\"]*(?:(?:[ef][\\\\'\"]*)?g[\\\\'\"]*r[\\\\'\"]*e[\\\\'\"]*p|d[\\\\'\"]*i[\\\\'\"]*f[\\\\'\"]*f|l[\\\\'\"]*e[\\\\'\"]*s[\\\\'\"]*s|m[\\\\'\"]*o[\\\\'\"]*r[\\\\'\"]*e|c[\\\\'\"]*a[\\\\'\"]*t)|s[\\\\'\"]*d[\\\\'\"]*(?:c[\\\\'\"]*a[\\\\'\"]*t|i[\\\\'\"]*f[\\\\'\"]*f|t[\\\\'\"]*a[\\\\'\"]*r)|u[\\\\'\"]*i[\\\\'\"]*l[\\\\'\"]*t[\\\\'\"]*i[\\\\'\"]*n|a[\\\\'\"]*s[\\\\'\"]*h)|m[\\\\'\"]*(?:y[\\\\'\"]*s[\\\\'\"]*q[\\\\'\"]*l[\\\\'\"]*(?:d[\\\\'\"]*u[\\\\'\"]*m[\\\\'\"]*p(?:[\\\\'\"]*s[\\\\'\"]*l[\\\\'\"]*o[\\\\'\"]*w)?|h[\\\\'\"]*o[\\\\'\"]*t[\\\\'\"]*c[\\\\'\"]*o[\\\\'\"]*p[\\\\'\"]*y|a[\\\\'\"]*d[\\\\'\"]*m[\\\\'\"]*i[\\\\'\"]*n|s[\\\\'\"]*h[\\\\'\"]*o[\\\\'\"]*w)|l[\\\\'\"]*o[\\\\'\"]*c[\\\\'\"]*a[\\\\'\"]*t[\\\\'\"]*e|a[\\\\'\"]*i[\\\\'\"]*l[\\\\'\"]*q)|u[\\\\'\"]*(?:n[\\\\'\"]*(?:c[\\\\'\"]*o[\\\\'\"]*m[\\\\'\"]*p[\\\\'\"]*r[\\\\'\"]*e[\\\\'\"]*s[\\\\'\"]*s|l[\\\\'\"]*z[\\\\'\"]*m[\\\\'\"]*a|a[\\\\'\"]*m[\\\\'\"]*e|r[\\\\'\"]*a[\\\\'\"]*r|s[\\\\'\"]*e[\\\\'\"]*t|z[\\\\'\"]*i[\\\\'\"]*p|x[\\\\'\"]*z)|s[\\\\'\"]*e[\\\\'\"]*r[\\\\'\"]*(?:(?:a[\\\\'\"]*d|m[\\\\'\"]*o)[\\\\'\"]*d|d[\\\\'\"]*e[\\\\'\"]*l))|x[\\\\'\"]*(?:z(?:[\\\\'\"]*(?:(?:[ef][\\\\'\"]*)?g[\\\\'\"]*r[\\\\'\"]*e[\\\\'\"]*p|d[\\\\'\"]*(?:i[\\\\'\"]*f[\\\\'\"]*f|e[\\\\'\"]*c)|c[\\\\'\"]*(?:a[\\\\'\"]*t|m[\\\\'\"]*p)|l[\\\\'\"]*e[\\\\'\"]*s[\\\\'\"]*s|m[\\\\'\"]*o[\\\\'\"]*r[\\\\'\"]*e))?|a[\\\\'\"]*r[\\\\'\"]*g[\\\\'\"]*s)|z[\\\\'\"]*(?:(?:(?:[ef][\\\\'\"]*)?g[\\\\'\"]*r[\\\\'\"]*e|i)[\\\\'\"]*p|c[\\\\'\"]*(?:a[\\\\'\"]*t|m[\\\\'\"]*p)|d[\\\\'\"]*i[\\\\'\"]*f[\\\\'\"]*f|l[\\\\'\"]*e[\\\\'\"]*s[\\\\'\"]*s|m[\\\\'\"]*o[\\\\'\"]*r[\\\\'\"]*e|r[\\\\'\"]*u[\\\\'\"]*n|s[\\\\'\"]*h)|f[\\\\'\"]*(?:t[\\\\'\"]*p[\\\\'\"]*(?:s[\\\\'\"]*t[\\\\'\"]*a[\\\\'\"]*t[\\\\'\"]*s|w[\\\\'\"]*h[\\\\'\"]*o)|i[\\\\'\"]*l[\\\\'\"]*e[\\\\'\"]*t[\\\\'\"]*e[\\\\'\"]*s[\\\\'\"]*t|e[\\\\'\"]*t[\\\\'\"]*c[\\\\'\"]*h|g[\\\\'\"]*r[\\\\'\"]*e[\\\\'\"]*p)|c[\\\\'\"]*(?:o[\\\\'\"]*(?:m[\\\\'\"]*m[\\\\'\"]*a[\\\\'\"]*n[\\\\'\"]*d|p[\\\\'\"]*r[\\\\'\"]*o[\\\\'\"]*c)|u[\\\\'\"]*r[\\\\'\"]*l|s[\\\\'\"]*h|c)|e[\\\\'\"]*(?:g[\\\\'\"]*r[\\\\'\"]*e[\\\\'\"]*p|c[\\\\'\"]*h[\\\\'\"]*o|v[\\\\'\"]*a[\\\\'\"]*l|x[\\\\'\"]*e[\\\\'\"]*c|n[\\\\'\"]*v)|d[\\\\'\"]*(?:m[\\\\'\"]*e[\\\\'\"]*s[\\\\'\"]*g|a[\\\\'\"]*s[\\\\'\"]*h|i[\\\\'\"]*f[\\\\'\"]*f|o[\\\\'\"]*a[\\\\'\"]*s)|g[\\\\'\"]*(?:z[\\\\'\"]*(?:c[\\\\'\"]*a[\\\\'\"]*t|i[\\\\'\"]*p)|r[\\\\'\"]*e[\\\\'\"]*p|c[\\\\'\"]*c)|j[\\\\'\"]*(?:o[\\\\'\"]*b[\\\\'\"]*s[\\\\'\"]*\s+[\\\\'\"]*-[\\\\'\"]*x|a[\\\\'\"]*v[\\\\'\"]*a)|w[\\\\'\"]*(?:h[\\\\'\"]*o[\\\\'\"]*a[\\\\'\"]*m[\\\\'\"]*i|g[\\\\'\"]*e[\\\\'\"]*t|3[\\\\'\"]*m)|i[\\\\'\"]*r[\\\\'\"]*b(?:[\\\\'\"]*(?:1(?:[\\\\'\"]*[89])?|2[\\\\'\"]*[012]))?|o[\\\\'\"]*n[\\\\'\"]*i[\\\\'\"]*n[\\\\'\"]*t[\\\\'\"]*r|h[\\\\'\"]*(?:e[\\\\'\"]*a[\\\\'\"]*d|u[\\\\'\"]*p)|v[\\\\'\"]*i[\\\\'\"]*(?:g[\\\\'\"]*r|p[\\\\'\"]*w)|G[\\\\'\"]*E[\\\\'\"]*T)[\\\\'\"]*(?:\s|;|\||&|<|>)" \
    "id:932150,\
    phase:2,\
    block,\
    capture,\
    t:none,\
    msg:'远程命令执行:直接执行Unix命令',\
    logdata:'Matched Data: %{TX.0} found within %{MATCHED_VAR_NAME}: %{MATCHED_VAR}',\
    tag:'application-multi',\
    tag:'language-shell',\
    tag:'platform-unix',\
    tag:'attack-rce',\
    tag:'OWASP_CRS',\
    tag:'OWASP_CRS/WEB_ATTACK/COMMAND_INJECTION',\
    tag:'WASCTC/WASC-31',\
    tag:'OWASP_TOP_10/A1',\
    tag:'PCI/6.5.2',\
    ctl:auditLogParts=+E,\
    ver:'OWASP_CRS/3.2.0',\
    severity:'CRITICAL',\
    setvar:'tx.rce_score=+%{tx.critical_anomaly_score}',\
    setvar:'tx.anomaly_score_pl1=+%{tx.critical_anomaly_score}'"


# [ Unix shell snippets ]
#
# Detect some common sequences found in shell commands and scripts.
#
# Some commands which were restricted in earlier rules due to FP,
# have been added here with their full path, in order to catch some
# cases where the full path is sent.
#
# This rule is also triggered by an Apache Struts Remote Code Execution exploit:
# [ Apache Struts vulnerability CVE-2017-9805 - Exploit tested: https://www.exploit-db.com/exploits/42627 ]
#
# This rule is also triggered by an Oracle WebLogic Remote Command Execution exploit:
# [ Oracle WebLogic vulnerability CVE-2017-10271 - Exploit tested: https://www.exploit-db.com/exploits/43458 ]

SecRule REQUEST_COOKIES|!REQUEST_COOKIES:/__utm/|REQUEST_COOKIES_NAMES|ARGS_NAMES|ARGS|XML:/* "@pmFromFile unix-shell.data" \
    "id:932160,\
    phase:2,\
    block,\
    capture,\
    t:none,t:urlDecodeUni,t:cmdLine,t:normalizePath,t:lowercase,\
    msg:'远程命令执行:发现Unix Shell代码',\
    logdata:'Matched Data: %{TX.0} found within %{MATCHED_VAR_NAME}: %{MATCHED_VAR}',\
    tag:'application-multi',\
    tag:'language-shell',\
    tag:'platform-unix',\
    tag:'attack-rce',\
    tag:'OWASP_CRS',\
    tag:'OWASP_CRS/WEB_ATTACK/COMMAND_INJECTION',\
    tag:'WASCTC/WASC-31',\
    tag:'OWASP_TOP_10/A1',\
    tag:'PCI/6.5.2',\
    ctl:auditLogParts=+E,\
    ver:'OWASP_CRS/3.2.0',\
    severity:'CRITICAL',\
    setvar:'tx.rce_score=+%{tx.critical_anomaly_score}',\
    setvar:'tx.anomaly_score_pl1=+%{tx.critical_anomaly_score}'"


# [ Shellshock vulnerability (CVE-2014-6271 and CVE-2014-7169) ]
#
# Detect exploitation of "Shellshock" GNU Bash RCE vulnerability.
#
# Based on ModSecurity rules created by Red Hat.
# <AUTHOR> <EMAIL>
#
# https://access.redhat.com/articles/1212303
#
SecRule REQUEST_HEADERS|REQUEST_LINE "@rx ^\(\s*\)\s+{" \
    "id:932170,\
    phase:2,\
    block,\
    capture,\
    t:none,t:urlDecode,\
    msg:'远程命令执行:Shellshock漏洞(CVE-2014-6271)',\
    logdata:'Matched Data: %{TX.0} found within %{MATCHED_VAR_NAME}: %{MATCHED_VAR}',\
    tag:'application-multi',\
    tag:'language-shell',\
    tag:'platform-unix',\
    tag:'attack-rce',\
    tag:'OWASP_CRS',\
    tag:'OWASP_CRS/WEB_ATTACK/COMMAND_INJECTION',\
    tag:'WASCTC/WASC-31',\
    tag:'OWASP_TOP_10/A1',\
    tag:'PCI/6.5.2',\
    ctl:auditLogParts=+E,\
    ver:'OWASP_CRS/3.2.0',\
    severity:'CRITICAL',\
    setvar:'tx.rce_score=+%{tx.critical_anomaly_score}',\
    setvar:'tx.anomaly_score_pl1=+%{tx.critical_anomaly_score}'"

SecRule ARGS_NAMES|ARGS|FILES_NAMES "@rx ^\(\s*\)\s+{" \
    "id:932171,\
    phase:2,\
    block,\
    capture,\
    t:none,t:urlDecode,t:urlDecodeUni,\
    msg:'远程命令执行:Shellshock漏洞(CVE-2014-6271)',\
    logdata:'Matched Data: %{TX.0} found within %{MATCHED_VAR_NAME}: %{MATCHED_VAR}',\
    tag:'application-multi',\
    tag:'language-shell',\
    tag:'platform-unix',\
    tag:'attack-rce',\
    tag:'OWASP_CRS',\
    tag:'OWASP_CRS/WEB_ATTACK/COMMAND_INJECTION',\
    tag:'WASCTC/WASC-31',\
    tag:'OWASP_TOP_10/A1',\
    tag:'PCI/6.5.2',\
    ctl:auditLogParts=+E,\
    ver:'OWASP_CRS/3.2.0',\
    severity:'CRITICAL',\
    setvar:'tx.rce_score=+%{tx.critical_anomaly_score}',\
    setvar:'tx.anomaly_score_pl1=+%{tx.critical_anomaly_score}'"


#
# -=[ Restricted File Upload ]=-
#
# Detects attempts to upload a file with a forbidden filename.
#
# Many application contain Unrestricted File Upload vulnerabilities.
# https://www.owasp.org/index.php/Unrestricted_File_Upload
#
# These might be abused to upload configuration files or other files
# that affect the behavior of the web server, possibly causing remote
# code execution.
#
SecRule FILES|REQUEST_HEADERS:X-Filename|REQUEST_HEADERS:X_Filename|REQUEST_HEADERS:X-File-Name \
    "@pmFromFile restricted-upload.data" \
    "id:932180,\
    phase:2,\
    block,\
    capture,\
    t:none,t:lowercase,\
    msg:'文件上传次数限制',\
    logdata:'Matched Data: %{TX.0} found within %{MATCHED_VAR_NAME}: %{MATCHED_VAR}',\
    tag:'application-multi',\
    tag:'language-multi',\
    tag:'platform-multi',\
    tag:'attack-rce',\
    tag:'OWASP_CRS',\
    tag:'OWASP_CRS/WEB_ATTACK/COMMAND_INJECTION',\
    tag:'WASCTC/WASC-31',\
    tag:'OWASP_TOP_10/A1',\
    tag:'PCI/6.5.2',\
    ver:'OWASP_CRS/3.2.0',\
    severity:'CRITICAL',\
    setvar:'tx.lfi_score=+%{tx.critical_anomaly_score}',\
    setvar:'tx.anomaly_score_pl1=+%{tx.critical_anomaly_score}'"


SecRule TX:EXECUTING_PARANOIA_LEVEL "@lt 2" "id:932013,phase:1,pass,nolog,skipAfter:END-REQUEST-932-APPLICATION-ATTACK-RCE"
SecRule TX:EXECUTING_PARANOIA_LEVEL "@lt 2" "id:932014,phase:2,pass,nolog,skipAfter:END-REQUEST-932-APPLICATION-ATTACK-RCE"
#
# -= Paranoia Level 2 =- (apply only when tx.executing_paranoia_level is sufficiently high: 2 or higher)
#



SecRule TX:EXECUTING_PARANOIA_LEVEL "@lt 3" "id:932015,phase:1,pass,nolog,skipAfter:END-REQUEST-932-APPLICATION-ATTACK-RCE"
SecRule TX:EXECUTING_PARANOIA_LEVEL "@lt 3" "id:932016,phase:2,pass,nolog,skipAfter:END-REQUEST-932-APPLICATION-ATTACK-RCE"
#
# -= Paranoia Level 3 =- (apply only when tx.executing_paranoia_level is sufficiently high: 3 or higher)
#

# Missing Unix commands have been added to a new word list i.e.
# util/regexp-assemble/regexp-932106.txt
# These commands may have a higher risk of false positives.
# Therefore, they have been split off to a separate rule in PL3.
# For explanation of this rule, see rule 932100.
#
# To rebuild the word list regexp:
#   cd util/regexp-assemble
#   cat regexp-932106.txt | ./regexp-cmdline.py unix | ./regexp-assemble.pl
#
# Then insert the assembled regexp into this template:
#
# SecRule REQUEST_COOKIES|!REQUEST_COOKIES:/__utm/|REQUEST_COOKIES_NAMES|ARGS_NAMES|ARGS|XML:/* "@rx (?:;|\{|\||\|\||&|&&|\n|\r|\$\(|\$\(\(|`|\${|<\(|>\(|\(\s*\))\s*(?:{|\s*\(\s*|\w+=(?:[^\s]*|\$.*|\$.*|<.*|>.*|\'.*\'|\".*\")\s+|!\s*|\$)*\s*(?:'|\")*(?:[\?\*\[\]\(\)\-\|+\w'\"\./\\\\]+/)?[\\\\'\"]*
# [regexp assembled from util/regexp-assemble/regexp-932106.txt]
# \b" \
#
# This rule is a stricter sibling of rule 932100.

SecRule REQUEST_COOKIES|!REQUEST_COOKIES:/__utm/|REQUEST_COOKIES_NAMES|ARGS_NAMES|ARGS|XML:/* "@rx (?:;|\{|\||\|\||&|&&|\n|\r|\$\(|\$\(\(|`|\${|<\(|>\(|\(\s*\))\s*(?:{|\s*\(\s*|\w+=(?:[^\s]*|\$.*|\$.*|<.*|>.*|\'.*\'|\".*\")\s+|!\s*|\$)*\s*(?:'|\")*(?:[\?\*\[\]\(\)\-\|+\w'\"\./\\\\]+/)?[\\\\'\"]*(?:(?:(?:a[\\\\'\"]*p[\\\\'\"]*t[\\\\'\"]*i[\\\\'\"]*t[\\\\'\"]*u[\\\\'\"]*d|u[\\\\'\"]*p[\\\\'\"]*2[\\\\'\"]*d[\\\\'\"]*a[\\\\'\"]*t)[\\\\'\"]*e|d[\\\\'\"]*n[\\\\'\"]*f|v[\\\\'\"]*i)[\\\\'\"]*(?:\s|<|>).*|p[\\\\'\"]*(?:a[\\\\'\"]*c[\\\\'\"]*m[\\\\'\"]*a[\\\\'\"]*n[\\\\'\"]*(?:\s|<|>).*|w[\\\\'\"]*d|s)|w[\\\\'\"]*(?:(?:\s|<|>).*|h[\\\\'\"]*o))\b" \
    "id:932106,\
    phase:2,\
    block,\
    capture,\
    t:none,\
    msg:'远程命令执行:Unix命令注入',\
    logdata:'Matched Data: %{TX.0} found within %{MATCHED_VAR_NAME}: %{MATCHED_VAR}',\
    tag:'application-multi',\
    tag:'language-shell',\
    tag:'platform-unix',\
    tag:'attack-rce',\
    tag:'OWASP_CRS',\
    tag:'OWASP_CRS/WEB_ATTACK/COMMAND_INJECTION',\
    tag:'WASCTC/WASC-31',\
    tag:'OWASP_TOP_10/A1',\
    tag:'PCI/6.5.2',\
    tag:'paranoia-level/3',\
    ctl:auditLogParts=+E,\
    ver:'OWASP_CRS/3.2.0',\
    severity:'CRITICAL',\
    setvar:'tx.rce_score=+%{tx.critical_anomaly_score}',\
    setvar:'tx.anomaly_score_pl3=+%{tx.critical_anomaly_score}'"

#
# -=[ Bypass Rule 930120 (wildcard) ]=-
#
# When Paranoia Level is set to 1 and 2, a Remote Command Execution
# could be exploited bypassing rule 930120 (OS File Access Attempt)
# by using wildcard characters.
#
# In some other cases, it could be bypassed even if the Paranoia Level is set to 3.
# Please, keep in mind that this rule could lead to many false positives.
#
SecRule ARGS "@rx (?:/|\\\\)(?:[\?\*]+[a-z/\\\\]+|[a-z/\\\\]+[\?\*]+)" \
    "id:932190,\
    phase:2,\
    block,\
    capture,\
    t:none,t:urlDecode,t:urlDecodeUni,t:normalizePath,t:cmdLine,\
    msg:'远程命令执行:通配符绕过技术尝试',\
    logdata:'Matched Data: %{TX.0} found within %{MATCHED_VAR_NAME}: %{MATCHED_VAR}',\
    tag:'application-multi',\
    tag:'language-shell',\
    tag:'platform-unix',\
    tag:'attack-rce',\
    tag:'OWASP_CRS',\
    tag:'OWASP_CRS/WEB_ATTACK/COMMAND_INJECTION',\
    tag:'WASCTC/WASC-31',\
    tag:'OWASP_TOP_10/A1',\
    tag:'PCI/6.5.2',\
    tag:'paranoia-level/3',\
    ctl:auditLogParts=+E,\
    severity:'CRITICAL',\
    setvar:'tx.rce_score=+%{tx.critical_anomaly_score}',\
    setvar:'tx.anomaly_score_pl3=+%{tx.critical_anomaly_score}'"

SecRule TX:EXECUTING_PARANOIA_LEVEL "@lt 4" "id:932017,phase:1,pass,nolog,skipAfter:END-REQUEST-932-APPLICATION-ATTACK-RCE"
SecRule TX:EXECUTING_PARANOIA_LEVEL "@lt 4" "id:932018,phase:2,pass,nolog,skipAfter:END-REQUEST-932-APPLICATION-ATTACK-RCE"
#
# -= Paranoia Level 4 =- (apply only when tx.executing_paranoia_level is sufficiently high: 4 or higher)
#



#
# -= Paranoia Levels Finished =-
#
SecMarker "END-REQUEST-932-APPLICATION-ATTACK-RCE"
