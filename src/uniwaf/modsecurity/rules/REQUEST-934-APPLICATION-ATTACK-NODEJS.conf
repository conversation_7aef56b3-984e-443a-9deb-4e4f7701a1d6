###
# ------------------------------------------------------------------------
# OWASP ModSecurity Core Rule Set ver.3.2.0
# Copyright (c) 2006-2019 Trustwave and contributors. All rights reserved.
#
# The OWASP ModSecurity Core Rule Set is distributed under
# Apache Software License (ASL) version 2
# Please see the enclosed LICENSE file for full details.
# ------------------------------------------------------------------------

#
# -= Paranoia Level 0 (empty) =- (apply unconditionally)
#



SecRule TX:EXECUTING_PARANOIA_LEVEL "@lt 1" "id:934011,phase:1,pass,nolog,skipAfter:END-REQUEST-934-APPLICATION-ATTACK-NODEJS"
SecRule TX:EXECUTING_PARANOIA_LEVEL "@lt 1" "id:934012,phase:2,pass,nolog,skipAfter:END-REQUEST-934-APPLICATION-ATTACK-NODEJS"
#
# -= Paranoia Level 1 (default) =- (apply only when tx.executing_paranoia_level is sufficiently high: 1 or higher)
#


# [ Insecure unserialization / generic RCE signatures ]
#
# Libraries performing insecure unserialization:
# - node-serialize: _$$ND_FUNC$$_ (CVE-2017-5941)
# - funcster: __js_function
#
# See:
# https://opsecx.com/index.php/2017/02/08/exploiting-node-js-deserialization-bug-for-remote-code-execution/
# https://www.acunetix.com/blog/web-security-zone/deserialization-vulnerabilities-attacking-deserialization-in-js/
#
# Some generic snippets used:
# - function() {
# - new Function(
# - eval(
# - String.fromCharCode(
#
# Last two are used by nodejsshell.py,
# https://github.com/ajinabraham/Node.Js-Security-Course/blob/master/nodejsshell.py
#
# As base64 is sometimes (but not always) used to encode serialized values,
# use multiMatch and t:base64decode.
#
# Regexp generated from util/regexp-assemble/regexp-934100.data using Regexp::Assemble.
# See https://coreruleset.org/20190826/optimizing-regular-expressions/ for usage.

SecRule REQUEST_COOKIES|!REQUEST_COOKIES:/__utm/|REQUEST_COOKIES_NAMES|ARGS_NAMES|ARGS|XML:/* "@rx (?:(?:_(?:\$\$ND_FUNC\$\$_|_js_function)|(?:new\s+Function|\beval)\s*\(|String\s*\.\s*fromCharCode|function\s*\(\s*\)\s*{|this\.constructor)|module\.exports\s*=)" \
    "id:934100,\
    phase:2,\
    block,\
    capture,\
    t:none,t:urlDecodeUni,t:base64Decode,\
    msg:'Node.js注入攻击',\
    logdata:'Matched Data: %{TX.0} found within %{MATCHED_VAR_NAME}: %{MATCHED_VAR}',\
    tag:'application-multi',\
    tag:'language-javascript',\
    tag:'platform-multi',\
    tag:'attack-rce',\
    tag:'attack-injection-nodejs',\
    tag:'OWASP_CRS/WEB_ATTACK/NODEJS_INJECTION',\
    tag:'OWASP_TOP_10/A1',\
    ctl:auditLogParts=+E,\
    ver:'OWASP_CRS/3.2.0',\
    severity:'CRITICAL',\
    multiMatch,\
    setvar:'tx.rce_score=+%{tx.critical_anomaly_score}',\
    setvar:'tx.anomaly_score_pl1=+%{tx.critical_anomaly_score}'"


SecRule TX:EXECUTING_PARANOIA_LEVEL "@lt 2" "id:934013,phase:1,pass,nolog,skipAfter:END-REQUEST-934-APPLICATION-ATTACK-NODEJS"
SecRule TX:EXECUTING_PARANOIA_LEVEL "@lt 2" "id:934014,phase:2,pass,nolog,skipAfter:END-REQUEST-934-APPLICATION-ATTACK-NODEJS"
#
# -= Paranoia Level 2 =- (apply only when tx.executing_paranoia_level is sufficiently high: 2 or higher)
#

SecRule TX:EXECUTING_PARANOIA_LEVEL "@lt 3" "id:934015,phase:1,pass,nolog,skipAfter:END-REQUEST-934-APPLICATION-ATTACK-NODEJS"
SecRule TX:EXECUTING_PARANOIA_LEVEL "@lt 3" "id:934016,phase:2,pass,nolog,skipAfter:END-REQUEST-934-APPLICATION-ATTACK-NODEJS"
#
# -= Paranoia Level 3 =- (apply only when tx.executing_paranoia_level is sufficiently high: 3 or higher)
#

SecRule TX:EXECUTING_PARANOIA_LEVEL "@lt 4" "id:934017,phase:1,pass,nolog,skipAfter:END-REQUEST-934-APPLICATION-ATTACK-NODEJS"
SecRule TX:EXECUTING_PARANOIA_LEVEL "@lt 4" "id:934018,phase:2,pass,nolog,skipAfter:END-REQUEST-934-APPLICATION-ATTACK-NODEJS"
#
# -= Paranoia Level 4 =- (apply only when tx.executing_paranoia_level is sufficiently high: 4 or higher)
#



#
# -= Paranoia Levels Finished =-
#
SecMarker "END-REQUEST-934-APPLICATION-ATTACK-NODEJS"
