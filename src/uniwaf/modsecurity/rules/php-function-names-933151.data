__autoload
addcslashes
addslashes
apache_child_terminate
apache_get_modules
apache_get_version
apache_getenv
apache_lookup_uri
apache_note
apache_request_headers
apache_reset_timeout
apache_response_headers
apache_setenv
array_change_key_case
array_chunk
array_column
array_combine
array_count_values
array_diff
array_diff_assoc
array_diff_key
array_fill
array_fill_keys
array_flip
array_intersect
array_intersect_assoc
array_intersect_key
array_key_exists
array_keys
array_merge
array_merge_recursive
array_multisort
array_pad
array_pop
array_product
array_push
array_rand
array_replace
array_replace_recursive
array_reverse
array_search
array_shift
array_slice
array_splice
array_sum
array_unique
array_unshift
array_values
array_walk
array_walk_recursive
base_convert
bin2hex
bind_textdomain_codeset
bindtextdomain
blenc_encrypt
boolval
bzclose
bzcompress
bzerrno
bzerror
bzerrstr
bzflush
bzread
bzwrite
calcul_hmac
calculhmac
chdb_create
checkdnsrr
chgrp
chunk_split
class_alias
class_exists
class_implements
class_parents
class_uses
clearstatcache
cli_get_process_title
cli_set_process_title
com_create_guid
com_event_sink
com_get_active_object
com_load_typelib
com_message_pump
com_print_typeinfo
config_get_hash
connection_aborted
connection_status
convert_cyr_string
count_chars
crack_check
crack_closedict
crack_getlastmessage
crack_opendict
ctype_alnum
ctype_alpha
ctype_cntrl
ctype_digit
ctype_graph
ctype_lower
ctype_print
ctype_punct
ctype_space
ctype_upper
ctype_xdigit
curl_close
curl_copy_handle
curl_errno
curl_error
curl_escape
curl_getinfo
curl_multi_add_handle
curl_multi_close
curl_multi_exec
curl_multi_getcontent
curl_multi_info_read
curl_multi_init
curl_multi_remove_handle
curl_multi_select
curl_multi_setopt
curl_multi_strerror
curl_pause
curl_reset
curl_setopt
curl_setopt_array
curl_share_close
curl_share_init
curl_share_setopt
curl_strerror
curl_unescape
curl_version
date_add
date_create
date_create_from_format
date_create_immutable
date_create_immutable_from_format
date_date_set
date_default_timezone_get
date_default_timezone_set
date_diff
date_format
date_get_last_errors
date_interval_create_from_date_string
date_interval_format
date_isodate_set
date_modify
date_offset_get
date_parse
date_parse_from_format
date_sub
date_sun_info
date_sunrise
date_sunset
date_time_set
date_timestamp_get
date_timestamp_set
date_timezone_get
date_timezone_set
dcgettext
dcngettext
debug_print_backtrace
debug_zval_dump
decbin
dechex
define_syslog_variables
deg2rad
dgettext
disk_free_space
disk_total_space
dngettext
dns_check_record
dns_get_mx
dns_get_record
dom_import_simplexml
ereg_replace
eregi_replace
error_clear_last
error_get_last
expect_expectl
expect_popen
expm1
extension_loaded
ezmlm_hash
fastcgi_finish_request
fflush
fgetc
fgetcsv
fgetss
filter_has_var
filter_id
filter_input
filter_input_array
filter_list
filter_var
filter_var_array
finfo_close
fnmatch
foreach
forward_static_call
forward_static_call_array
fpassthru
fprintf
fputcsv
frenchtojd
fribidi_log2vis
fscanf
fseek
ftp_ssl_connect
ftruncate
func_get_arg
func_get_args
func_num_args
gc_collect_cycles
gc_disable
gc_enable
gc_enabled
gc_mem_caches
gd_info
get_browser
get_called_class
get_class
get_declared_classes
get_declared_interfaces
get_declared_traits
get_extension_funcs
get_headers
get_html_translation_table
get_include_path
get_included_files
get_loaded_extensions
get_magic_quotes_gpc
get_magic_quotes_runtime
get_object_vars
get_parent_class
get_required_files
get_resource_type
get_resources
getallheaders
gethostbyaddr
gethostbyname
gethostbynamel
gethostname
getimagesizefromstring
getmxrr
getopt
getprotobyname
getprotobynumber
getrandmax
getrusage
getservbyname
getservbyport
gettimeofday
gmmktime
gmstrftime
gopher_parsedir
gregoriantojd
gzclose
gzeof
gzgetc
gzgets
gzgetss
gzpassthru
gzputs
gzrewind
gzseek
gztell
hash_algos
hash_copy
hash_equals
hash_final
hash_hmac
hash_init
hash_pbkdf2
hash_update
hash_update_stream
header_remove
hebrevc
hexdec
highlight_string
http_build_query
http_response_code
iconv_get_encoding
iconv_mime_decode
iconv_mime_decode_headers
iconv_mime_encode
iconv_set_encoding
iconv_strlen
iconv_strpos
iconv_strrpos
iconv_substr
idn_to_ascii
idn_to_utf8
ignore_user_abort
image_type_to_extension
image_type_to_mime_type
import_request_variables
in_array
inet_ntop
inet_pton
ini_alter
ini_restore
interface_exists
intl_error_name
intl_get_error_code
intl_get_error_message
intl_is_failure
ip2long
iptcembed
iptcparse
is_subclass_of
is_uploaded_file
iterator_apply
iterator_count
iterator_to_array
jddayofweek
jdmonthname
jdtofrench
jdtogregorian
jdtojewish
jdtojulian
jdtounix
jewishtojd
jpeg2wbmp
json_last_error
json_last_error_msg
judy_type
judy_version
juliantojd
key_exists
krsort
lcg_value
lchgrp
lchown
libxml_clear_errors
libxml_disable_entity_loader
libxml_get_errors
libxml_get_last_error
libxml_set_external_entity_loader
libxml_set_streams_context
libxml_use_internal_errors
localeconv
long2ip
lzf_compress
lzf_decompress
lzf_optimized_for
magic_quotes_runtime
mb_check_encoding
mb_convert_case
mb_convert_encoding
mb_convert_kana
mb_convert_variables
mb_decode_mimeheader
mb_decode_numericentity
mb_detect_encoding
mb_detect_order
mb_encode_mimeheader
mb_encode_numericentity
mb_encoding_aliases
mb_ereg_search
mb_ereg_search_getpos
mb_ereg_search_getregs
mb_ereg_search_init
mb_ereg_search_pos
mb_ereg_search_regs
mb_ereg_search_setpos
mb_get_info
mb_http_input
mb_http_output
mb_internal_encoding
mb_language
mb_list_encodings
mb_output_handler
mb_preferred_mime_name
mb_regex_encoding
mb_regex_set_options
mb_send_mail
mb_split
mb_strcut
mb_strimwidth
mb_stripos
mb_stristr
mb_strlen
mb_strpos
mb_strrchr
mb_strrichr
mb_strripos
mb_strrpos
mb_strstr
mb_strtolower
mb_strtoupper
mb_strwidth
mb_substitute_character
mb_substr
mb_substr_count
mbereg_match
mbereg_replace
mbereg_search
mbereg_search_getpos
mbereg_search_getregs
mbereg_search_init
mbereg_search_pos
mbereg_search_regs
mbereg_search_setpos
mberegi
mberegi_replace
mbregex_encoding
mcrypt_cbc
mcrypt_cfb
mcrypt_create_iv
mcrypt_decrypt
mcrypt_ecb
mcrypt_enc_get_algorithms_name
mcrypt_enc_get_block_size
mcrypt_enc_get_iv_size
mcrypt_enc_get_key_size
mcrypt_enc_get_modes_name
mcrypt_enc_get_supported_key_sizes
mcrypt_enc_is_block_algorithm
mcrypt_enc_is_block_algorithm_mode
mcrypt_enc_is_block_mode
mcrypt_enc_self_test
mcrypt_encrypt
mcrypt_generic
mcrypt_generic_deinit
mcrypt_generic_end
mcrypt_generic_init
mcrypt_get_block_size
mcrypt_get_cipher_name
mcrypt_get_iv_size
mcrypt_get_key_size
mcrypt_list_algorithms
mcrypt_list_modes
mcrypt_module_close
mcrypt_module_get_algo_block_size
mcrypt_module_get_algo_key_size
mcrypt_module_get_supported_key_sizes
mcrypt_module_is_block_algorithm
mcrypt_module_is_block_algorithm_mode
mcrypt_module_is_block_mode
mcrypt_module_open
mcrypt_module_self_test
mcrypt_ofb
mdecrypt_generic
memcache_debug
memory_get_peak_usage
memory_get_usage
mhash_count
mhash_get_block_size
mhash_get_hash_name
mhash_keygen_s2k
mime_content_type
mktime
money_format
msg_get_queue
msg_queue_exists
msg_receive
msg_remove_queue
msg_send
msg_set_queue
msg_stat_queue
mssql_bind
mssql_close
mssql_connect
mssql_data_seek
mssql_execute
mssql_fetch_array
mssql_fetch_assoc
mssql_fetch_batch
mssql_fetch_field
mssql_fetch_object
mssql_fetch_row
mssql_field_length
mssql_field_name
mssql_field_seek
mssql_field_type
mssql_free_result
mssql_free_statement
mssql_get_last_message
mssql_guid_string
mssql_init
mssql_min_error_severity
mssql_min_message_severity
mssql_next_result
mssql_num_fields
mssql_num_rows
mssql_pconnect
mssql_query
mssql_result
mssql_rows_affected
mssql_select_db
mt_getrandmax
mt_rand
mt_srand
mysql_affected_rows
mysql_client_encoding
mysql_close
mysql_connect
mysql_create_db
mysql_createdb
mysql_data_seek
mysql_db_name
mysql_db_query
mysql_dbname
mysql_drop_db
mysql_dropdb
mysql_errno
mysql_error
mysql_escape_string
mysql_fetch_array
mysql_fetch_assoc
mysql_fetch_field
mysql_fetch_lengths
mysql_fetch_object
mysql_fetch_row
mysql_field_flags
mysql_field_len
mysql_field_name
mysql_field_seek
mysql_field_table
mysql_field_type
mysql_fieldflags
mysql_fieldlen
mysql_fieldname
mysql_fieldtable
mysql_fieldtype
mysql_free_result
mysql_freeresult
mysql_get_client_info
mysql_get_host_info
mysql_get_proto_info
mysql_get_server_info
mysql_info
mysql_insert_id
mysql_list_dbs
mysql_list_fields
mysql_list_processes
mysql_list_tables
mysql_listdbs
mysql_listfields
mysql_listtables
mysql_num_fields
mysql_num_rows
mysql_numfields
mysql_numrows
mysql_pconnect
mysql_ping
mysql_real_escape_string
mysql_result
mysql_select_db
mysql_selectdb
mysql_set_charset
mysql_stat
mysql_table_name
mysql_tablename
mysql_thread_id
mysql_unbuffered_query
mysqli_bind_param
mysqli_bind_result
mysqli_client_encoding
mysqli_connect
mysqli_disable_rpl_parse
mysqli_enable_reads_from_master
mysqli_enable_rpl_parse
mysqli_escape_string
mysqli_execute
mysqli_fetch
mysqli_get_cache_stats
mysqli_get_client_stats
mysqli_get_client_version
mysqli_get_links_stats
mysqli_get_metadata
mysqli_master_query
mysqli_param_count
mysqli_report
mysqli_rpl_parse_enabled
mysqli_rpl_probe
mysqli_send_long_data
mysqli_slave_query
mysqlnd_memcache_get_config
mysqlnd_memcache_set
mysqlnd_ms_dump_servers
mysqlnd_ms_fabric_select_global
mysqlnd_ms_fabric_select_shard
mysqlnd_ms_get_last_gtid
mysqlnd_ms_get_last_used_connection
mysqlnd_ms_get_stats
mysqlnd_ms_match_wild
mysqlnd_ms_query_is_select
mysqlnd_ms_set_qos
mysqlnd_ms_set_user_pick_server
mysqlnd_ms_xa_begin
mysqlnd_ms_xa_commit
mysqlnd_ms_xa_gc
mysqlnd_ms_xa_rollback
mysqlnd_qc_clear_cache
mysqlnd_qc_get_available_handlers
mysqlnd_qc_get_cache_info
mysqlnd_qc_get_core_stats
mysqlnd_qc_get_normalized_query_trace_log
mysqlnd_qc_get_query_trace_log
mysqlnd_qc_set_cache_condition
mysqlnd_qc_set_is_select
mysqlnd_qc_set_storage_handler
mysqlnd_qc_set_user_handlers
mysqlnd_uh_convert_to_mysqlnd
mysqlnd_uh_set_connection_proxy
mysqlnd_uh_set_statement_proxy
natcasesort
ngettext
nl2br
nl_langinfo
nsapi_request_headers
nsapi_response_headers
nsapi_virtual
nthmac
number_format
oauth_get_sbs
oauth_urlencode
ob_get_length
ob_get_level
ob_get_status
ob_gzhandler
ob_iconv_handler
ob_implicit_flush
ob_list_handlers
ob_tidyhandler
odbc_autocommit
odbc_binmode
odbc_close
odbc_close_all
odbc_columnprivileges
odbc_columns
odbc_commit
odbc_cursor
odbc_data_source
odbc_do
odbc_error
odbc_errormsg
odbc_fetch_array
odbc_fetch_into
odbc_fetch_object
odbc_fetch_row
odbc_field_len
odbc_field_name
odbc_field_num
odbc_field_precision
odbc_field_scale
odbc_field_type
odbc_foreignkeys
odbc_free_result
odbc_gettypeinfo
odbc_longreadlen
odbc_next_result
odbc_num_fields
odbc_num_rows
odbc_pconnect
odbc_prepare
odbc_primarykeys
odbc_procedurecolumns
odbc_procedures
odbc_rollback
odbc_setoption
odbc_specialcolumns
odbc_statistics
odbc_tableprivileges
odbc_tables
opcache_compile_file
opcache_get_configuration
opcache_get_status
opcache_invalidate
opcache_is_script_cached
opcache_reset
openssl_cipher_iv_length
openssl_csr_export
openssl_csr_export_to_file
openssl_csr_get_public_key
openssl_csr_get_subject
openssl_csr_new
openssl_csr_sign
openssl_decrypt
openssl_dh_compute_key
openssl_digest
openssl_encrypt
openssl_error_string
openssl_free_key
openssl_get_cert_locations
openssl_get_cipher_methods
openssl_get_md_methods
openssl_get_privatekey
openssl_get_publickey
openssl_open
openssl_pbkdf2
openssl_pkcs12_export
openssl_pkcs12_export_to_file
openssl_pkcs12_read
openssl_pkcs7_decrypt
openssl_pkcs7_encrypt
openssl_pkcs7_sign
openssl_pkcs7_verify
openssl_pkey_export
openssl_pkey_export_to_file
openssl_pkey_free
openssl_pkey_get_details
openssl_pkey_get_private
openssl_pkey_get_public
openssl_pkey_new
openssl_private_decrypt
openssl_private_encrypt
openssl_public_decrypt
openssl_public_encrypt
openssl_random_pseudo_bytes
openssl_seal
openssl_sign
openssl_spki_export
openssl_spki_export_challenge
openssl_spki_new
openssl_spki_verify
openssl_verify
openssl_x509_check_private_key
openssl_x509_checkpurpose
openssl_x509_export
openssl_x509_export_to_file
openssl_x509_fingerprint
openssl_x509_free
openssl_x509_parse
openssl_x509_read
output_add_rewrite_var
output_reset_rewrite_vars
override_function
parse_ini_string
parse_url
parsekit_compile_file
parsekit_compile_string
parsekit_func_arginfo
password_get_info
password_hash
password_needs_rehash
password_verify
pcntl_alarm
pcntl_errno
pcntl_get_last_error
pcntl_getpriority
pcntl_setpriority
pcntl_signal
pcntl_signal_dispatch
pcntl_sigprocmask
pcntl_sigtimedwait
pcntl_sigwaitinfo
pcntl_strerror
pcntl_wait
pcntl_waitpid
pcntl_wexitstatus
pcntl_wifexited
pcntl_wifsignaled
pcntl_wifstopped
pcntl_wstopsig
pcntl_wtermsig
pg_affected_rows
pg_cancel_query
pg_client_encoding
pg_close
pg_connect_poll
pg_connection_busy
pg_connection_reset
pg_connection_status
pg_consume_input
pg_convert
pg_copy_from
pg_copy_to
pg_dbname
pg_delete
pg_end_copy
pg_escape_bytea
pg_escape_identifier
pg_escape_literal
pg_escape_string
pg_fetch_all
pg_fetch_all_columns
pg_fetch_array
pg_fetch_assoc
pg_fetch_object
pg_fetch_result
pg_fetch_row
pg_field_is_null
pg_field_name
pg_field_num
pg_field_prtlen
pg_field_size
pg_field_table
pg_field_type
pg_field_type_oid
pg_flush
pg_free_result
pg_get_notify
pg_get_pid
pg_get_result
pg_host
pg_insert
pg_last_error
pg_last_notice
pg_last_oid
pg_lo_close
pg_lo_create
pg_lo_export
pg_lo_import
pg_lo_open
pg_lo_read
pg_lo_read_all
pg_lo_seek
pg_lo_tell
pg_lo_truncate
pg_lo_unlink
pg_lo_write
pg_meta_data
pg_num_fields
pg_num_rows
pg_options
pg_parameter_status
pg_pconnect
pg_ping
pg_port
pg_put_line
pg_query_params
pg_result_error
pg_result_error_field
pg_result_seek
pg_result_status
pg_select
pg_send_execute
pg_send_prepare
pg_send_query
pg_send_query_params
pg_set_client_encoding
pg_set_error_verbosity
pg_socket
pg_trace
pg_transaction_status
pg_tty
pg_unescape_bytea
pg_untrace
pg_update
pg_version
php_check_syntax
php_ini_loaded_file
php_ini_scanned_files
php_logo_guid
php_sapi_name
phpcredits
png2wbmp
posix_access
posix_ctermid
posix_errno
posix_get_last_error
posix_getgrgid
posix_getgrnam
posix_getgroups
posix_getpgid
posix_getpgrp
posix_getpid
posix_getppid
posix_getrlimit
posix_getsid
posix_initgroups
posix_isatty
posix_setegid
posix_seteuid
posix_setgid
posix_setpgid
posix_setrlimit
posix_setsid
posix_setuid
posix_strerror
posix_times
preg_filter
preg_grep
preg_last_error
preg_quote
property_exists
quoted_printable_decode
quoted_printable_encode
rad2deg
random_bytes
random_int
rar_wrapper_cache_stats
readline_add_history
readline_callback_handler_install
readline_callback_handler_remove
readline_callback_read_char
readline_clear_history
readline_completion_function
readline_info
readline_list_history
readline_on_new_line
readline_read_history
readline_redisplay
readline_write_history
realpath
realpath_cache_get
realpath_cache_size
recode_file
recode_string
restore_error_handler
restore_exception_handler
restore_include_path
rewinddir
rmdir
rpm_close
rpm_get_tag
rpm_is_valid
rpm_open
rpm_version
rrdc_disconnect
runkit_class_adopt
runkit_class_emancipate
runkit_constant_remove
runkit_function_remove
runkit_import
runkit_lint
runkit_lint_file
runkit_method_remove
runkit_return_value_used
runkit_sandbox_output_handler
runkit_superglobals
sem_acquire
sem_get
sem_release
sem_remove
session_abort
session_cache_expire
session_cache_limiter
session_commit
session_decode
session_destroy
session_encode
session_get_cookie_params
session_id
session_is_registered
session_module_name
session_name
session_pgsql_add_error
session_pgsql_get_error
session_pgsql_get_field
session_pgsql_reset
session_pgsql_set_field
session_pgsql_status
session_regenerate_id
session_register
session_register_shutdown
session_reset
session_save_path
session_set_cookie_params
session_status
session_unregister
session_unset
session_write_close
set_file_buffer
set_socket_blocking
set_time_limit
setcookie
setlocale
setproctitle
setrawcookie
setthreadtitle
shm_attach
shm_detach
shm_get_var
shm_has_var
shm_put_var
shm_remove
shm_remove_var
shmop_close
shmop_delete
shmop_open
shmop_read
shmop_size
shmop_write
simplexml_import_dom
socket_accept
socket_bind
socket_clear_error
socket_close
socket_cmsg_space
socket_create_listen
socket_create_pair
socket_get_option
socket_get_status
socket_getopt
socket_getpeername
socket_getsockname
socket_import_stream
socket_last_error
socket_listen
socket_read
socket_recv
socket_recvfrom
socket_recvmsg
socket_select
socket_send
socket_sendmsg
socket_sendto
socket_set_block
socket_set_blocking
socket_set_nonblock
socket_set_option
socket_set_timeout
socket_setopt
socket_shutdown
socket_strerror
socket_write
solr_get_version
spl_autoload
spl_autoload_call
spl_autoload_extensions
spl_autoload_functions
spl_autoload_register
spl_autoload_unregister
spl_classes
spl_object_hash
sql_regcase
sqlite_busy_timeout
sqlite_changes
sqlite_close
sqlite_column
sqlite_current
sqlite_error_string
sqlite_escape_string
sqlite_factory
sqlite_fetch_all
sqlite_fetch_array
sqlite_fetch_column_types
sqlite_fetch_object
sqlite_fetch_single
sqlite_fetch_string
sqlite_field_name
sqlite_has_more
sqlite_has_prev
sqlite_key
sqlite_last_error
sqlite_last_insert_rowid
sqlite_libencoding
sqlite_libversion
sqlite_next
sqlite_num_fields
sqlite_num_rows
sqlite_prev
sqlite_rewind
sqlite_seek
sqlite_udf_decode_binary
sqlite_udf_encode_binary
sqlite_valid
sqlsrv_begin_transaction
sqlsrv_cancel
sqlsrv_client_info
sqlsrv_close
sqlsrv_commit
sqlsrv_configure
sqlsrv_connect
sqlsrv_errors
sqlsrv_execute
sqlsrv_fetch
sqlsrv_fetch_array
sqlsrv_fetch_object
sqlsrv_field_metadata
sqlsrv_free_stmt
sqlsrv_get_config
sqlsrv_get_field
sqlsrv_has_rows
sqlsrv_next_result
sqlsrv_num_fields
sqlsrv_num_rows
sqlsrv_prepare
sqlsrv_query
sqlsrv_rollback
sqlsrv_rows_affected
sqlsrv_send_stream_data
sqlsrv_server_info
sscanf
ssdeep_fuzzy_compare
ssdeep_fuzzy_hash
ssdeep_fuzzy_hash_filename
stomp_connect_error
stomp_version
str_getcsv
str_ireplace
str_pad
str_repeat
str_replace
str_shuffle
str_split
str_word_count
strcasecmp
strchr
strcmp
strcspn
stream_bucket_append
stream_bucket_make_writeable
stream_bucket_new
stream_bucket_prepend
stream_context_get_default
stream_context_get_options
stream_context_get_params
stream_context_set_default
stream_context_set_option
stream_context_set_params
stream_copy_to_stream
stream_encoding
stream_filter_append
stream_filter_prepend
stream_filter_register
stream_filter_remove
stream_get_contents
stream_get_filters
stream_get_line
stream_get_meta_data
stream_get_transports
stream_get_wrappers
stream_is_local
stream_notification_callback
stream_register_wrapper
stream_resolve_include_path
stream_select
stream_set_blocking
stream_set_chunk_size
stream_set_read_buffer
stream_set_timeout
stream_set_write_buffer
stream_socket_accept
stream_socket_enable_crypto
stream_socket_get_name
stream_socket_pair
stream_socket_recvfrom
stream_socket_sendto
stream_socket_server
stream_socket_shutdown
stream_supports_lock
stream_wrapper_register
stream_wrapper_restore
stream_wrapper_unregister
strftime
strip_tags
stripos
stristr
strnatcasecmp
strnatcmp
strncasecmp
strncmp
strpbrk
strpos
strptime
strrchr
strripos
strrpos
strstr
strtok
strtolower
strtotime
strtoupper
strtr
strval
substr_compare
substr_count
substr_replace
sys_getloadavg
tcpwrap_check
time_nanosleep
time_sleep_until
timezone_abbreviations_list
timezone_identifiers_list
timezone_location_get
timezone_name_from_abbr
timezone_name_get
timezone_offset_get
timezone_open
timezone_transitions_get
timezone_version_get
token_get_all
token_name
trait_exists
trigger_error
ucwords
unixtojd
unregister_tick_function
use_soap_error_handler
user_error
utf8_decode
utf8_encode
var_export
version_compare
vfprintf
vprintf
vsprintf
win32_continue_service
win32_create_service
win32_delete_service
win32_get_last_control_message
win32_pause_service
win32_ps_list_procs
win32_ps_stat_mem
win32_ps_stat_proc
win32_query_service_status
win32_set_service_status
win32_start_service
win32_start_service_ctrl_dispatcher
win32_stop_service
xattr_get
xattr_list
xattr_remove
xattr_set
xattr_supported
xml_error_string
xml_get_current_byte_index
xml_get_current_column_number
xml_get_current_line_number
xml_get_error_code
xml_parse
xml_parse_into_struct
xml_parser_create
xml_parser_create_ns
xml_parser_free
xml_parser_get_option
xml_parser_set_option
xml_set_character_data_handler
xml_set_default_handler
xml_set_element_handler
xml_set_end_namespace_decl_handler
xml_set_external_entity_ref_handler
xml_set_notation_decl_handler
xml_set_object
xml_set_processing_instruction_handler
xml_set_start_namespace_decl_handler
xml_set_unparsed_entity_decl_handler
xmlrpc_decode
xmlrpc_decode_request
xmlrpc_encode
xmlrpc_encode_request
xmlrpc_get_type
xmlrpc_is_fault
xmlrpc_parse_method_descriptions
xmlrpc_server_add_introspection_data
xmlrpc_server_call_method
xmlrpc_server_create
xmlrpc_server_destroy
xmlrpc_server_register_introspection_callback
xmlrpc_server_register_method
xmlrpc_set_type
yaml_emit
yaml_emit_file
yaml_parse
yaml_parse_file
yaml_parse_url
zend_logo_guid
zend_thread_id
zend_version
zip_close
zip_entry_close
zip_entry_compressedsize
zip_entry_compressionmethod
zip_entry_filesize
zip_entry_name
zip_entry_open
zip_entry_read
zip_open
zip_read
zlib_encode
zlib_get_coding_type
