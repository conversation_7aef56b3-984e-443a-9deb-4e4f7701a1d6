###
# ------------------------------------------------------------------------
# OWASP ModSecurity Core Rule Set ver.3.2.0
# Copyright (c) 2006-2019 Trustwave and contributors. All rights reserved.
#
# The OWASP ModSecurity Core Rule Set is distributed under
# Apache Software License (ASL) version 2
# Please see the enclosed LICENSE file for full details.
# ------------------------------------------------------------------------

# These exclusions remedy false positives in a default cPanel environment.
# The exclusions are only active if crs_exclusions_cpanel=1 is set.
# See rule 900130 in crs-setup.conf.example for instructions.


SecRule &TX:crs_exclusions_cpanel|TX:crs_exclusions_cpanel "@eq 0" \
    "id:9005000,\
    phase:1,\
    pass,\
    t:none,\
    nolog,\
    skipAfter:END-CPANEL"

SecRule &TX:crs_exclusions_cpanel|TX:crs_exclusions_cpanel "@eq 0" \
    "id:9005001,\
    phase:2,\
    pass,\
    t:none,\
    nolog,\
    skipAfter:END-CPANEL"


#
# [ cPanel whm-server-status ]
#
# Cpanel's WHM auto generates requests to /whm-server-status from
# 127.0.0.1 (triggers rule 920280, non-blocking, log only) Once every 5 minutes.
# These false positives have a low impact (logged, non-blocking) to a large number of users (all cPanel admins).
#

#
# Rule to allow cPanel whm-server-status requests from localhost without log entry.
#
SecRule REQUEST_LINE "@rx ^GET /whm-server-status(?:/|/\?auto)? HTTP/[12]\.[01]$" \
    "id:9005100,\
    phase:1,\
    pass,\
    t:none,\
    nolog,\
    tag:'application-multi',\
    tag:'language-multi',\
    tag:'platform-apache',\
    tag:'attack-generic',\
    chain"
    SecRule REMOTE_ADDR "@ipMatch 127.0.0.1,::1" \
        "t:none,\
        ctl:ruleRemoveById=920280,\
        ctl:ruleRemoveById=920350"


SecMarker "END-CPANEL"
