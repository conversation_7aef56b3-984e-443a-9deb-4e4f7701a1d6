###
# ------------------------------------------------------------------------
# OWASP ModSecurity Core Rule Set ver.3.2.0
# Copyright (c) 2006-2019 Trustwave and contributors. All rights reserved.
#
# The OWASP ModSecurity Core Rule Set is distributed under
# Apache Software License (ASL) version 2
# Please see the enclosed LICENSE file for full details.
# ------------------------------------------------------------------------

# You should set the score to the proper threshold you would prefer. If kept at "@gt 0"
# it will work similarly to previous Mod CRS rules and will create an event in the error_log
# file if there are any rules that match.  If you would like to lessen the number of events
# generated in the error_log file, you should increase the anomaly score threshold to
# something like "@gt 20".  This would only generate an event in the error_log file if
# there are multiple lower severity rule matches or if any 1 higher severity item matches.
#
# You should also set the desired disruptive action (deny, redirect, etc...).
#

#
# -= Paranoia Level 0 (empty) =- (apply unconditionally)
#

# Summing up the anomaly score.

# NOTE: tx.anomaly_score should not be set initially, but masking would lead to difficult bugs.
# So we add to it.
SecRule TX:PARANOIA_LEVEL "@ge 1" \
    "id:959060,\
    phase:4,\
    pass,\
    t:none,\
    nolog,\
    setvar:'tx.outbound_anomaly_score=+%{tx.outbound_anomaly_score_pl1}'"

SecRule TX:PARANOIA_LEVEL "@ge 2" \
    "id:959061,\
    phase:4,\
    pass,\
    t:none,\
    nolog,\
    setvar:'tx.outbound_anomaly_score=+%{tx.outbound_anomaly_score_pl2}'"

SecRule TX:PARANOIA_LEVEL "@ge 3" \
    "id:959062,\
    phase:4,\
    pass,\
    t:none,\
    nolog,\
    setvar:'tx.outbound_anomaly_score=+%{tx.outbound_anomaly_score_pl3}'"

SecRule TX:PARANOIA_LEVEL "@ge 4" \
    "id:959063,\
    phase:4,\
    pass,\
    t:none,\
    nolog,\
    setvar:'tx.outbound_anomaly_score=+%{tx.outbound_anomaly_score_pl4}'"


# Alert and Block on High Anomaly Scores - this would block outbound data leakages
#
# Note: This rule also sets the 'tx.anomaly_score' variable.
# That variable name was formerly used in CRS, but not any longer.
# However, Jwall AuditConsole depends on this exact variable name.
# Without setting it, the 'Outbound Score' in the AuditConsole GUI would always be 0.

#SecRule TX:OUTBOUND_ANOMALY_SCORE "@ge %{tx.outbound_anomaly_score_threshold}" \
#    "id:959100,\
#    phase:4,\
#    deny,\
#    t:none,\
#    msg:'超出出站异常分数（总分数：%{TX.OUTBOUND_ANOMALY_SCORE}',\
#    tag:'anomaly-evaluation',\
#    setvar:'tx.anomaly_score=+%{tx.outbound_anomaly_score}'"



SecRule TX:EXECUTING_PARANOIA_LEVEL "@lt 1" "id:959011,phase:3,pass,nolog,skipAfter:END-RESPONSE-959-BLOCKING-EVALUATION"
SecRule TX:EXECUTING_PARANOIA_LEVEL "@lt 1" "id:959012,phase:4,pass,nolog,skipAfter:END-RESPONSE-959-BLOCKING-EVALUATION"
#
# -= Paranoia Level 1 (default) =- (apply only when tx.executing_paranoia_level is sufficiently high: 1 or higher)
#



SecRule TX:EXECUTING_PARANOIA_LEVEL "@lt 2" "id:959013,phase:3,pass,nolog,skipAfter:END-RESPONSE-959-BLOCKING-EVALUATION"
SecRule TX:EXECUTING_PARANOIA_LEVEL "@lt 2" "id:959014,phase:4,pass,nolog,skipAfter:END-RESPONSE-959-BLOCKING-EVALUATION"
#
# -= Paranoia Level 2 =- (apply only when tx.executing_paranoia_level is sufficiently high: 2 or higher)
#



SecRule TX:EXECUTING_PARANOIA_LEVEL "@lt 3" "id:959015,phase:3,pass,nolog,skipAfter:END-RESPONSE-959-BLOCKING-EVALUATION"
SecRule TX:EXECUTING_PARANOIA_LEVEL "@lt 3" "id:959016,phase:4,pass,nolog,skipAfter:END-RESPONSE-959-BLOCKING-EVALUATION"
#
# -= Paranoia Level 3 =- (apply only when tx.executing_paranoia_level is sufficiently high: 3 or higher)
#



SecRule TX:EXECUTING_PARANOIA_LEVEL "@lt 4" "id:959017,phase:3,pass,nolog,skipAfter:END-RESPONSE-959-BLOCKING-EVALUATION"
SecRule TX:EXECUTING_PARANOIA_LEVEL "@lt 4" "id:959018,phase:4,pass,nolog,skipAfter:END-RESPONSE-959-BLOCKING-EVALUATION"
#
# -= Paranoia Level 4 =- (apply only when tx.executing_paranoia_level is sufficiently high: 4 or higher)
#



#
# -= Paranoia Levels Finished =-
#
SecMarker "END-RESPONSE-959-BLOCKING-EVALUATION"
