###
# ------------------------------------------------------------------------
# OWASP ModSecurity Core Rule Set ver.3.2.0
# Copyright (c) 2006-2019 Trustwave and contributors. All rights reserved.
#
# The OWASP ModSecurity Core Rule Set is distributed under
# Apache Software License (ASL) version 2
# Please see the enclosed LICENSE file for full details.
# ------------------------------------------------------------------------

#
# -= Paranoia Level 0 (empty) =- (apply unconditionally)
#

# Summing up the anomaly score.

# NOTE: tx.anomaly_score should not be set initially, but masking would lead to difficult bugs.
# So we add to it.
SecRule TX:PARANOIA_LEVEL "@ge 1" \
    "id:949060,\
    phase:2,\
    pass,\
    t:none,\
    nolog,\
    setvar:'tx.anomaly_score=+%{tx.anomaly_score_pl1}'"

SecRule TX:PARANOIA_LEVEL "@ge 2" \
    "id:949061,\
    phase:2,\
    pass,\
    t:none,\
    nolog,\
    setvar:'tx.anomaly_score=+%{tx.anomaly_score_pl2}'"

SecRule TX:PARANOIA_LEVEL "@ge 3" \
    "id:949062,\
    phase:2,\
    pass,\
    t:none,\
    nolog,\
    setvar:'tx.anomaly_score=+%{tx.anomaly_score_pl3}'"

SecRule TX:PARANOIA_LEVEL "@ge 4" \
    "id:949063,\
    phase:2,\
    pass,\
    t:none,\
    nolog,\
    setvar:'tx.anomaly_score=+%{tx.anomaly_score_pl4}'"


SecMarker "BEGIN-REQUEST-BLOCKING-EVAL"

# These rules use the anomaly score settings specified in the 10 config file.
# You should also set the desired disruptive action (deny, redirect, etc...).
#
# -=[ IP Reputation Checks ]=-
#
# Block based on variable IP.REPUT_BLOCK_FLAG and TX.DO_REPUT_BLOCK
#
SecRule IP:REPUT_BLOCK_FLAG "@eq 1" \
    "id:949100,\
    phase:2,\
    deny,\
    log,\
    msg:'IP声誉执法拒绝请求。',\
    logdata:'Previous Block Reason: %{ip.reput_block_reason}',\
    tag:'application-multi',\
    tag:'language-multi',\
    tag:'platform-multi',\
    tag:'attack-reputation-ip',\
    severity:'CRITICAL',\
    chain"
    SecRule TX:DO_REPUT_BLOCK "@eq 1" \
        "setvar:'tx.inbound_anomaly_score=%{tx.anomaly_score}'"

#
# -=[ Anomaly Mode: Overall Transaction Anomaly Score ]=-
#
#SecRule TX:ANOMALY_SCORE "@ge %{tx.inbound_anomaly_score_threshold}" \
#    "id:949110,\
#    phase:2,\
#    deny,\
#    t:none,\
#    log,\
#    msg:'Inbound Anomaly Score Exceeded (Total Score: %{TX.ANOMALY_SCORE})',\
#    tag:'application-multi',\
#    tag:'language-multi',\
#    tag:'platform-multi',\
#    tag:'attack-generic',\
#    severity:'CRITICAL',\
#    setvar:'tx.inbound_anomaly_score=%{tx.anomaly_score}'"



SecRule TX:EXECUTING_PARANOIA_LEVEL "@lt 1" "id:949011,phase:1,pass,nolog,skipAfter:END-REQUEST-949-BLOCKING-EVALUATION"
SecRule TX:EXECUTING_PARANOIA_LEVEL "@lt 1" "id:949012,phase:2,pass,nolog,skipAfter:END-REQUEST-949-BLOCKING-EVALUATION"
#
# -= Paranoia Level 1 (default) =- (apply only when tx.executing_paranoia_level is sufficiently high: 1 or higher)
#



SecRule TX:EXECUTING_PARANOIA_LEVEL "@lt 2" "id:949013,phase:1,pass,nolog,skipAfter:END-REQUEST-949-BLOCKING-EVALUATION"
SecRule TX:EXECUTING_PARANOIA_LEVEL "@lt 2" "id:949014,phase:2,pass,nolog,skipAfter:END-REQUEST-949-BLOCKING-EVALUATION"
#
# -= Paranoia Level 2 =- (apply only when tx.executing_paranoia_level is sufficiently high: 2 or higher)
#



SecRule TX:EXECUTING_PARANOIA_LEVEL "@lt 3" "id:949015,phase:1,pass,nolog,skipAfter:END-REQUEST-949-BLOCKING-EVALUATION"
SecRule TX:EXECUTING_PARANOIA_LEVEL "@lt 3" "id:949016,phase:2,pass,nolog,skipAfter:END-REQUEST-949-BLOCKING-EVALUATION"
#
# -= Paranoia Level 3 =- (apply only when tx.executing_paranoia_level is sufficiently high: 3 or higher)
#



SecRule TX:EXECUTING_PARANOIA_LEVEL "@lt 4" "id:949017,phase:1,pass,nolog,skipAfter:END-REQUEST-949-BLOCKING-EVALUATION"
SecRule TX:EXECUTING_PARANOIA_LEVEL "@lt 4" "id:949018,phase:2,pass,nolog,skipAfter:END-REQUEST-949-BLOCKING-EVALUATION"
#
# -= Paranoia Level 4 =- (apply only when tx.executing_paranoia_level is sufficiently high: 4 or higher)
#



#
# -= Paranoia Levels Finished =-
#
SecMarker "END-REQUEST-949-BLOCKING-EVALUATION"
