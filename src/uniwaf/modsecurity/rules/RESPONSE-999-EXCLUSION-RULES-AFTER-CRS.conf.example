# ------------------------------------------------------------------------
# OWASP ModSecurity Core Rule Set ver.3.2.0
# Copyright (c) 2006-2019 Trustwave and contributors. All rights reserved.
#
# The OWASP ModSecurity Core Rule Set is distributed under
# Apache Software License (ASL) version 2
# Please see the enclosed LICENSE file for full details.
# ------------------------------------------------------------------------

#
# The purpose of this file is to hold LOCAL exceptions for your site.
# The types of rules that would go into this file are one where you want
# to unconditionally disable rules or modify their actions during startup.
#
# Please see the file REQUEST-900-EXCLUSION-RULES-BEFORE-CRS.conf.example
# for a description of the rule exclusions mechanism and the correct
# use of this file.
#

#
# Example Exclusion Rule: To unconditionally disable a rule ID
#
# ModSecurity Rule Exclusion: 942100 SQL Injection Detected via libinjection
# SecRuleRemoveById 942100

# Example Exclusion Rule: Remove a group of rules
#
# ModSecurity Rule Exclusion: Disable PHP injection rules
# SecRuleRemoveByTag "attack-injection-php"

#
# Example Exclusion Rule: To unconditionally remove parameter "foo" from
#                         inspection for SQLi rules
#
# ModSecurity Rule Exclusion: disable sqli rules for parameter foo.
# SecRuleUpdateTargetByTag "attack-sqli" "!ARGS:foo"


# -- [[ Changing the Disruptive Action for Anomaly Mode ]] --
#
# In Anomaly Mode (default in CRS3), the rules in REQUEST-949-BLOCKING-EVALUATION.conf
# and RESPONSE-959-BLOCKING-EVALUATION.conf check the accumulated attack scores
# against your policy. To apply a disruptive action, they overwrite the default
# actions specified in SecDefaultAction (setup.conf) with a 'deny' action.
# This 'deny' is by default paired with a 'status:403' action.
#
# In order to change the disruptive action from 'deny' to something else,
# you must use SecRuleUpdateActionByID directives AFTER the CRS rules
# are configured, for instance in the RESPONSE-999-EXCLUSION-RULES-AFTER-CRS.conf file.
#
# These actions only apply when using Anomaly Mode.
#
# Default action: block with error 403
# (No configuration needed in this file if you want the default behavior.)
#

# Example: redirect back to the homepage on blocking
#
# SecRuleUpdateActionById 949110 "t:none,redirect:'http://%{request_headers.host}/'"
# SecRuleUpdateActionById 959100 "t:none,redirect:'http://%{request_headers.host}/'"

# Example: redirect to another URL on blocking
#
# SecRuleUpdateActionById 949110 "t:none,redirect:'http://example.com/report_problem'"
# SecRuleUpdateActionById 959100 "t:none,redirect:'http://example.com/report_problem'"

# Example: send an error 404
#
# SecRuleUpdateActionById 949110 "t:none,deny,status:404"
# SecRuleUpdateActionById 959100 "t:none,deny,status:404"

# Example: drop the connection (best for DoS attacks)
#
# SecRuleUpdateActionById 949110 "t:none,drop"
# SecRuleUpdateActionById 959100 "t:none,drop"
