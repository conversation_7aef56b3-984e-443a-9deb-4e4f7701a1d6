###
# ------------------------------------------------------------------------
# OWASP ModSecurity Core Rule Set ver.3.2.0
# Copyright (c) 2006-2019 Trustwave and contributors. All rights reserved.
#
# The OWASP ModSecurity Core Rule Set is distributed under
# Apache Software License (ASL) version 2
# Please see the enclosed LICENSE file for full details.
# ------------------------------------------------------------------------

#
# -= Paranoia Level 0 (empty) =- (apply unconditionally)
#



SecRule TX:EXECUTING_PARANOIA_LEVEL "@lt 1" "id:941011,phase:1,pass,nolog,skipAfter:END-REQUEST-941-APPLICATION-ATTACK-XSS"
SecRule TX:EXECUTING_PARANOIA_LEVEL "@lt 1" "id:941012,phase:2,pass,nolog,skipAfter:END-REQUEST-941-APPLICATION-ATTACK-XSS"
#
# -= Paranoia Level 1 (default) =- (apply only when tx.executing_paranoia_level is sufficiently high: 1 or higher)
#


#
# -=[ Libinjection - XSS Detection ]=-
#
# Ref: https://github.com/client9/libinjection
# Ref: https://speakerdeck.com/ngalbreath/libinjection-from-sqli-to-xss
#
# -=[ Targets ]=-
#
# 941100: PL1 : REQUEST_COOKIES|!REQUEST_COOKIES:/__utm/|
#               REQUEST_COOKIES_NAMES|REQUEST_HEADERS:User-Agent|
#               ARGS_NAMES|ARGS|XML:/*
#
# 941101: PL2 : REQUEST_HEADERS:Referer
#
SecRule REQUEST_COOKIES|!REQUEST_COOKIES:/__utm/|REQUEST_COOKIES_NAMES|REQUEST_HEADERS:User-Agent|ARGS_NAMES|ARGS|XML:/* "@detectXSS" \
    "id:941100,\
    phase:2,\
    block,\
    t:none,t:utf8toUnicode,t:urlDecodeUni,t:htmlEntityDecode,t:jsDecode,t:cssDecode,t:removeNulls,\
    msg:'通过libinjection检测到XSS攻击',\
    logdata:'Matched Data: XSS data found within %{MATCHED_VAR_NAME}: %{MATCHED_VAR}',\
    tag:'application-multi',\
    tag:'language-multi',\
    tag:'platform-multi',\
    tag:'attack-xss',\
    tag:'OWASP_CRS',\
    tag:'OWASP_CRS/WEB_ATTACK/XSS',\
    tag:'WASCTC/WASC-8',\
    tag:'WASCTC/WASC-22',\
    tag:'OWASP_TOP_10/A3',\
    tag:'OWASP_AppSensor/IE1',\
    tag:'CAPEC-242',\
    ctl:auditLogParts=+E,\
    ver:'OWASP_CRS/3.2.0',\
    severity:'CRITICAL',\
    setvar:'tx.xss_score=+%{tx.critical_anomaly_score}',\
    setvar:'tx.anomaly_score_pl1=+%{tx.critical_anomaly_score}'"


#
# -=[ XSS Filters - Category 1 ]=-
# http://xssplayground.net23.net/xssfilter.html
# script tag based XSS vectors, e.g., <script> alert(1)</script>
#
SecRule REQUEST_COOKIES|!REQUEST_COOKIES:/__utm/|REQUEST_COOKIES_NAMES|REQUEST_FILENAME|REQUEST_HEADERS:User-Agent|REQUEST_HEADERS:Referer|ARGS_NAMES|ARGS|XML:/* "@rx (?i)<script[^>]*>[\s\S]*?" \
    "id:941110,\
    phase:2,\
    block,\
    capture,\
    t:none,t:utf8toUnicode,t:urlDecodeUni,t:htmlEntityDecode,t:jsDecode,t:cssDecode,t:removeNulls,\
    msg:'XSS过滤器-类别1:脚本标签向量',\
    logdata:'Matched Data: %{TX.0} found within %{MATCHED_VAR_NAME}: %{MATCHED_VAR}',\
    tag:'application-multi',\
    tag:'language-multi',\
    tag:'platform-multi',\
    tag:'attack-xss',\
    tag:'OWASP_CRS',\
    tag:'OWASP_CRS/WEB_ATTACK/XSS',\
    tag:'WASCTC/WASC-8',\
    tag:'WASCTC/WASC-22',\
    tag:'OWASP_TOP_10/A3',\
    tag:'OWASP_AppSensor/IE1',\
    tag:'CAPEC-242',\
    ctl:auditLogParts=+E,\
    ver:'OWASP_CRS/3.2.0',\
    severity:'CRITICAL',\
    setvar:'tx.xss_score=+%{tx.critical_anomaly_score}',\
    setvar:'tx.anomaly_score_pl1=+%{tx.critical_anomaly_score}'"


#
# -=[ XSS Filters - Category 2 ]=-
# XSS vectors making use of event handlers like onerror, onload etc, e.g., <body onload="alert(1)">
#
SecRule REQUEST_COOKIES|!REQUEST_COOKIES:/__utm/|REQUEST_COOKIES_NAMES|REQUEST_HEADERS:User-Agent|REQUEST_HEADERS:Referer|ARGS_NAMES|ARGS|XML:/* "@rx (?i)[\s\"'`;\/0-9=\x0B\x09\x0C\x3B\x2C\x28\x3B]+on[a-zA-Z]+[\s\x0B\x09\x0C\x3B\x2C\x28\x3B]*?=" \
    "id:941120,\
    phase:2,\
    block,\
    capture,\
    t:none,t:utf8toUnicode,t:urlDecodeUni,t:htmlEntityDecode,t:jsDecode,t:cssDecode,t:removeNulls,\
    msg:'XSS过滤器-类别2:事件处理程序向量',\
    logdata:'Matched Data: %{TX.0} found within %{MATCHED_VAR_NAME}: %{MATCHED_VAR}',\
    tag:'application-multi',\
    tag:'language-multi',\
    tag:'platform-multi',\
    tag:'attack-xss',\
    tag:'OWASP_CRS',\
    tag:'OWASP_CRS/WEB_ATTACK/XSS',\
    tag:'WASCTC/WASC-8',\
    tag:'WASCTC/WASC-22',\
    tag:'OWASP_TOP_10/A3',\
    tag:'OWASP_AppSensor/IE1',\
    tag:'CAPEC-242',\
    ctl:auditLogParts=+E,\
    ver:'OWASP_CRS/3.2.0',\
    severity:'CRITICAL',\
    setvar:'tx.xss_score=+%{tx.critical_anomaly_score}',\
    setvar:'tx.anomaly_score_pl1=+%{tx.critical_anomaly_score}'"


#
# -=[ XSS Filters - Category 3 ]=-
#
SecRule REQUEST_COOKIES|!REQUEST_COOKIES:/__utm/|REQUEST_COOKIES_NAMES|REQUEST_HEADERS:User-Agent|ARGS_NAMES|ARGS|XML:/* "@rx (?i)[\s\S]((?:x(?:link:href|html|mlns)|!ENTITY.*?(?:SYSTEM|PUBLIC)|data:text\/html|formaction|\@import|base64)\b|pattern\b.*?=)" \
    "id:941130,\
    phase:2,\
    block,\
    capture,\
    t:none,t:utf8toUnicode,t:urlDecodeUni,t:htmlEntityDecode,t:jsDecode,t:cssDecode,t:removeNulls,\
    msg:'XSS过滤器-类别3:属性向量',\
    logdata:'Matched Data: %{TX.0} found within %{MATCHED_VAR_NAME}: %{MATCHED_VAR}',\
    tag:'application-multi',\
    tag:'language-multi',\
    tag:'platform-multi',\
    tag:'attack-xss',\
    tag:'OWASP_CRS',\
    tag:'OWASP_CRS/WEB_ATTACK/XSS',\
    tag:'WASCTC/WASC-8',\
    tag:'WASCTC/WASC-22',\
    tag:'OWASP_TOP_10/A3',\
    tag:'OWASP_AppSensor/IE1',\
    tag:'CAPEC-242',\
    ctl:auditLogParts=+E,\
    ver:'OWASP_CRS/3.2.0',\
    severity:'CRITICAL',\
    setvar:'tx.xss_score=+%{tx.critical_anomaly_score}',\
    setvar:'tx.anomaly_score_pl1=+%{tx.critical_anomaly_score}'"


#
# -=[ XSS Filters - Category 4 ]=-
# XSS vectors making use of javascript uri and tags, e.g., <p style="background:url(javascript:alert(1))">
#
SecRule REQUEST_COOKIES|!REQUEST_COOKIES:/__utm/|REQUEST_COOKIES_NAMES|REQUEST_HEADERS:User-Agent|REQUEST_HEADERS:Referer|ARGS_NAMES|ARGS|XML:/* "@rx (?i)(?:<(?:(?:apple|objec)t|isindex|embed|style|form|meta)\b[^>]*?>[\s\S]*?|(?:=|U\s*?R\s*?L\s*?\()\s*?[^>]*?\s*?S\s*?C\s*?R\s*?I\s*?P\s*?T\s*?:)" \
    "id:941140,\
    phase:2,\
    block,\
    capture,\
    t:none,t:utf8toUnicode,t:urlDecodeUni,t:htmlEntityDecode,t:jsDecode,t:cssDecode,t:removeNulls,\
    msg:'XSS过滤器-类别4:Javascript URI向量',\
    logdata:'Matched Data: %{TX.0} found within %{MATCHED_VAR_NAME}: %{MATCHED_VAR}',\
    tag:'application-multi',\
    tag:'language-multi',\
    tag:'platform-multi',\
    tag:'attack-xss',\
    tag:'OWASP_CRS',\
    tag:'OWASP_CRS/WEB_ATTACK/XSS',\
    tag:'WASCTC/WASC-8',\
    tag:'WASCTC/WASC-22',\
    tag:'OWASP_TOP_10/A3',\
    tag:'OWASP_AppSensor/IE1',\
    tag:'CAPEC-242',\
    ctl:auditLogParts=+E,\
    ver:'OWASP_CRS/3.2.0',\
    severity:'CRITICAL',\
    setvar:'tx.xss_score=+%{tx.critical_anomaly_score}',\
    setvar:'tx.anomaly_score_pl1=+%{tx.critical_anomaly_score}'"


#
# -=[ NoScript XSS Filters ]=-
# Ref: http://noscript.net/
#
# [NoScript InjectionChecker] HTML injection
#
# Regexp generated from util/regexp-assemble/regexp-941160.data using Regexp::Assemble.
# To rebuild the regexp:
#   cd util/regexp-assemble
#   ./regexp-assemble.pl regexp-941160.data
# Note that after assemble an ignore case flag (i) is added to the to the Regexp::Assemble output:
#   Add ignore case flag between '?' and ':': "(?i:...)"
#
SecRule REQUEST_COOKIES|!REQUEST_COOKIES:/__utm/|REQUEST_COOKIES_NAMES|REQUEST_HEADERS:User-Agent|REQUEST_HEADERS:Referer|ARGS_NAMES|ARGS|XML:/* "@rx (?i:(?:<\w[\s\S]*[\s\/]|['\"](?:[\s\S]*[\s\/])?)(?:on(?:d(?:e(?:vice(?:(?:orienta|mo)tion|proximity|found|light)|livery(?:success|error)|activate)|r(?:ag(?:e(?:n(?:ter|d)|xit)|(?:gestur|leav)e|start|drop|over)|op)|i(?:s(?:c(?:hargingtimechange|onnect(?:ing|ed))|abled)|aling)|ata(?:setc(?:omplete|hanged)|(?:availabl|chang)e|error)|urationchange|ownloading|blclick)|Moz(?:M(?:agnifyGesture(?:Update|Start)?|ouse(?:PixelScroll|Hittest))|S(?:wipeGesture(?:Update|Start|End)?|crolledAreaChanged)|(?:(?:Press)?TapGestur|BeforeResiz)e|EdgeUI(?:C(?:omplet|ancel)|Start)ed|RotateGesture(?:Update|Start)?|A(?:udioAvailable|fterPaint))|c(?:o(?:m(?:p(?:osition(?:update|start|end)|lete)|mand(?:update)?)|n(?:t(?:rolselect|extmenu)|nect(?:ing|ed))|py)|a(?:(?:llschang|ch)ed|nplay(?:through)?|rdstatechange)|h(?:(?:arging(?:time)?ch)?ange|ecking)|(?:fstate|ell)change|u(?:echange|t)|l(?:ick|ose))|s(?:t(?:a(?:t(?:uschanged|echange)|lled|rt)|k(?:sessione|comma)nd|op)|e(?:ek(?:complete|ing|ed)|(?:lec(?:tstar)?)?t|n(?:ding|t))|(?:peech|ound)(?:start|end)|u(?:ccess|spend|bmit)|croll|how)|m(?:o(?:z(?:(?:pointerlock|fullscreen)(?:change|error)|(?:orientation|time)change|network(?:down|up)load)|use(?:(?:lea|mo)ve|o(?:ver|ut)|enter|wheel|down|up)|ve(?:start|end)?)|essage|ark)|b(?:e(?:for(?:e(?:(?:scriptexecu|activa)te|u(?:nload|pdate)|p(?:aste|rint)|c(?:opy|ut)|editfocus)|deactivate)|gin(?:Event)?)|oun(?:dary|ce)|l(?:ocked|ur)|roadcast|usy)|a(?:n(?:imation(?:iteration|start|end)|tennastatechange)|fter(?:(?:scriptexecu|upda)te|print)|udio(?:process|start|end)|d(?:apteradded|dtrack)|ctivate|lerting|bort)|DOM(?:Node(?:Inserted(?:IntoDocument)?|Removed(?:FromDocument)?)|(?:CharacterData|Subtree)Modified|A(?:ttrModified|ctivate)|Focus(?:Out|In)|MouseScroll)|r(?:e(?:s(?:u(?:m(?:ing|e)|lt)|ize|et)|adystatechange|pea(?:tEven)?t|movetrack|trieving|ceived)|ow(?:s(?:inserted|delete)|e(?:nter|xit))|atechange)|p(?:op(?:up(?:hid(?:den|ing)|show(?:ing|n))|state)|a(?:ge(?:hide|show)|(?:st|us)e|int)|ro(?:pertychange|gress)|lay(?:ing)?)|t(?:ouch(?:(?:lea|mo)ve|en(?:ter|d)|cancel|start)|ime(?:update|out)|ransitionend|ext)|u(?:s(?:erproximity|sdreceived)|p(?:gradeneeded|dateready)|n(?:derflow|load))|f(?:o(?:rm(?:change|input)|cus(?:out|in)?)|i(?:lterchange|nish)|ailed)|l(?:o(?:ad(?:e(?:d(?:meta)?data|nd)|start)|secapture)|evelchange|y)|g(?:amepad(?:(?:dis)?connected|button(?:down|up)|axismove)|et)|e(?:n(?:d(?:Event|ed)?|abled|ter)|rror(?:update)?|mptied|xit)|i(?:cc(?:cardlockerror|infochange)|n(?:coming|valid|put))|o(?:(?:(?:ff|n)lin|bsolet)e|verflow(?:changed)?|pen)|SVG(?:(?:Unl|L)oad|Resize|Scroll|Abort|Error|Zoom)|h(?:e(?:adphoneschange|l[dp])|ashchange|olding)|v(?:o(?:lum|ic)e|ersion)change|w(?:a(?:it|rn)ing|heel)|key(?:press|down|up)|(?:AppComman|Loa)d|no(?:update|match)|Request|zoom)|s(?:tyle|rc)|background|formaction|lowsrc|ping)[\s\x08]*?=|<[^\w<>]*(?:[^<>\"'\s]*:)?[^\w<>]*\W*?(?:(?:a\W*?(?:n\W*?i\W*?m\W*?a\W*?t\W*?e|p\W*?p\W*?l\W*?e\W*?t|u\W*?d\W*?i\W*?o)|b\W*?(?:i\W*?n\W*?d\W*?i\W*?n\W*?g\W*?s|a\W*?s\W*?e|o\W*?d\W*?y)|i?\W*?f\W*?r\W*?a\W*?m\W*?e|o\W*?b\W*?j\W*?e\W*?c\W*?t|i\W*?m\W*?a?\W*?g\W*?e?|e\W*?m\W*?b\W*?e\W*?d|p\W*?a\W*?r\W*?a\W*?m|v\W*?i\W*?d\W*?e\W*?o|l\W*?i\W*?n\W*?k)[^>\w]|s\W*?(?:c\W*?r\W*?i\W*?p\W*?t|t\W*?y\W*?l\W*?e|e\W*?t[^>\w]|v\W*?g)|m\W*?(?:a\W*?r\W*?q\W*?u\W*?e\W*?e|e\W*?t\W*?a[^>\w])|f\W*?o\W*?r\W*?m))" \
    "id:941160,\
    phase:2,\
    block,\
    capture,\
    t:none,t:utf8toUnicode,t:urlDecodeUni,t:htmlEntityDecode,t:jsDecode,t:cssDecode,t:removeNulls,\
    msg:'NoScript XSS InjectionChecker: HTML注入',\
    logdata:'Matched Data: %{TX.0} found within %{MATCHED_VAR_NAME}: %{MATCHED_VAR}',\
    tag:'application-multi',\
    tag:'language-multi',\
    tag:'platform-multi',\
    tag:'attack-xss',\
    tag:'OWASP_CRS',\
    tag:'OWASP_CRS/WEB_ATTACK/XSS',\
    tag:'WASCTC/WASC-8',\
    tag:'WASCTC/WASC-22',\
    tag:'OWASP_TOP_10/A3',\
    tag:'OWASP_AppSensor/IE1',\
    tag:'CAPEC-242',\
    ctl:auditLogParts=+E,\
    ver:'OWASP_CRS/3.2.0',\
    severity:'CRITICAL',\
    setvar:'tx.xss_score=+%{tx.critical_anomaly_score}',\
    setvar:'tx.anomaly_score_pl1=+%{tx.critical_anomaly_score}'"


#
# [NoScript InjectionChecker] Attributes injection
#
SecRule REQUEST_COOKIES|!REQUEST_COOKIES:/__utm/|REQUEST_COOKIES_NAMES|REQUEST_HEADERS:User-Agent|REQUEST_HEADERS:Referer|ARGS_NAMES|ARGS|XML:/* "@rx (?i)(?:\W|^)(?:javascript:(?:[\s\S]+[=\\\(\[\.<]|[\s\S]*?(?:\bname\b|\\[ux]\d))|data:(?:(?:[a-z]\w+\/\w[\w+-]+\w)?[;,]|[\s\S]*?;[\s\S]*?\b(?:base64|charset=)|[\s\S]*?,[\s\S]*?<[\s\S]*?\w[\s\S]*?>))|@\W*?i\W*?m\W*?p\W*?o\W*?r\W*?t\W*?(?:\/\*[\s\S]*?)?(?:[\"']|\W*?u\W*?r\W*?l[\s\S]*?\()|\W*?-\W*?m\W*?o\W*?z\W*?-\W*?b\W*?i\W*?n\W*?d\W*?i\W*?n\W*?g[\s\S]*?:[\s\S]*?\W*?u\W*?r\W*?l[\s\S]*?\(" \
    "id:941170,\
    phase:2,\
    block,\
    capture,\
    t:none,t:utf8toUnicode,t:urlDecodeUni,t:htmlEntityDecode,t:jsDecode,t:cssDecode,t:removeNulls,\
    msg:'NoScript XSS InjectionChecker:属性注入',\
    logdata:'Matched Data: %{TX.0} found within %{MATCHED_VAR_NAME}: %{MATCHED_VAR}',\
    tag:'application-multi',\
    tag:'language-multi',\
    tag:'platform-multi',\
    tag:'attack-xss',\
    tag:'OWASP_CRS',\
    tag:'OWASP_CRS/WEB_ATTACK/XSS',\
    tag:'WASCTC/WASC-8',\
    tag:'WASCTC/WASC-22',\
    tag:'OWASP_TOP_10/A3',\
    tag:'OWASP_AppSensor/IE1',\
    tag:'CAPEC-242',\
    ctl:auditLogParts=+E,\
    ver:'OWASP_CRS/3.2.0',\
    severity:'CRITICAL',\
    setvar:'tx.xss_score=+%{tx.critical_anomaly_score}',\
    setvar:'tx.anomaly_score_pl1=+%{tx.critical_anomaly_score}'"


#
# [Blacklist Keywords from Node-Validator]
# https://raw.github.com/chriso/node-validator/master/validator.js
#
SecRule REQUEST_COOKIES|!REQUEST_COOKIES:/__utm/|REQUEST_COOKIES_NAMES|ARGS_NAMES|ARGS|XML:/* "@pm document.cookie document.write .parentnode .innerhtml window.location -moz-binding <!-- --> <![cdata[" \
    "id:941180,\
    phase:2,\
    block,\
    capture,\
    t:none,t:utf8toUnicode,t:urlDecodeUni,t:htmlEntityDecode,t:jsDecode,t:cssDecode,t:lowercase,t:removeNulls,\
    msg:'节点验证器黑名单关键字',\
    logdata:'Matched Data: %{TX.0} found within %{MATCHED_VAR_NAME}: %{MATCHED_VAR}',\
    tag:'application-multi',\
    tag:'language-multi',\
    tag:'platform-multi',\
    tag:'attack-xss',\
    tag:'OWASP_CRS',\
    tag:'OWASP_CRS/WEB_ATTACK/XSS',\
    tag:'WASCTC/WASC-8',\
    tag:'WASCTC/WASC-22',\
    tag:'OWASP_TOP_10/A3',\
    tag:'OWASP_AppSensor/IE1',\
    tag:'CAPEC-242',\
    ctl:auditLogParts=+E,\
    ver:'OWASP_CRS/3.2.0',\
    severity:'CRITICAL',\
    setvar:'tx.xss_score=+%{tx.critical_anomaly_score}',\
    setvar:'tx.anomaly_score_pl1=+%{tx.critical_anomaly_score}'"


#
# -=[ XSS Filters from IE ]=-
# Ref: http://blogs.technet.com/srd/archive/2008/08/18/ie-8-xss-filter-architecture-implementation.aspx
# Ref: http://xss.cx/examples/ie/internet-exploror-ie9-xss-filter-rules-example-regexp-mshtmldll.txt
#
SecRule REQUEST_COOKIES|!REQUEST_COOKIES:/__utm/|REQUEST_COOKIES_NAMES|ARGS_NAMES|ARGS|XML:/* "@rx (?i:<style.*?>.*?(?:@[i\\\\]|(?:[:=]|&#x?0*(?:58|3A|61|3D);?).*?(?:[(\\\\]|&#x?0*(?:40|28|92|5C);?)))" \
    "id:941190,\
    phase:2,\
    block,\
    capture,\
    t:none,t:utf8toUnicode,t:urlDecodeUni,t:htmlEntityDecode,t:jsDecode,t:cssDecode,t:removeNulls,\
    msg:'IE XSS过滤器-攻击检测。',\
    logdata:'Matched Data: %{TX.0} found within %{MATCHED_VAR_NAME}: %{MATCHED_VAR}',\
    tag:'application-multi',\
    tag:'language-multi',\
    tag:'platform-multi',\
    tag:'attack-xss',\
    tag:'OWASP_CRS',\
    tag:'OWASP_CRS/WEB_ATTACK/XSS',\
    tag:'WASCTC/WASC-8',\
    tag:'WASCTC/WASC-22',\
    tag:'OWASP_TOP_10/A3',\
    tag:'OWASP_AppSensor/IE1',\
    tag:'CAPEC-242',\
    ctl:auditLogParts=+E,\
    ver:'OWASP_CRS/3.2.0',\
    severity:'CRITICAL',\
    setvar:'tx.xss_score=+%{tx.critical_anomaly_score}',\
    setvar:'tx.anomaly_score_pl1=+%{tx.critical_anomaly_score}'"


SecRule REQUEST_COOKIES|!REQUEST_COOKIES:/__utm/|REQUEST_COOKIES_NAMES|ARGS_NAMES|ARGS|XML:/* "@rx (?i:<.*[:]?vmlframe.*?[\s/+]*?src[\s/+]*=)" \
    "id:941200,\
    phase:2,\
    block,\
    capture,\
    t:none,t:utf8toUnicode,t:urlDecodeUni,t:htmlEntityDecode,t:jsDecode,t:cssDecode,t:removeNulls,\
    msg:'IE XSS过滤器-攻击检测。',\
    logdata:'Matched Data: %{TX.0} found within %{MATCHED_VAR_NAME}: %{MATCHED_VAR}',\
    tag:'application-multi',\
    tag:'language-multi',\
    tag:'platform-multi',\
    tag:'attack-xss',\
    tag:'OWASP_CRS',\
    tag:'OWASP_CRS/WEB_ATTACK/XSS',\
    tag:'WASCTC/WASC-8',\
    tag:'WASCTC/WASC-22',\
    tag:'OWASP_TOP_10/A3',\
    tag:'OWASP_AppSensor/IE1',\
    tag:'CAPEC-242',\
    ctl:auditLogParts=+E,\
    ver:'OWASP_CRS/3.2.0',\
    severity:'CRITICAL',\
    setvar:'tx.xss_score=+%{tx.critical_anomaly_score}',\
    setvar:'tx.anomaly_score_pl1=+%{tx.critical_anomaly_score}'"


SecRule REQUEST_COOKIES|!REQUEST_COOKIES:/__utm/|REQUEST_COOKIES_NAMES|ARGS_NAMES|ARGS|XML:/* "@rx (?i:(?:j|&#x?0*(?:74|4A|106|6A);?)(?:\t|&(?:#x?0*(?:9|13|10|A|D);?|tab;|newline;))*(?:a|&#x?0*(?:65|41|97|61);?)(?:\t|&(?:#x?0*(?:9|13|10|A|D);?|tab;|newline;))*(?:v|&#x?0*(?:86|56|118|76);?)(?:\t|&(?:#x?0*(?:9|13|10|A|D);?|tab;|newline;))*(?:a|&#x?0*(?:65|41|97|61);?)(?:\t|&(?:#x?0*(?:9|13|10|A|D);?|tab;|newline;))*(?:s|&#x?0*(?:83|53|115|73);?)(?:\t|&(?:#x?0*(?:9|13|10|A|D);?|tab;|newline;))*(?:c|&#x?0*(?:67|43|99|63);?)(?:\t|&(?:#x?0*(?:9|13|10|A|D);?|tab;|newline;))*(?:r|&#x?0*(?:82|52|114|72);?)(?:\t|&(?:#x?0*(?:9|13|10|A|D);?|tab;|newline;))*(?:i|&#x?0*(?:73|49|105|69);?)(?:\t|&(?:#x?0*(?:9|13|10|A|D);?|tab;|newline;))*(?:p|&#x?0*(?:80|50|112|70);?)(?:\t|&(?:#x?0*(?:9|13|10|A|D);?|tab;|newline;))*(?:t|&#x?0*(?:84|54|116|74);?)(?:\t|&(?:#x?0*(?:9|13|10|A|D);?|tab;|newline;))*(?::|&(?:#x?0*(?:58|3A);?|colon;)).)" \
    "id:941210,\
    phase:2,\
    block,\
    capture,\
    t:none,t:utf8toUnicode,t:urlDecodeUni,t:htmlEntityDecode,t:jsDecode,t:cssDecode,t:removeNulls,\
    msg:'IE XSS过滤器-攻击检测。',\
    logdata:'Matched Data: %{TX.0} found within %{MATCHED_VAR_NAME}: %{MATCHED_VAR}',\
    tag:'application-multi',\
    tag:'language-multi',\
    tag:'platform-multi',\
    tag:'attack-xss',\
    tag:'OWASP_CRS',\
    tag:'OWASP_CRS/WEB_ATTACK/XSS',\
    tag:'WASCTC/WASC-8',\
    tag:'WASCTC/WASC-22',\
    tag:'OWASP_TOP_10/A3',\
    tag:'OWASP_AppSensor/IE1',\
    tag:'CAPEC-242',\
    ctl:auditLogParts=+E,\
    ver:'OWASP_CRS/3.2.0',\
    severity:'CRITICAL',\
    setvar:'tx.xss_score=+%{tx.critical_anomaly_score}',\
    setvar:'tx.anomaly_score_pl1=+%{tx.critical_anomaly_score}'"


SecRule REQUEST_COOKIES|!REQUEST_COOKIES:/__utm/|REQUEST_COOKIES_NAMES|ARGS_NAMES|ARGS|XML:/* "@rx (?i:(?:v|&#x?0*(?:86|56|118|76);?)(?:\t|&(?:#x?0*(?:9|13|10|A|D);?|tab;|newline;))*(?:b|&#x?0*(?:66|42|98|62);?)(?:\t|&(?:#x?0*(?:9|13|10|A|D);?|tab;|newline;))*(?:s|&#x?0*(?:83|53|115|73);?)(?:\t|&(?:#x?0*(?:9|13|10|A|D);?|tab;|newline;))*(?:c|&#x?0*(?:67|43|99|63);?)(?:\t|&(?:#x?0*(?:9|13|10|A|D);?|tab;|newline;))*(?:r|&#x?0*(?:82|52|114|72);?)(?:\t|&(?:#x?0*(?:9|13|10|A|D);?|tab;|newline;))*(?:i|&#x?0*(?:73|49|105|69);?)(?:\t|&(?:#x?0*(?:9|13|10|A|D);?|tab;|newline;))*(?:p|&#x?0*(?:80|50|112|70);?)(?:\t|&(?:#x?0*(?:9|13|10|A|D);?|tab;|newline;))*(?:t|&#x?0*(?:84|54|116|74);?)(?:\t|&(?:#x?0*(?:9|13|10|A|D);?|tab;|newline;))*(?::|&(?:#x?0*(?:58|3A);?|colon;)).)" \
    "id:941220,\
    phase:2,\
    block,\
    capture,\
    t:none,t:utf8toUnicode,t:urlDecodeUni,t:htmlEntityDecode,t:jsDecode,t:cssDecode,t:removeNulls,\
    msg:'IE XSS过滤器-攻击检测。',\
    logdata:'Matched Data: %{TX.0} found within %{MATCHED_VAR_NAME}: %{MATCHED_VAR}',\
    tag:'application-multi',\
    tag:'language-multi',\
    tag:'platform-multi',\
    tag:'attack-xss',\
    tag:'OWASP_CRS',\
    tag:'OWASP_CRS/WEB_ATTACK/XSS',\
    tag:'WASCTC/WASC-8',\
    tag:'WASCTC/WASC-22',\
    tag:'OWASP_TOP_10/A3',\
    tag:'OWASP_AppSensor/IE1',\
    tag:'CAPEC-242',\
    ctl:auditLogParts=+E,\
    ver:'OWASP_CRS/3.2.0',\
    severity:'CRITICAL',\
    setvar:'tx.xss_score=+%{tx.critical_anomaly_score}',\
    setvar:'tx.anomaly_score_pl1=+%{tx.critical_anomaly_score}'"


SecRule REQUEST_COOKIES|!REQUEST_COOKIES:/__utm/|REQUEST_COOKIES_NAMES|ARGS_NAMES|ARGS|XML:/* "@rx (?i)<EMBED[\s/+].*?(?:src|type).*?=" \
    "id:941230,\
    phase:2,\
    block,\
    capture,\
    t:none,t:utf8toUnicode,t:urlDecodeUni,t:htmlEntityDecode,t:jsDecode,t:cssDecode,t:removeNulls,\
    msg:'IE XSS过滤器-攻击检测。',\
    logdata:'Matched Data: %{TX.0} found within %{MATCHED_VAR_NAME}: %{MATCHED_VAR}',\
    tag:'application-multi',\
    tag:'language-multi',\
    tag:'platform-multi',\
    tag:'attack-xss',\
    tag:'OWASP_CRS',\
    tag:'OWASP_CRS/WEB_ATTACK/XSS',\
    tag:'WASCTC/WASC-8',\
    tag:'WASCTC/WASC-22',\
    tag:'OWASP_TOP_10/A3',\
    tag:'OWASP_AppSensor/IE1',\
    tag:'CAPEC-242',\
    ctl:auditLogParts=+E,\
    ver:'OWASP_CRS/3.2.0',\
    severity:'CRITICAL',\
    setvar:'tx.xss_score=+%{tx.critical_anomaly_score}',\
    setvar:'tx.anomaly_score_pl1=+%{tx.critical_anomaly_score}'"


SecRule REQUEST_COOKIES|!REQUEST_COOKIES:/__utm/|REQUEST_COOKIES_NAMES|ARGS_NAMES|ARGS|XML:/* "@rx <[?]?import[\s\/+\S]*?implementation[\s\/+]*?=" \
    "id:941240,\
    phase:2,\
    block,\
    capture,\
    t:none,t:utf8toUnicode,t:urlDecodeUni,t:htmlEntityDecode,t:jsDecode,t:cssDecode,t:lowercase,t:removeNulls,\
    msg:'IE XSS过滤器-攻击检测。',\
    logdata:'Matched Data: %{TX.0} found within %{MATCHED_VAR_NAME}: %{MATCHED_VAR}',\
    tag:'application-multi',\
    tag:'language-multi',\
    tag:'platform-multi',\
    tag:'attack-xss',\
    tag:'OWASP_CRS',\
    tag:'OWASP_CRS/WEB_ATTACK/XSS',\
    tag:'WASCTC/WASC-8',\
    tag:'WASCTC/WASC-22',\
    tag:'OWASP_TOP_10/A3',\
    tag:'OWASP_AppSensor/IE1',\
    tag:'CAPEC-242',\
    ctl:auditLogParts=+E,\
    ver:'OWASP_CRS/3.2.0',\
    severity:'CRITICAL',\
    setvar:'tx.xss_score=+%{tx.critical_anomaly_score}',\
    setvar:'tx.anomaly_score_pl1=+%{tx.critical_anomaly_score}'"


SecRule REQUEST_COOKIES|!REQUEST_COOKIES:/__utm/|REQUEST_COOKIES_NAMES|ARGS_NAMES|ARGS|XML:/* "@rx (?i:<META[\s/+].*?http-equiv[\s/+]*=[\s/+]*[\"'`]?(?:(?:c|&#x?0*(?:67|43|99|63);?)|(?:r|&#x?0*(?:82|52|114|72);?)|(?:s|&#x?0*(?:83|53|115|73);?)))" \
    "id:941250,\
    phase:2,\
    block,\
    capture,\
    t:none,t:utf8toUnicode,t:urlDecodeUni,t:htmlEntityDecode,t:jsDecode,t:cssDecode,t:removeNulls,\
    msg:'IE XSS过滤器-攻击检测。',\
    logdata:'Matched Data: %{TX.0} found within %{MATCHED_VAR_NAME}: %{MATCHED_VAR}',\
    tag:'application-multi',\
    tag:'language-multi',\
    tag:'platform-multi',\
    tag:'attack-xss',\
    tag:'OWASP_CRS',\
    tag:'OWASP_CRS/WEB_ATTACK/XSS',\
    tag:'WASCTC/WASC-8',\
    tag:'WASCTC/WASC-22',\
    tag:'OWASP_TOP_10/A3',\
    tag:'OWASP_AppSensor/IE1',\
    tag:'CAPEC-242',\
    ctl:auditLogParts=+E,\
    ver:'OWASP_CRS/3.2.0',\
    severity:'CRITICAL',\
    setvar:'tx.xss_score=+%{tx.critical_anomaly_score}',\
    setvar:'tx.anomaly_score_pl1=+%{tx.critical_anomaly_score}'"


SecRule REQUEST_COOKIES|!REQUEST_COOKIES:/__utm/|REQUEST_COOKIES_NAMES|ARGS_NAMES|ARGS|XML:/* "@rx (?i:<META[\s/+].*?charset[\s/+]*=)" \
    "id:941260,\
    phase:2,\
    block,\
    capture,\
    t:none,t:utf8toUnicode,t:urlDecodeUni,t:htmlEntityDecode,t:jsDecode,t:cssDecode,t:removeNulls,\
    msg:'IE XSS过滤器-攻击检测。',\
    logdata:'Matched Data: %{TX.0} found within %{MATCHED_VAR_NAME}: %{MATCHED_VAR}',\
    tag:'application-multi',\
    tag:'language-multi',\
    tag:'platform-multi',\
    tag:'attack-xss',\
    tag:'OWASP_CRS',\
    tag:'OWASP_CRS/WEB_ATTACK/XSS',\
    tag:'WASCTC/WASC-8',\
    tag:'WASCTC/WASC-22',\
    tag:'OWASP_TOP_10/A3',\
    tag:'OWASP_AppSensor/IE1',\
    tag:'CAPEC-242',\
    ctl:auditLogParts=+E,\
    ver:'OWASP_CRS/3.2.0',\
    severity:'CRITICAL',\
    setvar:'tx.xss_score=+%{tx.critical_anomaly_score}',\
    setvar:'tx.anomaly_score_pl1=+%{tx.critical_anomaly_score}'"


SecRule REQUEST_COOKIES|!REQUEST_COOKIES:/__utm/|REQUEST_COOKIES_NAMES|ARGS_NAMES|ARGS|XML:/* "@rx (?i)<LINK[\s/+].*?href[\s/+]*=" \
    "id:941270,\
    phase:2,\
    block,\
    capture,\
    t:none,t:utf8toUnicode,t:urlDecodeUni,t:htmlEntityDecode,t:jsDecode,t:cssDecode,t:removeNulls,\
    msg:'IE XSS过滤器-攻击检测。',\
    logdata:'Matched Data: %{TX.0} found within %{MATCHED_VAR_NAME}: %{MATCHED_VAR}',\
    tag:'application-multi',\
    tag:'language-multi',\
    tag:'platform-multi',\
    tag:'attack-xss',\
    tag:'OWASP_CRS',\
    tag:'OWASP_CRS/WEB_ATTACK/XSS',\
    tag:'WASCTC/WASC-8',\
    tag:'WASCTC/WASC-22',\
    tag:'OWASP_TOP_10/A3',\
    tag:'OWASP_AppSensor/IE1',\
    tag:'CAPEC-242',\
    ctl:auditLogParts=+E,\
    ver:'OWASP_CRS/3.2.0',\
    severity:'CRITICAL',\
    setvar:'tx.xss_score=+%{tx.critical_anomaly_score}',\
    setvar:'tx.anomaly_score_pl1=+%{tx.critical_anomaly_score}'"


SecRule REQUEST_COOKIES|!REQUEST_COOKIES:/__utm/|REQUEST_COOKIES_NAMES|ARGS_NAMES|ARGS|XML:/* "@rx (?i)<BASE[\s/+].*?href[\s/+]*=" \
    "id:941280,\
    phase:2,\
    block,\
    capture,\
    t:none,t:utf8toUnicode,t:urlDecodeUni,t:htmlEntityDecode,t:jsDecode,t:cssDecode,t:removeNulls,\
    msg:'IE XSS过滤器-攻击检测。',\
    logdata:'Matched Data: %{TX.0} found within %{MATCHED_VAR_NAME}: %{MATCHED_VAR}',\
    tag:'application-multi',\
    tag:'language-multi',\
    tag:'platform-multi',\
    tag:'attack-xss',\
    tag:'OWASP_CRS',\
    tag:'OWASP_CRS/WEB_ATTACK/XSS',\
    tag:'WASCTC/WASC-8',\
    tag:'WASCTC/WASC-22',\
    tag:'OWASP_TOP_10/A3',\
    tag:'OWASP_AppSensor/IE1',\
    tag:'CAPEC-242',\
    ctl:auditLogParts=+E,\
    ver:'OWASP_CRS/3.2.0',\
    severity:'CRITICAL',\
    setvar:'tx.xss_score=+%{tx.critical_anomaly_score}',\
    setvar:'tx.anomaly_score_pl1=+%{tx.critical_anomaly_score}'"


SecRule REQUEST_COOKIES|!REQUEST_COOKIES:/__utm/|REQUEST_COOKIES_NAMES|ARGS_NAMES|ARGS|XML:/* "@rx (?i)<APPLET[\s/+>]" \
    "id:941290,\
    phase:2,\
    block,\
    capture,\
    t:none,t:utf8toUnicode,t:urlDecodeUni,t:htmlEntityDecode,t:jsDecode,t:cssDecode,t:removeNulls,\
    msg:'IE XSS过滤器-攻击检测。',\
    logdata:'Matched Data: %{TX.0} found within %{MATCHED_VAR_NAME}: %{MATCHED_VAR}',\
    tag:'application-multi',\
    tag:'language-multi',\
    tag:'platform-multi',\
    tag:'attack-xss',\
    tag:'OWASP_CRS',\
    tag:'OWASP_CRS/WEB_ATTACK/XSS',\
    tag:'WASCTC/WASC-8',\
    tag:'WASCTC/WASC-22',\
    tag:'OWASP_TOP_10/A3',\
    tag:'OWASP_AppSensor/IE1',\
    tag:'CAPEC-242',\
    ctl:auditLogParts=+E,\
    ver:'OWASP_CRS/3.2.0',\
    severity:'CRITICAL',\
    setvar:'tx.xss_score=+%{tx.critical_anomaly_score}',\
    setvar:'tx.anomaly_score_pl1=+%{tx.critical_anomaly_score}'"


SecRule REQUEST_COOKIES|!REQUEST_COOKIES:/__utm/|REQUEST_COOKIES_NAMES|ARGS_NAMES|ARGS|XML:/* "@rx (?i)<OBJECT[\s/+].*?(?:type|codetype|classid|code|data)[\s/+]*=" \
    "id:941300,\
    phase:2,\
    block,\
    capture,\
    t:none,t:utf8toUnicode,t:urlDecodeUni,t:htmlEntityDecode,t:jsDecode,t:cssDecode,t:removeNulls,\
    msg:'IE XSS过滤器-攻击检测。',\
    logdata:'Matched Data: %{TX.0} found within %{MATCHED_VAR_NAME}: %{MATCHED_VAR}',\
    tag:'application-multi',\
    tag:'language-multi',\
    tag:'platform-multi',\
    tag:'attack-xss',\
    tag:'OWASP_CRS',\
    tag:'OWASP_CRS/WEB_ATTACK/XSS',\
    tag:'WASCTC/WASC-8',\
    tag:'WASCTC/WASC-22',\
    tag:'OWASP_TOP_10/A3',\
    tag:'OWASP_AppSensor/IE1',\
    tag:'CAPEC-242',\
    ctl:auditLogParts=+E,\
    ver:'OWASP_CRS/3.2.0',\
    severity:'CRITICAL',\
    setvar:'tx.xss_score=+%{tx.critical_anomaly_score}',\
    setvar:'tx.anomaly_score_pl1=+%{tx.critical_anomaly_score}'"

#
# https://www.owasp.org/index.php/XSS_Filter_Evasion_Cheat_Sheet
# US-ASCII encoding bypass listed on XSS filter evasion
# Reported by Mazin Ahmed
#

SecRule REQUEST_COOKIES|!REQUEST_COOKIES:/__utm/|REQUEST_COOKIES_NAMES|ARGS_NAMES|ARGS|XML:/* "@rx (?:¾|¼).*(?:¾|¼|>)|(?:¾|¼|<).*(?:¾|¼)" \
    "id:941310,\
    phase:2,\
    block,\
    capture,\
    t:none,t:urlDecodeUni,t:lowercase,t:urlDecode,t:htmlEntityDecode,t:jsDecode,\
    msg:'US-ASCII畸形编码XSS过滤器-攻击检测。',\
    logdata:'Matched Data: %{TX.0} found within %{MATCHED_VAR_NAME}: %{MATCHED_VAR}',\
    tag:'application-multi',\
    tag:'language-multi',\
    tag:'platform-tomcat',\
    tag:'attack-xss',\
    tag:'OWASP_CRS',\
    tag:'OWASP_CRS/WEB_ATTACK/XSS',\
    tag:'WASCTC/WASC-8',\
    tag:'WASCTC/WASC-22',\
    tag:'OWASP_TOP_10/A3',\
    tag:'OWASP_AppSensor/IE1',\
    tag:'CAPEC-242',\
    ctl:auditLogParts=+E,\
    ver:'OWASP_CRS/3.2.0',\
    severity:'CRITICAL',\
    setvar:'tx.xss_score=+%{tx.critical_anomaly_score}',\
    setvar:'tx.anomaly_score_pl1=+%{tx.critical_anomaly_score}'"

#
# UTF-7 encoding XSS filter evasion for IE.
# Reported by Vladimir Ivanov
#

SecRule REQUEST_COOKIES|!REQUEST_COOKIES:/__utm/|REQUEST_COOKIES_NAMES|ARGS_NAMES|ARGS|XML:/* "@rx (?:\+ADw\-|\+AD4\-).*(?:\+ADw\-|\+AD4\-|>)|(?:\+ADw\-|\+AD4\-|<).*(?:\+ADw\-|\+AD4\-)" \
    "id:941350,\
    phase:2,\
    block,\
    capture,\
    t:none,t:urlDecodeUni,t:urlDecode,t:htmlEntityDecode,t:jsDecode,\
    msg:'UTF-7编码IE XSS -攻击检测。',\
    logdata:'Matched Data: %{TX.0} found within %{MATCHED_VAR_NAME}: %{MATCHED_VAR}',\
    tag:'application-multi',\
    tag:'language-multi',\
    tag:'platform-internet-explorer',\
    tag:'attack-xss',\
    tag:'OWASP_CRS',\
    tag:'OWASP_CRS/WEB_ATTACK/XSS',\
    tag:'WASCTC/WASC-8',\
    tag:'WASCTC/WASC-22',\
    tag:'OWASP_TOP_10/A3',\
    tag:'OWASP_AppSensor/IE1',\
    tag:'CAPEC-242',\
    ctl:auditLogParts=+E,\
    ver:'OWASP_CRS/3.2.0',\
    severity:'CRITICAL',\
    setvar:'tx.xss_score=+%{tx.critical_anomaly_score}',\
    setvar:'tx.anomaly_score_pl1=+%{tx.critical_anomaly_score}'"

#
# Defend against JSFuck and Hieroglyphy obfuscation of Javascript code
#
# https://en.wikipedia.org/wiki/JSFuck
# https://github.com/alcuadrado/hieroglyphy
#
# These JS obfuscations mostly aim for client side XSS exploits, hence the
# integration of this rule into the XSS rule group. But serverside JS could
# also be attacked via these techniques.
#
# Detection pattern / Core elements of JSFuck and Hieroglyphy are the
# following two items:
# !![]
# !+[]
#
# ModSecurity always transforms "+" into " " with query strings and the
# URLENCODE body processor (but not for JSON). So we need to check for
# the following patterns:
# !![]
# !+[]
# ! []

SecRule REQUEST_COOKIES|!REQUEST_COOKIES:/__utm/|REQUEST_COOKIES_NAMES|ARGS_NAMES|ARGS|XML:/* "@rx ![!+ ]\[\]" \
    "id:941360,\
    phase:2,\
    block,\
    capture,\
    t:none,\
    msg:'检测到 JSFuck/象形文字混淆',\
    logdata:'Matched Data: Suspicious payload found within %{MATCHED_VAR_NAME}: %{MATCHED_VAR}',\
    tag:'application-multi',\
    tag:'language-multi',\
    tag:'attack-xss',\
    tag:'OWASP_CRS',\
    tag:'OWASP_CRS/WEB_ATTACK/XSS',\
    tag:'OWASP_TOP_10/A7',\
    tag:'CAPEC-63',\
    ctl:auditLogParts=+E,\
    ver:'OWASP_CRS/3.2.0',\
    severity:'CRITICAL',\
    setvar:'tx.xss_score=+%{tx.critical_anomaly_score}',\
    setvar:'tx.anomaly_score_pl1=+%{tx.critical_anomaly_score}'"

#
# Prevent 941180 bypass by using JavaScript global variables
# Examples:
#    - /?search=/?a=";+alert(self["document"]["cookie"]);//
#    - /?search=/?a=";+document+/*foo*/+.+/*bar*/+cookie;//
#
SecRule REQUEST_COOKIES|!REQUEST_COOKIES:/__utm/|REQUEST_COOKIES_NAMES|ARGS|XML:/* "@rx (?:self|document|this|top|window)\s*\)*(?:\[[^\]]+\]|\.\s*document|\.\s*cookie)" \
    "id:941370,\
    phase:2,\
    block,\
    capture,\
    t:none,t:removeComments,t:urlDecodeUni,\
    msg:'发现JavaScript全局变量',\
    logdata:'Matched Data: Suspicious JS global variable found within %{MATCHED_VAR_NAME}: %{MATCHED_VAR}',\
    tag:'application-multi',\
    tag:'language-multi',\
    tag:'attack-xss',\
    tag:'OWASP_CRS',\
    tag:'OWASP_CRS/WEB_ATTACK/XSS',\
    tag:'OWASP_TOP_10/A7',\
    tag:'CAPEC-63',\
    ctl:auditLogParts=+E,\
    ver:'OWASP_CRS/3.2.0',\
    severity:'CRITICAL',\
    setvar:'tx.xss_score=+%{tx.critical_anomaly_score}',\
    setvar:'tx.anomaly_score_pl1=+%{tx.critical_anomaly_score}'"


SecRule TX:EXECUTING_PARANOIA_LEVEL "@lt 2" "id:941013,phase:1,pass,nolog,skipAfter:END-REQUEST-941-APPLICATION-ATTACK-XSS"
SecRule TX:EXECUTING_PARANOIA_LEVEL "@lt 2" "id:941014,phase:2,pass,nolog,skipAfter:END-REQUEST-941-APPLICATION-ATTACK-XSS"
#
# -= Paranoia Level 2 =- (apply only when tx.executing_paranoia_level is sufficiently high: 2 or higher)
#

#
# This is a stricter sibling of rule 941100.
#
SecRule REQUEST_HEADERS:Referer "@detectXSS" \
    "id:941101,\
    phase:2,\
    block,\
    capture,\
    t:none,t:utf8toUnicode,t:urlDecodeUni,t:htmlEntityDecode,t:jsDecode,t:cssDecode,t:removeNulls,\
    msg:'通过libinjection检测到XSS攻击',\
    logdata:'Matched Data: %{TX.0} found within %{MATCHED_VAR_NAME}: %{MATCHED_VAR}',\
    tag:'application-multi',\
    tag:'language-multi',\
    tag:'platform-multi',\
    tag:'attack-xss',\
    tag:'OWASP_CRS',\
    tag:'OWASP_CRS/WEB_ATTACK/XSS',\
    tag:'WASCTC/WASC-8',\
    tag:'WASCTC/WASC-22',\
    tag:'OWASP_TOP_10/A3',\
    tag:'OWASP_AppSensor/IE1',\
    tag:'CAPEC-242',\
    tag:'paranoia-level/2',\
    ctl:auditLogParts=+E,\
    ver:'OWASP_CRS/3.2.0',\
    severity:'CRITICAL',\
    setvar:'tx.xss_score=+%{tx.critical_anomaly_score}',\
    setvar:'tx.anomaly_score_pl2=+%{tx.critical_anomaly_score}'"


#
# -=[ XSS Filters - Category 5 ]=-
# HTML attribues - src, style and href
#
SecRule REQUEST_COOKIES|!REQUEST_COOKIES:/__utm/|REQUEST_COOKIES_NAMES|REQUEST_HEADERS:User-Agent|ARGS_NAMES|ARGS|XML:/* "@rx (?i)\b(?:s(?:tyle|rc)|href)\b[\s\S]*?=" \
    "id:941150,\
    phase:2,\
    block,\
    capture,\
    t:none,t:utf8toUnicode,t:urlDecodeUni,t:htmlEntityDecode,t:jsDecode,t:cssDecode,t:removeNulls,\
    msg:'XSS过滤器-类别5:不允许的HTML属性',\
    logdata:'Matched Data: %{TX.0} found within %{MATCHED_VAR_NAME}: %{MATCHED_VAR}',\
    tag:'application-multi',\
    tag:'language-multi',\
    tag:'platform-multi',\
    tag:'attack-xss',\
    tag:'OWASP_CRS',\
    tag:'OWASP_CRS/WEB_ATTACK/XSS',\
    tag:'WASCTC/WASC-8',\
    tag:'WASCTC/WASC-22',\
    tag:'OWASP_TOP_10/A3',\
    tag:'OWASP_AppSensor/IE1',\
    tag:'CAPEC-242',\
    tag:'paranoia-level/2',\
    ctl:auditLogParts=+E,\
    ver:'OWASP_CRS/3.2.0',\
    severity:'CRITICAL',\
    setvar:'tx.xss_score=+%{tx.critical_anomaly_score}',\
    setvar:'tx.anomaly_score_pl2=+%{tx.critical_anomaly_score}'"


# Detect tags that are the most common direct HTML injection points.
#
#     <a href=javascript:...
#     <applet src="..." type=text/html>
#     <applet src="data:text/html;base64,PHNjcmlwdD5hbGVydCgvWFNTLyk8L3NjcmlwdD4" type=text/html>
#     <base href=javascript:...
#     <base href=... // change base URL to something else to exploit relative filename inclusion
#     <bgsound src=javascript:...
#     <body background=javascript:...
#     <body onload=...
#     <embed src=http://www.example.com/flash.swf allowScriptAccess=always
#     <embed src="data:image/svg+xml;
#     <frameset><frame src="javascript:..."></frameset>
#     <iframe src=javascript:...
#     <img src=x onerror=...
#     <input type=image src=javascript:...
#     <layer src=...
#     <link href="javascript:..." rel="stylesheet" type="text/css"
#     <link href="http://www.example.com/xss.css" rel="stylesheet" type="text/css"
#     <meta http-equiv="refresh" content="0;url=javascript:..."
#     <meta http-equiv="refresh" content="0;url=http://;javascript:..." // evasion
#     <meta http-equiv="link" rel=stylesheet content="http://www.example.com/xss.css">
#     <meta http-equiv="Set-Cookie" content="NEW_COOKIE_VALUE">
#     <object data=http://www.example.com
#     <object type=text/x-scriptlet data=...
#     <object type=application/x-shockwave-flash data=xss.swf>
#     <object classid=clsid:ae24fdae-03c6-11d1-8b76-0080c744f389><param name=url value=javascript:...></object> // not verified
#     <script>...</script>
#     <script src=http://www.example.com/xss.js></script> - TODO add another rule for this
#     <script src="data:text/javascript,alert(1)"></script>
#     <script src="data:text/javascript;base64,PHNjcmlwdD5hbGVydChkb2N1bWVudC5jb29raWUpOzwvc2NyaXB0Pg=="></script>
#     <style>STYLE</style>
#     <style type=text/css>STYLE</style>
#     <style type=text/javascript>alert('xss')</style>
#     <table background=javascript:...
#     <td background=javascript:
#
#
# NOTES
#
#  - Reference the WASC Script Mapping Project - http://projects.webappsec.org/Script-Mapping
#
#  - Not using closing brackets because they are not needed for the
#    attacks to succeed. The following seems to work in FF: <body/s/onload=...
#
#  - Also, browsers sometimes tend to translate < into >, in order to "repair"
#    what they think was a mistake made by the programmer/template designer.
#
#  - Browsers are flexible when it comes to what they accept as separator between
#    tag names and attributes. The following is commonly used in payloads: <img/src=...
#    A better example: <BODY onload!#$%&amp;()*~+-_.,:;?@[/|\]^=alert("XSS")>
#
#  - Grave accents are sometimes used as an evasion technique (as a replacement for quotes),
#    but I don't believe we need to look for quotes anywhere.
#
#  - Links do not have to be fully qualified. For example, the following works:
#    <script src="//ha.ckers.org/.j">
#
# This rule is also triggered by the following exploit(s):
# [ SAP CRM Java vulnerability CVE-2018-2380 - Exploit tested: https://www.exploit-db.com/exploits/44292 ]
#
SecRule REQUEST_COOKIES|!REQUEST_COOKIES:/__utm/|!REQUEST_COOKIES:/_pk_ref/|REQUEST_COOKIES_NAMES|ARGS_NAMES|ARGS|XML:/* "@rx <(?:a|abbr|acronym|address|applet|area|audioscope|b|base|basefront|bdo|bgsound|big|blackface|blink|blockquote|body|bq|br|button|caption|center|cite|code|col|colgroup|comment|dd|del|dfn|dir|div|dl|dt|em|embed|fieldset|fn|font|form|frame|frameset|h1|head|hr|html|i|iframe|ilayer|img|input|ins|isindex|kdb|keygen|label|layer|legend|li|limittext|link|listing|map|marquee|menu|meta|multicol|nobr|noembed|noframes|noscript|nosmartquotes|object|ol|optgroup|option|p|param|plaintext|pre|q|rt|ruby|s|samp|script|select|server|shadow|sidebar|small|spacer|span|strike|strong|style|sub|sup|table|tbody|td|textarea|tfoot|th|thead|title|tr|tt|u|ul|var|wbr|xml|xmp)\W" \
    "id:941320,\
    phase:2,\
    block,\
    capture,\
    t:none,t:urlDecodeUni,t:jsDecode,t:lowercase,\
    msg:'检测到可能的XSS攻击- HTML标签处理程序',\
    logdata:'Matched Data: %{TX.0} found within %{MATCHED_VAR_NAME}: %{MATCHED_VAR}',\
    tag:'application-multi',\
    tag:'language-multi',\
    tag:'platform-multi',\
    tag:'attack-xss',\
    tag:'OWASP_CRS',\
    tag:'OWASP_CRS/WEB_ATTACK/XSS',\
    tag:'WASCTC/WASC-8',\
    tag:'WASCTC/WASC-22',\
    tag:'OWASP_TOP_10/A2',\
    tag:'OWASP_AppSensor/IE1',\
    tag:'PCI/6.5.1',\
    tag:'paranoia-level/2',\
    ver:'OWASP_CRS/3.2.0',\
    severity:'CRITICAL',\
    setvar:'tx.xss_score=+%{tx.critical_anomaly_score}',\
    setvar:'tx.anomaly_score_pl2=+%{tx.critical_anomaly_score}'"

SecRule REQUEST_COOKIES|!REQUEST_COOKIES:/__utm/|!REQUEST_COOKIES:/_pk_ref/|REQUEST_COOKIES_NAMES|ARGS_NAMES|ARGS|XML:/* "@rx (?i:[\"'][ ]*(?:[^a-z0-9~_:' ]|in).*?(?:(?:l|\\\\u006C)(?:o|\\\\u006F)(?:c|\\\\u0063)(?:a|\\\\u0061)(?:t|\\\\u0074)(?:i|\\\\u0069)(?:o|\\\\u006F)(?:n|\\\\u006E)|(?:n|\\\\u006E)(?:a|\\\\u0061)(?:m|\\\\u006D)(?:e|\\\\u0065)|(?:o|\\\\u006F)(?:n|\\\\u006E)(?:e|\\\\u0065)(?:r|\\\\u0072)(?:r|\\\\u0072)(?:o|\\\\u006F)(?:r|\\\\u0072)|(?:v|\\\\u0076)(?:a|\\\\u0061)(?:l|\\\\u006C)(?:u|\\\\u0075)(?:e|\\\\u0065)(?:O|\\\\u004F)(?:f|\\\\u0066)).*?=)" \
    "id:941330,\
    phase:2,\
    block,\
    capture,\
    t:none,t:urlDecodeUni,t:htmlEntityDecode,t:compressWhitespace,\
    msg:'IE XSS过滤器-攻击检测。',\
    logdata:'Matched Data: %{TX.0} found within %{MATCHED_VAR_NAME}: %{MATCHED_VAR}',\
    tag:'application-multi',\
    tag:'language-multi',\
    tag:'platform-multi',\
    tag:'attack-xss',\
    tag:'OWASP_CRS',\
    tag:'OWASP_CRS/WEB_ATTACK/XSS',\
    tag:'WASCTC/WASC-8',\
    tag:'WASCTC/WASC-22',\
    tag:'OWASP_TOP_10/A2',\
    tag:'OWASP_AppSensor/IE1',\
    tag:'PCI/6.5.1',\
    tag:'paranoia-level/2',\
    ver:'OWASP_CRS/3.2.0',\
    severity:'CRITICAL',\
    setvar:'tx.xss_score=+%{tx.critical_anomaly_score}',\
    setvar:'tx.anomaly_score_pl2=+%{tx.critical_anomaly_score}'"

# This rule is also triggered by the following exploit(s):
# [ SAP CRM Java vulnerability CVE-2018-2380 - Exploit tested: https://www.exploit-db.com/exploits/44292 ]
#
SecRule REQUEST_COOKIES|!REQUEST_COOKIES:/__utm/|!REQUEST_COOKIES:/_pk_ref/|REQUEST_COOKIES_NAMES|ARGS_NAMES|ARGS|XML:/* "@rx (?i)[\"\'][ ]*(?:[^a-z0-9~_:\' ]|in).+?[.].+?=" \
    "id:941340,\
    phase:2,\
    block,\
    capture,\
    t:none,t:urlDecodeUni,t:htmlEntityDecode,t:compressWhitespace,\
    msg:'IE XSS过滤器-攻击检测。',\
    logdata:'Matched Data: %{TX.0} found within %{MATCHED_VAR_NAME}: %{MATCHED_VAR}',\
    tag:'application-multi',\
    tag:'language-multi',\
    tag:'platform-multi',\
    tag:'attack-xss',\
    tag:'OWASP_CRS',\
    tag:'OWASP_CRS/WEB_ATTACK/XSS',\
    tag:'WASCTC/WASC-8',\
    tag:'WASCTC/WASC-22',\
    tag:'OWASP_TOP_10/A2',\
    tag:'OWASP_AppSensor/IE1',\
    tag:'PCI/6.5.1',\
    tag:'paranoia-level/2',\
    ver:'OWASP_CRS/3.2.0',\
    severity:'CRITICAL',\
    setvar:'tx.xss_score=+%{tx.critical_anomaly_score}',\
    setvar:'tx.anomaly_score_pl2=+%{tx.critical_anomaly_score}'"

#
# Defend against AngularJS client side template injection
#
# Of course, pure client-side AngularJS commands can not be intercepted.
# But once a command is sent to the server, the CRS will trigger.
#
# https://portswigger.net/blog/xss-without-html-client-side-template-injection-with-angularjs
#
# Example payload:
# http://localhost/login?user=%20x%20%7B%7Bconstructor.constructor(%27alert(1)%27)()%7D%7D%20.%20ff
# Decoded argument:
# {{constructor.constructor('alert(1)')()}}
#
SecRule REQUEST_COOKIES|!REQUEST_COOKIES:/__utm/|REQUEST_COOKIES_NAMES|ARGS_NAMES|ARGS|XML:/* "@rx {{.*?}}" \
    "id:941380,\
    phase:2,\
    block,\
    capture,\
    t:none,\
    msg:'检测到AngularJS客户端模板注入',\
    logdata:'Matched Data: Suspicious payload found within %{MATCHED_VAR_NAME}: %{MATCHED_VAR}',\
    tag:'application-multi',\
    tag:'language-multi',\
    tag:'attack-xss',\
    tag:'OWASP_CRS',\
    tag:'OWASP_CRS/WEB_ATTACK/XSS',\
    tag:'OWASP_TOP_10/A7',\
    tag:'CAPEC-63',\
    tag:'paranoia-level/2',\
    ctl:auditLogParts=+E,\
    ver:'OWASP_CRS/3.2.0',\
    severity:'CRITICAL',\
    setvar:'tx.xss_score=+%{tx.critical_anomaly_score}',\
    setvar:'tx.anomaly_score_pl2=+%{tx.critical_anomaly_score}'"



SecRule TX:EXECUTING_PARANOIA_LEVEL "@lt 3" "id:941015,phase:1,pass,nolog,skipAfter:END-REQUEST-941-APPLICATION-ATTACK-XSS"
SecRule TX:EXECUTING_PARANOIA_LEVEL "@lt 3" "id:941016,phase:2,pass,nolog,skipAfter:END-REQUEST-941-APPLICATION-ATTACK-XSS"
#
# -= Paranoia Level 3 =- (apply only when tx.executing_paranoia_level is sufficiently high: 3 or higher)
#



SecRule TX:EXECUTING_PARANOIA_LEVEL "@lt 4" "id:941017,phase:1,pass,nolog,skipAfter:END-REQUEST-941-APPLICATION-ATTACK-XSS"
SecRule TX:EXECUTING_PARANOIA_LEVEL "@lt 4" "id:941018,phase:2,pass,nolog,skipAfter:END-REQUEST-941-APPLICATION-ATTACK-XSS"
#
# -= Paranoia Level 4 =- (apply only when tx.executing_paranoia_level is sufficiently high: 4 or higher)
#



#
# -= Paranoia Levels Finished =-
#
SecMarker "END-REQUEST-941-APPLICATION-ATTACK-XSS"
