powershell.exe
Add-BitsFile
Add-Computer
Add-Content
Add-History
Add-Member
Add-PSSnapin
Add-Type
Checkpoint-Computer
Clear-Content
Clear-EventLog
Clear-History
Clear-Item
Clear-ItemProperty
Clear-Variable
Compare-Object
Complete-BitsTransfer
Complete-Transaction
Connect-WSMan
ConvertFrom-CSV
ConvertFrom-SecureString
ConvertFrom-StringData
Convert-Path
ConvertTo-CSV
ConvertTo-Html
ConvertTo-SecureString
ConvertTo-XML
Copy-Item
Copy-ItemProperty
Debug-Process
Disable-ComputerRestore
Disable-PSBreakpoint
Disable-PSSessionConfiguration
Disable-WSManCredSSP
Disconnect-WSMan
Enable-ComputerRestore
Enable-PSBreakpoint
Enable-PSRemoting
Enable-PSSessionConfiguration
Enable-WSManCredSSP
Enter-PSSession
Exit-PSSession
Export-Alias
Export-Clixml
Export-Console
Export-Counter
Export-CSV
Export-FormatData
Export-ModuleMember
Export-PSSession
ForEach-Object
Format-Custom
Format-List
Format-Table
Format-Wide
Get-Acl
Get-Alias
Get-AppLockerFileInformation
Get-AppLockerPolicy
Get-AuthenticodeSignature
Get-BitsTransfer
Get-ChildItem
Get-Command
Get-ComputerRestorePoint
Get-Content
Get-Counter
Get-Credential
Get-Culture
Get-Event
Get-EventLog
Get-EventSubscriber
Get-ExecutionPolicy
Get-FormatData
Get-History
Get-Host
Get-HotFix
Get-Item
Get-ItemProperty
Get-Job
Get-Location
Get-Member
Get-Module
Get-PfxCertificate
Get-Process
Get-PSBreakpoint
Get-PSCallStack
Get-PSDrive
Get-PSProvider
Get-PSSession
Get-PSSessionConfiguration
Get-PSSnapin
Get-Random
Get-Service
Get-TraceSource
Get-Transaction
Get-TroubleshootingPack
Get-UICulture
Get-Unique
Get-Variable
Get-WinEvent
Get-WmiObject
Get-WSManCredSSP
Get-WSManInstance
Group-Object
Import-Alias
Import-Clixml
Import-Counter
Import-CSV
Import-LocalizedData
Import-Module
Import-PSSession
Invoke-Command
Invoke-Expression
Invoke-History
Invoke-Item
Invoke-TroubleshootingPack
Invoke-WmiMethod
Invoke-WSManAction
Join-Path
Limit-EventLog
Measure-Command
Measure-Object
Move-Item
Move-ItemProperty
New-Alias
New-AppLockerPolicy
New-Event
New-EventLog
New-Item
New-ItemProperty
New-Module
New-ModuleManifest
New-Object
New-PSDrive
New-PSSession
New-PSSessionOption
New-Service
New-TimeSpan
New-Variable
New-WebServiceProxy
New-WSManInstance
New-WSManSessionOption
Out-Default
Out-File
Out-GridView
Out-Host
Out-Null
Out-Printer
Out-String
Pop-Location
Push-Location
Read-Host
Receive-Job
Register-EngineEvent
Register-ObjectEvent
Register-PSSessionConfiguration
Register-WmiEvent
Remove-BitsTransfer
Remove-Computer
Remove-Event
Remove-EventLog
Remove-Item
Remove-ItemProperty
Remove-Job
Remove-Module
Remove-PSBreakpoint
Remove-PSDrive
Remove-PSSession
Remove-PSSnapin
Remove-Variable
Remove-WmiObject
Remove-WSManInstance
Rename-Item
Rename-ItemProperty
Reset-ComputerMachinePassword
Resolve-Path
Restart-Computer
Restart-Service
Restore-Computer
Resume-BitsTransfer
Resume-Service
Select-Object
Select-String
Select-XML
Send-MailMessage
Set-Acl
Set-Alias
Set-AppLockerPolicy
Set-AuthenticodeSignature
Set-BitsTransfer
Set-Content
Set-Date
Set-ExecutionPolicy
Set-Item
Set-ItemProperty
Set-Location
Set-PSBreakpoint
Set-PSDebug
Set-PSSessionConfiguration
Set-Service
Set-StrictMode
Set-TraceSource
Set-Variable
Set-WmiInstance
Set-WSManInstance
Set-WSManQuickConfig
Show-EventLog
Sort-Object
Split-Path
Start-BitsTransfer
Start-Job
Start-Process
Start-Service
Start-Sleep
Start-Transaction
Start-Transcript
Stop-Computer
Stop-Job
Stop-Process
Stop-Service
Stop-Transcript
Suspend-BitsTransfer
Suspend-Service
Tee-Object
Test-AppLockerPolicy
Test-ComputerSecureChannel
Test-Connection
Test-ModuleManifest
Test-Path
Test-WSMan
Trace-Command
Undo-Transaction
Unregister-Event
Unregister-PSSessionConfiguration
Update-FormatData
Update-List
Update-TypeData
Use-Transaction
Wait-Event
Wait-Job
Wait-Process
Where-Object
Write-Debug
Write-Error
Write-EventLog
Write-Host
Write-Output
Write-Progress
Write-Verbose
Write-Warning
-EncodedCommand
-ExecutionPolicy
-PSConsoleFile
