###
# ------------------------------------------------------------------------
# OWASP ModSecurity Core Rule Set ver.3.2.0
# Copyright (c) 2006-2019 Trustwave and contributors. All rights reserved.
#
# The OWASP ModSecurity Core Rule Set is distributed under
# Apache Software License (ASL) version 2
# Please see the enclosed LICENSE file for full details.
# ------------------------------------------------------------------------

#
# -= Paranoia Level 0 (empty) =- (apply unconditionally)
#



SecRule TX:EXECUTING_PARANOIA_LEVEL "@lt 1" "id:921011,phase:1,pass,nolog,skipAfter:END-REQUEST-921-PROTOCOL-ATTACK"
SecRule TX:EXECUTING_PARANOIA_LEVEL "@lt 1" "id:921012,phase:2,pass,nolog,skipAfter:END-REQUEST-921-PROTOCOL-ATTACK"
#
# -= Paranoia Level 1 (default) =- (apply only when tx.executing_paranoia_level is sufficiently high: 1 or higher)
#

#
# -=[ HTTP Request Smuggling ]=-
#
# [ Rule Logic ]
# This rule looks for a CR/LF character in combination with a HTTP / WEBDAV method name.
# This would point to an attempt to inject a 2nd request into the request, thus bypassing
# tests carried out on the primary request.
#
# [ References ]
# http://projects.webappsec.org/HTTP-Request-Smuggling
#
SecRule ARGS_NAMES|ARGS|XML:/* "@rx [\n\r]+(?:get|post|head|options|connect|put|delete|trace|track|patch|propfind|propatch|mkcol|copy|move|lock|unlock)\s+[^\s]+(?:\s+http|[\r\n])" \
    "id:921110,\
    phase:2,\
    block,\
    capture,\
    t:none,t:urlDecodeUni,t:htmlEntityDecode,t:lowercase,\
    msg:'HTTP请求走私攻击',\
    logdata:'Matched Data: %{TX.0} found within %{MATCHED_VAR_NAME}: %{MATCHED_VAR}',\
    tag:'application-multi',\
    tag:'language-multi',\
    tag:'platform-multi',\
    tag:'attack-protocol',\
    tag:'OWASP_CRS',\
    tag:'OWASP_CRS/WEB_ATTACK/REQUEST_SMUGGLING',\
    ctl:auditLogParts=+E,\
    ver:'OWASP_CRS/3.2.0',\
    severity:'CRITICAL',\
    setvar:'tx.http_violation_score=+%{tx.critical_anomaly_score}',\
    setvar:'tx.anomaly_score_pl1=+%{tx.critical_anomaly_score}'"

#
# -=[ HTTP Response Splitting ]=-
#
# [ Rule Logic ]
# These rules look for Carriage Return (CR) %0d and Linefeed (LF) %0a characters.
# These characters may cause problems if the data is returned in a respones header and
# may be interpreted by an intermediary proxy server and treated as two separate
# responses.
#
# [ References ]
# http://projects.webappsec.org/HTTP-Response-Splitting
#
SecRule REQUEST_COOKIES|!REQUEST_COOKIES:/__utm/|REQUEST_COOKIES_NAMES|ARGS_NAMES|ARGS|XML:/* "@rx [\r\n]\W*?(?:content-(?:type|length)|set-cookie|location):" \
    "id:921120,\
    phase:2,\
    block,\
    capture,\
    t:none,t:urlDecodeUni,t:lowercase,\
    msg:'HTTP响应分裂攻击',\
    logdata:'Matched Data: %{TX.0} found within %{MATCHED_VAR_NAME}: %{MATCHED_VAR}',\
    tag:'application-multi',\
    tag:'language-multi',\
    tag:'platform-multi',\
    tag:'attack-protocol',\
    tag:'OWASP_CRS',\
    tag:'OWASP_CRS/WEB_ATTACK/RESPONSE_SPLITTING',\
    ctl:auditLogParts=+E,\
    ver:'OWASP_CRS/3.2.0',\
    severity:'CRITICAL',\
    setvar:'tx.http_violation_score=+%{tx.critical_anomaly_score}',\
    setvar:'tx.anomaly_score_pl1=+%{tx.critical_anomaly_score}'"


SecRule REQUEST_COOKIES|!REQUEST_COOKIES:/__utm/|REQUEST_COOKIES_NAMES|ARGS_NAMES|ARGS|XML:/* "@rx (?:\bhttp\/(?:0\.9|1\.[01])|<(?:html|meta)\b)" \
    "id:921130,\
    phase:2,\
    block,\
    capture,\
    t:none,t:urlDecodeUni,t:htmlEntityDecode,t:lowercase,\
    msg:'HTTP响应分裂攻击',\
    logdata:'Matched Data: %{TX.0} found within %{MATCHED_VAR_NAME}: %{MATCHED_VAR}',\
    tag:'application-multi',\
    tag:'language-multi',\
    tag:'platform-multi',\
    tag:'attack-protocol',\
    tag:'OWASP_CRS',\
    tag:'OWASP_CRS/WEB_ATTACK/RESPONSE_SPLITTING',\
    ctl:auditLogParts=+E,\
    ver:'OWASP_CRS/3.2.0',\
    severity:'CRITICAL',\
    setvar:'tx.http_violation_score=+%{tx.critical_anomaly_score}',\
    setvar:'tx.anomaly_score_pl1=+%{tx.critical_anomaly_score}'"

#
# -=[ HTTP Header Injection ]=-
#
# [ Rule Logic ]
# These rules look for Carriage Return (CR) %0d and Linefeed (LF) %0a characters,
# on their own or in combination with header field names.
# These characters may cause problems if the data is returned in a respones header
# and interpreted by the client.
# The rules are similar to rules defending against the HTTP Request Splitting and
# Request Smuggling rules.
#
# [ References ]
# https://en.wikipedia.org/wiki/HTTP_header_injection
#
SecRule REQUEST_HEADERS_NAMES|REQUEST_HEADERS "@rx [\n\r]" \
    "id:921140,\
    phase:2,\
    block,\
    capture,\
    t:none,t:htmlEntityDecode,t:lowercase,\
    msg:'HTTP头注入攻击通过报头',\
    logdata:'Matched Data: %{TX.0} found within %{MATCHED_VAR_NAME}: %{MATCHED_VAR}',\
    tag:'application-multi',\
    tag:'language-multi',\
    tag:'platform-multi',\
    tag:'attack-protocol',\
    tag:'OWASP_CRS',\
    tag:'OWASP_CRS/WEB_ATTACK/HEADER_INJECTION',\
    ctl:auditLogParts=+E,\
    ver:'OWASP_CRS/3.2.0',\
    severity:'CRITICAL',\
    setvar:'tx.http_violation_score=+%{tx.critical_anomaly_score}',\
    setvar:'tx.anomaly_score_pl1=+%{tx.critical_anomaly_score}'"


# Detect newlines in argument names.
# Checking for GET arguments has been moved to paranoia level 2 (921151)
# in order to mitigate possible false positives.
#
# This rule is also triggered by the following exploit(s):
# [ SAP CRM Java vulnerability CVE-2018-2380 - Exploit tested: https://www.exploit-db.com/exploits/44292 ]
#
SecRule ARGS_NAMES "@rx [\n\r]" \
    "id:921150,\
    phase:2,\
    block,\
    capture,\
    t:none,t:urlDecodeUni,t:htmlEntityDecode,\
    msg:'通过有效负载的 HTTP 标头注入攻击（检测到 CR/LF）',\
    logdata:'Matched Data: %{TX.0} found within %{MATCHED_VAR_NAME}: %{MATCHED_VAR}',\
    tag:'application-multi',\
    tag:'language-multi',\
    tag:'platform-multi',\
    tag:'attack-protocol',\
    tag:'OWASP_CRS',\
    tag:'OWASP_CRS/WEB_ATTACK/HEADER_INJECTION',\
    ctl:auditLogParts=+E,\
    ver:'OWASP_CRS/3.2.0',\
    severity:'CRITICAL',\
    setvar:'tx.http_violation_score=+%{tx.critical_anomaly_score}',\
    setvar:'tx.anomaly_score_pl1=+%{tx.critical_anomaly_score}'"


SecRule ARGS_GET_NAMES|ARGS_GET "@rx (?:\n|\r)+(?:\s|location|refresh|(?:set-)?cookie|(?:x-)?(?:forwarded-(?:for|host|server)|host|via|remote-ip|remote-addr|originating-IP))\s*:" \
    "id:921160,\
    phase:1,\
    block,\
    capture,\
    t:none,t:urlDecodeUni,t:htmlEntityDecode,t:lowercase,\
    msg:'通过负载进行 HTTP 标头注入攻击（检测到 CR/LF 和标头名称）',\
    logdata:'Matched Data: %{TX.0} found within %{MATCHED_VAR_NAME}: %{MATCHED_VAR}',\
    tag:'application-multi',\
    tag:'language-multi',\
    tag:'platform-multi',\
    tag:'attack-protocol',\
    tag:'OWASP_CRS',\
    tag:'OWASP_CRS/WEB_ATTACK/HEADER_INJECTION',\
    ctl:auditLogParts=+E,\
    ver:'OWASP_CRS/3.2.0',\
    severity:'CRITICAL',\
    setvar:'tx.http_violation_score=+%{tx.critical_anomaly_score}',\
    setvar:'tx.anomaly_score_pl1=+%{tx.critical_anomaly_score}'"



SecRule TX:EXECUTING_PARANOIA_LEVEL "@lt 2" "id:921013,phase:1,pass,nolog,skipAfter:END-REQUEST-921-PROTOCOL-ATTACK"
SecRule TX:EXECUTING_PARANOIA_LEVEL "@lt 2" "id:921014,phase:2,pass,nolog,skipAfter:END-REQUEST-921-PROTOCOL-ATTACK"
#
# -= Paranoia Level 2 =- (apply only when tx.executing_paranoia_level is sufficiently high: 2 or higher)
#


# Detect newlines in GET argument values.
# These may point to a HTTP header injection attack, but can also sometimes
# occur in benign query parameters.
#
# See also: rule 921140, 921150
#
SecRule ARGS_GET "@rx [\n\r]" \
    "id:921151,\
    phase:2,\
    block,\
    capture,\
    t:none,t:urlDecodeUni,t:htmlEntityDecode,\
    msg:'通过有效负载的 HTTP 标头注入攻击（检测到 CR/LF）',\
    logdata:'Matched Data: %{TX.0} found within %{MATCHED_VAR_NAME}: %{MATCHED_VAR}',\
    tag:'application-multi',\
    tag:'language-multi',\
    tag:'platform-multi',\
    tag:'attack-protocol',\
    tag:'paranoia-level/2',\
    tag:'OWASP_CRS',\
    tag:'OWASP_CRS/WEB_ATTACK/HEADER_INJECTION',\
    ctl:auditLogParts=+E,\
    ver:'OWASP_CRS/3.2.0',\
    severity:'CRITICAL',\
    setvar:'tx.http_violation_score=+%{tx.critical_anomaly_score}',\
    setvar:'tx.anomaly_score_pl2=+%{tx.critical_anomaly_score}'"


SecRule TX:EXECUTING_PARANOIA_LEVEL "@lt 3" "id:921015,phase:1,pass,nolog,skipAfter:END-REQUEST-921-PROTOCOL-ATTACK"
SecRule TX:EXECUTING_PARANOIA_LEVEL "@lt 3" "id:921016,phase:2,pass,nolog,skipAfter:END-REQUEST-921-PROTOCOL-ATTACK"
#
# -= Paranoia Level 3 =- (apply only when tx.executing_paranoia_level is sufficiently high: 3 or higher)
#
#

# -=[ HTTP Parameter Polution ]=-
#
# [ Rule Logic ]
# These rules look for multiple parameters with the same name.
# 921170 counts the occurrences of the individual parameters.
# 921180 checks if any counter is > 1.
#
# One HPP attack vector is to try evade signature filters by distributing the
# attack payload across multiple parameters with the same name.
# This works as many security devices only apply signatures to individual
# parameter payloads, however the back-end web application may (in the case
# of ASP.NET) consolidate all of the payloads into one thus making the
# attack payload active.
#
# [ References ]
# http://tacticalwebappsec.blogspot.com/2009/05/http-parameter-pollution.html
# https://capec.mitre.org/data/definitions/460.html
#
SecRule ARGS_NAMES "@rx ." \
    "id:921170,\
    phase:2,\
    pass,\
    nolog,\
    tag:'application-multi',\
    tag:'language-multi',\
    tag:'platform-multi',\
    tag:'attack-protocol',\
    tag:'paranoia-level/3',\
    tag:'CAPEC-460',\
    ver:'OWASP_CRS/3.2.0',\
    setvar:'TX.paramcounter_%{MATCHED_VAR_NAME}=+1'"

SecRule TX:/paramcounter_.*/ "@gt 1" \
    "id:921180,\
    phase:2,\
    pass,\
    msg:'HTTP 参数污染 (%{TX.1})',\
    logdata:'Matched Data: %{TX.0} found within %{MATCHED_VAR_NAME}: %{MATCHED_VAR}',\
    tag:'application-multi',\
    tag:'language-multi',\
    tag:'platform-multi',\
    tag:'attack-protocol',\
    tag:'OWASP_CRS',\
    tag:'OWASP_CRS/WEB_ATTACK/HTTP_PARAMETER_POLLUTION',\
    tag:'paranoia-level/3',\
    tag:'CAPEC-460',\
    ver:'OWASP_CRS/3.2.0',\
    severity:'CRITICAL',\
    chain"
    SecRule MATCHED_VARS_NAMES "@rx TX:paramcounter_(.*)" \
        "capture,\
        setvar:'tx.http_violation_score=+%{tx.critical_anomaly_score}',\
        setvar:'tx.anomaly_score_pl3=+%{tx.critical_anomaly_score}'"



SecRule TX:EXECUTING_PARANOIA_LEVEL "@lt 4" "id:921017,phase:1,pass,nolog,skipAfter:END-REQUEST-921-PROTOCOL-ATTACK"
SecRule TX:EXECUTING_PARANOIA_LEVEL "@lt 4" "id:921018,phase:2,pass,nolog,skipAfter:END-REQUEST-921-PROTOCOL-ATTACK"
#
# -= Paranoia Level 4 =- (apply only when tx.executing_paranoia_level is sufficiently high: 4 or higher)
#



#
# -= Paranoia Levels Finished =-
#
SecMarker "END-REQUEST-921-PROTOCOL-ATTACK"
