###
# ------------------------------------------------------------------------
# OWASP ModSecurity Core Rule Set ver.3.2.0
# Copyright (c) 2006-2019 Trustwave and contributors. All rights reserved.
#
# The OWASP ModSecurity Core Rule Set is distributed under
# Apache Software License (ASL) version 2
# Please see the enclosed LICENSE file for full details.
# ------------------------------------------------------------------------


# This file is used as an exception mechanism to remove common false positives
# that may be encountered.
#
# Exception for Apache SSL pinger
#
SecRule REQUEST_LINE "@streq GET /" \
    "id:905100,\
    phase:1,\
    pass,\
    t:none,\
    nolog,\
    tag:'application-multi',\
    tag:'language-multi',\
    tag:'platform-apache',\
    tag:'attack-generic',\
    chain"
    SecRule REMOTE_ADDR "@ipMatch 127.0.0.1,::1" \
        "t:none,\
        ctl:ruleEngine=Off,\
        ctl:auditEngine=Off"

#
# Exception for Apache internal dummy connection
#
SecRule REQUEST_LINE "@rx ^(?:GET /|OPTIONS \*) HTTP/[12]\.[01]$" \
    "id:905110,\
    phase:1,\
    pass,\
    t:none,\
    nolog,\
    tag:'application-multi',\
    tag:'language-multi',\
    tag:'platform-apache',\
    tag:'attack-generic',\
    chain"
    SecRule REMOTE_ADDR "@ipMatch 127.0.0.1,::1" \
        "t:none,\
        chain"
        SecRule REQUEST_HEADERS:User-Agent "@rx ^.*\(internal dummy connection\)$" \
            "t:none,\
            ctl:ruleEngine=Off,\
            ctl:auditEngine=Off"
