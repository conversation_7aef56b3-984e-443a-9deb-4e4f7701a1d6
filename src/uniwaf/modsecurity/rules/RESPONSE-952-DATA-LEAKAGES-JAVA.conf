###
# ------------------------------------------------------------------------
# OWASP ModSecurity Core Rule Set ver.3.2.0
# Copyright (c) 2006-2019 Trustwave and contributors. All rights reserved.
#
# The OWASP ModSecurity Core Rule Set is distributed under
# Apache Software License (ASL) version 2
# Please see the enclosed LICENSE file for full details.
# ------------------------------------------------------------------------

#
# -= Paranoia Level 0 (empty) =- (apply unconditionally)
#



SecRule TX:EXECUTING_PARANOIA_LEVEL "@lt 1" "id:952011,phase:3,pass,nolog,skipAfter:END-RESPONSE-952-DATA-LEAKAGES-JAVA"
SecRule TX:EXECUTING_PARANOIA_LEVEL "@lt 1" "id:952012,phase:4,pass,nolog,skipAfter:END-RESPONSE-952-DATA-LEAKAGES-JAVA"
#
# -= Paranoia Level 1 (default) =- (apply only when tx.executing_paranoia_level is sufficiently high: 1 or higher)
#

#
# -=[ Java Source Code Leakages ]=-
#
SecRule RESPONSE_BODY "@pmFromFile java-code-leakages.data" \
    "id:952100,\
    phase:4,\
    block,\
    capture,\
    t:none,\
    msg:'Java源代码泄漏',\
    logdata:'Matched Data: %{TX.0} found within %{MATCHED_VAR_NAME}: %{MATCHED_VAR}',\
    tag:'application-multi',\
    tag:'language-java',\
    tag:'platform-multi',\
    tag:'attack-disclosure',\
    tag:'OWASP_CRS',\
    tag:'OWASP_CRS/LEAKAGE/SOURCE_CODE_JAVA',\
    tag:'WASCTC/WASC-13',\
    tag:'OWASP_TOP_10/A6',\
    tag:'PCI/6.5.6',\
    ctl:auditLogParts=+E,\
    ver:'OWASP_CRS/3.2.0',\
    severity:'ERROR',\
    setvar:'tx.outbound_anomaly_score_pl1=+%{tx.error_anomaly_score}',\
    setvar:'tx.anomaly_score_pl1=+%{tx.error_anomaly_score}'"

#
# -=[ Java Errors ]=-
#
# Ref: https://github.com/andresriancho/w3af/blob/master/w3af/plugins/grep/error_pages.py
#
SecRule RESPONSE_BODY "@pmFromFile java-errors.data" \
    "id:952110,\
    phase:4,\
    block,\
    capture,\
    t:none,\
    msg:'Java错误',\
    logdata:'Matched Data: %{TX.0} found within %{MATCHED_VAR_NAME}: %{MATCHED_VAR}',\
    tag:'application-multi',\
    tag:'language-java',\
    tag:'platform-multi',\
    tag:'attack-disclosure',\
    tag:'OWASP_CRS',\
    tag:'OWASP_CRS/LEAKAGE/ERRORS_JAVA',\
    tag:'WASCTC/WASC-13',\
    tag:'OWASP_TOP_10/A6',\
    tag:'PCI/6.5.6',\
    ctl:auditLogParts=+E,\
    ver:'OWASP_CRS/3.2.0',\
    severity:'ERROR',\
    setvar:'tx.outbound_anomaly_score_pl1=+%{tx.error_anomaly_score}',\
    setvar:'tx.anomaly_score_pl1=+%{tx.error_anomaly_score}'"



SecRule TX:EXECUTING_PARANOIA_LEVEL "@lt 2" "id:952013,phase:3,pass,nolog,skipAfter:END-RESPONSE-952-DATA-LEAKAGES-JAVA"
SecRule TX:EXECUTING_PARANOIA_LEVEL "@lt 2" "id:952014,phase:4,pass,nolog,skipAfter:END-RESPONSE-952-DATA-LEAKAGES-JAVA"
#
# -= Paranoia Level 2 =- (apply only when tx.executing_paranoia_level is sufficiently high: 2 or higher)
#



SecRule TX:EXECUTING_PARANOIA_LEVEL "@lt 3" "id:952015,phase:3,pass,nolog,skipAfter:END-RESPONSE-952-DATA-LEAKAGES-JAVA"
SecRule TX:EXECUTING_PARANOIA_LEVEL "@lt 3" "id:952016,phase:4,pass,nolog,skipAfter:END-RESPONSE-952-DATA-LEAKAGES-JAVA"
#
# -= Paranoia Level 3 =- (apply only when tx.executing_paranoia_level is sufficiently high: 3 or higher)
#



SecRule TX:EXECUTING_PARANOIA_LEVEL "@lt 4" "id:952017,phase:3,pass,nolog,skipAfter:END-RESPONSE-952-DATA-LEAKAGES-JAVA"
SecRule TX:EXECUTING_PARANOIA_LEVEL "@lt 4" "id:952018,phase:4,pass,nolog,skipAfter:END-RESPONSE-952-DATA-LEAKAGES-JAVA"
#
# -= Paranoia Level 4 =- (apply only when tx.executing_paranoia_level is sufficiently high: 4 or higher)
#



#
# -= Paranoia Levels Finished =-
#
SecMarker "END-RESPONSE-952-DATA-LEAKAGES-JAVA"
