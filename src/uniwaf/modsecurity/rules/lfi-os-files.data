# Apache
# (no slash; also guards against old.htaccess, old.htpasswd, etc.)
.htaccess
.htdigest
.htpasswd
# dotfiles (keep in sync with restricted-files.data)
.aptitude/config
.bash_config
.bash_history
.bash_logout
.bash_profile
.bashrc
.cache/notify-osd.log
.config/odesk/odesk team.conf
.cshrc
.drush/
.gitconfig
.gnupg/
.hplip/hplip.conf
.ksh_history
.lesshst
.lftp/
.lhistory
.lldb-history
.local/share/mc/
.my.cnf
.mysql_history
.nano_history
.node_repl_history
.nsr
.pearrc
.php_history
.pki/
.profile
.psql_history
.python_history
.rediscli_history
.Rhistory
.sh_history
.sqlite_history
.ssh/authorized_keys
.ssh/config
.ssh/id_dsa
.ssh/id_dsa.pub
.ssh/id_rsa
.ssh/id_rsa.pub
.ssh/identity
.ssh/identity.pub
.ssh/known_hosts
.subversion/auth
.subversion/config
.subversion/servers
.tconn/tconn.conf
.tcshrc
.vidalia/vidalia.conf
.viminfo
.vimrc
.xauthority
.zhistory
.zshrc
.zsh_history
etc/redis.conf
etc/redis-sentinel.conf
etc/php.ini
bin/php.ini
etc/httpd/php.ini
usr/lib/php.ini
usr/lib/php/php.ini
usr/local/etc/php.ini
usr/local/lib/php.ini
usr/local/php/lib/php.ini
usr/local/php4/lib/php.ini
usr/local/php5/lib/php.ini
usr/local/apache/conf/php.ini
etc/php4.4/fcgi/php.ini
etc/php4/apache/php.ini
etc/php4/apache2/php.ini
etc/php5/apache/php.ini
etc/php5/apache2/php.ini
etc/php/php.ini
etc/php/php4/php.ini
etc/php/apache/php.ini
etc/php/apache2/php.ini
web/conf/php.ini
usr/local/zend/etc/php.ini
opt/xampp/etc/php.ini
var/local/www/conf/php.ini
etc/php/cgi/php.ini
etc/php4/cgi/php.ini
etc/php5/cgi/php.ini
home2/bin/stable/apache/php.ini
home/bin/stable/apache/php.ini
etc/httpd/conf.d/php.conf
php5/php.ini
php4/php.ini
php/php.ini
windows/php.ini
winnt/php.ini
apache/php/php.ini
xampp/apache/bin/php.ini
netserver/bin/stable/apache/php.ini
volumes/macintosh_hd1/usr/local/php/lib/php.ini
etc/mono/1.0/machine.config
etc/mono/2.0/machine.config
etc/mono/2.0/web.config
etc/mono/config
usr/local/cpanel/logs/stats_log
usr/local/cpanel/logs/access_log
usr/local/cpanel/logs/error_log
usr/local/cpanel/logs/license_log
usr/local/cpanel/logs/login_log
var/cpanel/cpanel.config
var/log/sw-cp-server/error_log
usr/local/psa/admin/logs/httpsd_access_log
usr/local/psa/admin/logs/panel.log
var/log/sso/sso.log
usr/local/psa/admin/conf/php.ini
etc/sw-cp-server/applications.d/plesk.conf
usr/local/psa/admin/conf/site_isolation_settings.ini
usr/local/sb/config
etc/sw-cp-server/applications.d/00-sso-cpserver.conf
etc/sso/sso_config.ini
etc/mysql/conf.d/old_passwords.cnf
var/log/mysql/mysql-bin.log
var/log/mysql/mysql-bin.index
var/log/mysql/data/mysql-bin.index
var/log/mysql.log
var/log/mysql.err
var/log/mysqlderror.log
var/log/mysql/mysql.log
var/log/mysql/mysql-slow.log
var/log/mysql-bin.index
var/log/data/mysql-bin.index
var/mysql.log
var/mysql-bin.index
var/data/mysql-bin.index
program files/mysql/mysql server 5.0/data/{host}.err
program files/mysql/mysql server 5.0/data/mysql.log
program files/mysql/mysql server 5.0/data/mysql.err
program files/mysql/mysql server 5.0/data/mysql-bin.log
program files/mysql/mysql server 5.0/data/mysql-bin.index
program files/mysql/data/{host}.err
program files/mysql/data/mysql.log
program files/mysql/data/mysql.err
program files/mysql/data/mysql-bin.log
program files/mysql/data/mysql-bin.index
mysql/data/{host}.err
mysql/data/mysql.log
mysql/data/mysql.err
mysql/data/mysql-bin.log
mysql/data/mysql-bin.index
usr/local/mysql/data/mysql.log
usr/local/mysql/data/mysql.err
usr/local/mysql/data/mysql-bin.log
usr/local/mysql/data/mysql-slow.log
usr/local/mysql/data/mysqlderror.log
usr/local/mysql/data/{host}.err
usr/local/mysql/data/mysql-bin.index
var/lib/mysql/my.cnf
etc/mysql/my.cnf
etc/my.cnf
program files/mysql/mysql server 5.0/my.ini
program files/mysql/mysql server 5.0/my.cnf
program files/mysql/my.ini
program files/mysql/my.cnf
mysql/my.ini
mysql/my.cnf
mysql/bin/my.ini
var/postgresql/log/postgresql.log
var/log/postgresql/postgresql.log
var/log/postgres/pg_backup.log
var/log/postgres/postgres.log
var/log/postgresql.log
var/log/pgsql/pgsql.log
var/log/postgresql/postgresql-8.1-main.log
var/log/postgresql/postgresql-8.3-main.log
var/log/postgresql/postgresql-8.4-main.log
var/log/postgresql/postgresql-9.0-main.log
var/log/postgresql/postgresql-9.1-main.log
var/log/pgsql8.log
var/log/postgresql/postgres.log
var/log/pgsql_log
var/log/postgresql/main.log
var/log/cron/var/log/postgres.log
usr/internet/pgsql/data/postmaster.log
usr/local/pgsql/data/postgresql.log
usr/local/pgsql/data/pg_log
postgresql/log/pgadmin.log
var/lib/pgsql/data/postgresql.conf
var/postgresql/db/postgresql.conf
var/nm2/postgresql.conf
usr/local/pgsql/data/postgresql.conf
usr/local/pgsql/data/pg_hba.conf
usr/internet/pgsql/data/pg_hba.conf
usr/local/pgsql/data/passwd
usr/local/pgsql/bin/pg_passwd
etc/postgresql/postgresql.conf
etc/postgresql/pg_hba.conf
home/postgres/data/postgresql.conf
home/postgres/data/pg_version
home/postgres/data/pg_ident.conf
home/postgres/data/pg_hba.conf
program files/postgresql/8.3/data/pg_hba.conf
program files/postgresql/8.3/data/pg_ident.conf
program files/postgresql/8.3/data/postgresql.conf
program files/postgresql/8.4/data/pg_hba.conf
program files/postgresql/8.4/data/pg_ident.conf
program files/postgresql/8.4/data/postgresql.conf
program files/postgresql/9.0/data/pg_hba.conf
program files/postgresql/9.0/data/pg_ident.conf
program files/postgresql/9.0/data/postgresql.conf
program files/postgresql/9.1/data/pg_hba.conf
program files/postgresql/9.1/data/pg_ident.conf
program files/postgresql/9.1/data/postgresql.conf
wamp/logs/access.log
wamp/logs/apache_error.log
wamp/logs/genquery.log
wamp/logs/mysql.log
wamp/logs/slowquery.log
wamp/bin/apache/apache2.2.22/logs/access.log
wamp/bin/apache/apache2.2.22/logs/error.log
wamp/bin/apache/apache2.2.21/logs/access.log
wamp/bin/apache/apache2.2.21/logs/error.log
wamp/bin/mysql/mysql5.5.24/data/mysql-bin.index
wamp/bin/mysql/mysql5.5.16/data/mysql-bin.index
wamp/bin/apache/apache2.2.21/conf/httpd.conf
wamp/bin/apache/apache2.2.22/conf/httpd.conf
wamp/bin/apache/apache2.2.21/wampserver.conf
wamp/bin/apache/apache2.2.22/wampserver.conf
wamp/bin/apache/apache2.2.22/conf/wampserver.conf
wamp/bin/mysql/mysql5.5.24/my.ini
wamp/bin/mysql/mysql5.5.24/wampserver.conf
wamp/bin/mysql/mysql5.5.16/my.ini
wamp/bin/mysql/mysql5.5.16/wampserver.conf
wamp/bin/php/php5.3.8/php.ini
wamp/bin/php/php5.4.3/php.ini
xampp/apache/logs/access.log
xampp/apache/logs/error.log
xampp/mysql/data/mysql-bin.index
xampp/mysql/data/mysql.err
xampp/mysql/data/{host}.err
xampp/sendmail/sendmail.log
xampp/apache/conf/httpd.conf
xampp/filezillaftp/filezilla server.xml
xampp/mercurymail/mercury.ini
xampp/php/php.ini
xampp/phpmyadmin/config.inc.php
xampp/sendmail/sendmail.ini
xampp/webalizer/webalizer.conf
opt/lampp/etc/httpd.conf
xampp/htdocs/aca.txt
xampp/htdocs/admin.php
xampp/htdocs/leer.txt
usr/local/apache/logs/audit_log
usr/local/apache2/logs/audit_log
logs/security_debug_log
logs/security_log
usr/local/apache/conf/modsec.conf
usr/local/apache2/conf/modsec.conf
winnt/system32/logfiles/msftpsvc
winnt/system32/logfiles/msftpsvc1
winnt/system32/logfiles/msftpsvc2
windows/system32/logfiles/msftpsvc
windows/system32/logfiles/msftpsvc1
windows/system32/logfiles/msftpsvc2
etc/logrotate.d/proftpd
www/logs/proftpd.system.log
var/log/proftpd
var/log/proftpd/xferlog.legacy
var/log/proftpd.access_log
var/log/proftpd.xferlog
etc/pam.d/proftpd
etc/proftp.conf
etc/protpd/proftpd.conf
etc/vhcs2/proftpd/proftpd.conf
etc/proftpd/modules.conf
var/log/vsftpd.log
etc/vsftpd.chroot_list
etc/logrotate.d/vsftpd.log
etc/vsftpd/vsftpd.conf
etc/vsftpd.conf
etc/chrootusers
var/log/xferlog
var/adm/log/xferlog
etc/wu-ftpd/ftpaccess
etc/wu-ftpd/ftphosts
etc/wu-ftpd/ftpusers
var/log/pure-ftpd/pure-ftpd.log
logs/pure-ftpd.log
var/log/pureftpd.log
usr/sbin/pure-config.pl
usr/etc/pure-ftpd.conf
etc/pure-ftpd/pure-ftpd.conf
usr/local/etc/pure-ftpd.conf
usr/local/etc/pureftpd.pdb
usr/local/pureftpd/etc/pureftpd.pdb
usr/local/pureftpd/sbin/pure-config.pl
usr/local/pureftpd/etc/pure-ftpd.conf
etc/pure-ftpd.conf
etc/pure-ftpd/pure-ftpd.pdb
etc/pureftpd.pdb
etc/pureftpd.passwd
etc/pure-ftpd/pureftpd.pdb
usr/ports/ftp/pure-ftpd/pure-ftpd.conf
usr/ports/ftp/pure-ftpd/pureftpd.pdb
usr/ports/ftp/pure-ftpd/pureftpd.passwd
usr/ports/net/pure-ftpd/pure-ftpd.conf
usr/ports/net/pure-ftpd/pureftpd.pdb
usr/ports/net/pure-ftpd/pureftpd.passwd
usr/pkgsrc/net/pureftpd/pure-ftpd.conf
usr/pkgsrc/net/pureftpd/pureftpd.pdb
usr/pkgsrc/net/pureftpd/pureftpd.passwd
usr/ports/contrib/pure-ftpd/pure-ftpd.conf
usr/ports/contrib/pure-ftpd/pureftpd.pdb
usr/ports/contrib/pure-ftpd/pureftpd.passwd
var/log/muddleftpd
usr/sbin/mudlogd
etc/muddleftpd/mudlog
etc/muddleftpd.com
etc/muddleftpd/mudlogd.conf
etc/muddleftpd/muddleftpd.conf
var/log/muddleftpd.conf
usr/sbin/mudpasswd
etc/muddleftpd/muddleftpd.passwd
etc/muddleftpd/passwd
var/log/ftp-proxy/ftp-proxy.log
var/log/ftp-proxy
var/log/ftplog
etc/logrotate.d/ftp
etc/ftpchroot
etc/ftphosts
etc/ftpusers
var/log/exim_mainlog
var/log/exim/mainlog
var/log/maillog
var/log/exim_paniclog
var/log/exim/paniclog
var/log/exim/rejectlog
var/log/exim_rejectlog
winnt/system32/logfiles/smtpsvc
winnt/system32/logfiles/smtpsvc1
winnt/system32/logfiles/smtpsvc2
winnt/system32/logfiles/smtpsvc3
winnt/system32/logfiles/smtpsvc4
winnt/system32/logfiles/smtpsvc5
windows/system32/logfiles/smtpsvc
windows/system32/logfiles/smtpsvc1
windows/system32/logfiles/smtpsvc2
windows/system32/logfiles/smtpsvc3
windows/system32/logfiles/smtpsvc4
windows/system32/logfiles/smtpsvc5
etc/osxhttpd/osxhttpd.conf
system/library/webobjects/adaptors/apache2.2/apache.conf
etc/apache2/sites-available/default
etc/apache2/sites-available/default-ssl
etc/apache2/sites-enabled/000-default
etc/apache2/sites-enabled/default
etc/apache2/apache2.conf
etc/apache2/ports.conf
usr/local/etc/apache/httpd.conf
usr/pkg/etc/httpd/httpd.conf
usr/pkg/etc/httpd/httpd-default.conf
usr/pkg/etc/httpd/httpd-vhosts.conf
etc/httpd/mod_php.conf
etc/httpd/extra/httpd-ssl.conf
etc/rc.d/rc.httpd
usr/local/apache/conf/httpd.conf.default
usr/local/apache/conf/access.conf
usr/local/apache22/conf/httpd.conf
usr/local/apache22/httpd.conf
usr/local/etc/apache22/conf/httpd.conf
usr/local/apps/apache22/conf/httpd.conf
etc/apache22/conf/httpd.conf
etc/apache22/httpd.conf
opt/apache22/conf/httpd.conf
usr/local/etc/apache2/vhosts.conf
usr/local/apache/conf/vhosts.conf
usr/local/apache2/conf/vhosts.conf
usr/local/apache/conf/vhosts-custom.conf
usr/local/apache2/conf/vhosts-custom.conf
etc/apache/default-server.conf
etc/apache2/default-server.conf
usr/local/apache2/conf/extra/httpd-ssl.conf
usr/local/apache2/conf/ssl.conf
etc/httpd/conf.d
usr/local/etc/apache22/httpd.conf
usr/local/etc/apache2/httpd.conf
etc/apache2/httpd2.conf
etc/apache2/ssl-global.conf
etc/apache2/vhosts.d/00_default_vhost.conf
apache/conf/httpd.conf
etc/apache/httpd.conf
etc/httpd/conf
http/httpd.conf
usr/local/apache1.3/conf/httpd.conf
usr/local/etc/httpd/conf
var/apache/conf/httpd.conf
var/www/conf
www/apache/conf/httpd.conf
www/conf/httpd.conf
etc/init.d
etc/apache/access.conf
etc/rc.conf
www/logs/freebsddiary-error.log
www/logs/freebsddiary-access_log
library/webserver/documents/index.html
library/webserver/documents/index.htm
library/webserver/documents/default.html
library/webserver/documents/default.htm
library/webserver/documents/index.php
library/webserver/documents/default.php
var/log/webmin/miniserv.log
usr/local/etc/webmin/miniserv.conf
etc/webmin/miniserv.conf
usr/local/etc/webmin/miniserv.users
etc/webmin/miniserv.users
winnt/system32/logfiles/w3svc/inetsvn1.log
winnt/system32/logfiles/w3svc1/inetsvn1.log
winnt/system32/logfiles/w3svc2/inetsvn1.log
winnt/system32/logfiles/w3svc3/inetsvn1.log
windows/system32/logfiles/w3svc/inetsvn1.log
windows/system32/logfiles/w3svc1/inetsvn1.log
windows/system32/logfiles/w3svc2/inetsvn1.log
windows/system32/logfiles/w3svc3/inetsvn1.log
var/log/httpd/access_log
var/log/httpd/error_log
apache/logs/error.log
apache/logs/access.log
apache2/logs/error.log
apache2/logs/access.log
logs/error.log
logs/access.log
etc/httpd/logs/access_log
etc/httpd/logs/access.log
etc/httpd/logs/error_log
etc/httpd/logs/error.log
usr/local/apache/logs/access_log
usr/local/apache/logs/access.log
usr/local/apache/logs/error_log
usr/local/apache/logs/error.log
usr/local/apache2/logs/access_log
usr/local/apache2/logs/access.log
usr/local/apache2/logs/error_log
usr/local/apache2/logs/error.log
var/www/logs/access_log
var/www/logs/access.log
var/www/logs/error_log
var/www/logs/error.log
var/log/httpd/access.log
var/log/httpd/error.log
var/log/apache/access_log
var/log/apache/access.log
var/log/apache/error_log
var/log/apache/error.log
var/log/apache2/access_log
var/log/apache2/access.log
var/log/apache2/error_log
var/log/apache2/error.log
var/log/access_log
var/log/access.log
var/log/error_log
var/log/error.log
opt/lampp/logs/access_log
opt/lampp/logs/error_log
opt/xampp/logs/access_log
opt/xampp/logs/error_log
opt/lampp/logs/access.log
opt/lampp/logs/error.log
opt/xampp/logs/access.log
opt/xampp/logs/error.log
program files/apache group/apache/logs/access.log
program files/apache group/apache/logs/error.log
program files/apache software foundation/apache2.2/logs/error.log
program files/apache software foundation/apache2.2/logs/access.log
opt/apache/apache.conf
opt/apache/conf/apache.conf
opt/apache2/apache.conf
opt/apache2/conf/apache.conf
opt/httpd/apache.conf
opt/httpd/conf/apache.conf
etc/httpd/apache.conf
etc/apache2/apache.conf
etc/httpd/conf/apache.conf
usr/local/apache/apache.conf
usr/local/apache/conf/apache.conf
usr/local/apache2/apache.conf
usr/local/apache2/conf/apache.conf
usr/local/php/apache.conf.php
usr/local/php4/apache.conf.php
usr/local/php5/apache.conf.php
usr/local/php/apache.conf
usr/local/php4/apache.conf
usr/local/php5/apache.conf
private/etc/httpd/apache.conf
opt/apache/apache2.conf
opt/apache/conf/apache2.conf
opt/apache2/apache2.conf
opt/apache2/conf/apache2.conf
opt/httpd/apache2.conf
opt/httpd/conf/apache2.conf
etc/httpd/apache2.conf
etc/httpd/conf/apache2.conf
usr/local/apache/apache2.conf
usr/local/apache/conf/apache2.conf
usr/local/apache2/apache2.conf
usr/local/apache2/conf/apache2.conf
usr/local/php/apache2.conf.php
usr/local/php4/apache2.conf.php
usr/local/php5/apache2.conf.php
usr/local/php/apache2.conf
usr/local/php4/apache2.conf
usr/local/php5/apache2.conf
private/etc/httpd/apache2.conf
usr/local/apache/conf/httpd.conf
usr/local/apache2/conf/httpd.conf
etc/httpd/conf/httpd.conf
etc/apache/apache.conf
etc/apache/conf/httpd.conf
etc/apache2/httpd.conf
usr/apache2/conf/httpd.conf
usr/apache/conf/httpd.conf
usr/local/etc/apache/conf/httpd.conf
usr/local/apache/httpd.conf
usr/local/apache2/httpd.conf
usr/local/httpd/conf/httpd.conf
usr/local/etc/apache2/conf/httpd.conf
usr/local/etc/httpd/conf/httpd.conf
usr/local/apps/apache2/conf/httpd.conf
usr/local/apps/apache/conf/httpd.conf
usr/local/php/httpd.conf.php
usr/local/php4/httpd.conf.php
usr/local/php5/httpd.conf.php
usr/local/php/httpd.conf
usr/local/php4/httpd.conf
usr/local/php5/httpd.conf
etc/apache2/conf/httpd.conf
etc/http/conf/httpd.conf
etc/httpd/httpd.conf
etc/http/httpd.conf
etc/httpd.conf
opt/apache/conf/httpd.conf
opt/apache2/conf/httpd.conf
var/www/conf/httpd.conf
private/etc/httpd/httpd.conf
private/etc/httpd/httpd.conf.default
etc/apache2/vhosts.d/default_vhost.include
etc/apache2/conf.d/charset
etc/apache2/conf.d/security
etc/apache2/envvars
etc/apache2/mods-available/autoindex.conf
etc/apache2/mods-available/deflate.conf
etc/apache2/mods-available/dir.conf
etc/apache2/mods-available/mem_cache.conf
etc/apache2/mods-available/mime.conf
etc/apache2/mods-available/proxy.conf
etc/apache2/mods-available/setenvif.conf
etc/apache2/mods-available/ssl.conf
etc/apache2/mods-enabled/alias.conf
etc/apache2/mods-enabled/deflate.conf
etc/apache2/mods-enabled/dir.conf
etc/apache2/mods-enabled/mime.conf
etc/apache2/mods-enabled/negotiation.conf
etc/apache2/mods-enabled/php5.conf
etc/apache2/mods-enabled/status.conf
program files/apache group/apache/conf/httpd.conf
program files/apache group/apache2/conf/httpd.conf
program files/xampp/apache/conf/apache.conf
program files/xampp/apache/conf/apache2.conf
program files/xampp/apache/conf/httpd.conf
program files/apache group/apache/apache.conf
program files/apache group/apache/conf/apache.conf
program files/apache group/apache2/conf/apache.conf
program files/apache group/apache/apache2.conf
program files/apache group/apache/conf/apache2.conf
program files/apache group/apache2/conf/apache2.conf
program files/apache software foundation/apache2.2/conf/httpd.conf
volumes/macintosh_hd1/opt/httpd/conf/httpd.conf
volumes/macintosh_hd1/opt/apache/conf/httpd.conf
volumes/macintosh_hd1/opt/apache2/conf/httpd.conf
volumes/macintosh_hd1/usr/local/php/httpd.conf.php
volumes/macintosh_hd1/usr/local/php4/httpd.conf.php
volumes/macintosh_hd1/usr/local/php5/httpd.conf.php
volumes/webbackup/opt/apache2/conf/httpd.conf
volumes/webbackup/private/etc/httpd/httpd.conf
volumes/webbackup/private/etc/httpd/httpd.conf.default
usr/local/etc/apache/vhosts.conf
usr/local/jakarta/tomcat/conf/jakarta.conf
usr/local/jakarta/tomcat/conf/server.xml
usr/local/jakarta/tomcat/conf/context.xml
usr/local/jakarta/tomcat/conf/workers.properties
usr/local/jakarta/tomcat/conf/logging.properties
usr/local/jakarta/dist/tomcat/conf/jakarta.conf
usr/local/jakarta/dist/tomcat/conf/server.xml
usr/local/jakarta/dist/tomcat/conf/context.xml
usr/local/jakarta/dist/tomcat/conf/workers.properties
usr/local/jakarta/dist/tomcat/conf/logging.properties
usr/share/tomcat6/conf/server.xml
usr/share/tomcat6/conf/context.xml
usr/share/tomcat6/conf/workers.properties
usr/share/tomcat6/conf/logging.properties
var/log/tomcat6/catalina.out
var/cpanel/tomcat.options
usr/local/jakarta/tomcat/logs/catalina.out
usr/local/jakarta/tomcat/logs/catalina.err
opt/tomcat/logs/catalina.out
opt/tomcat/logs/catalina.err
usr/share/logs/catalina.out
usr/share/logs/catalina.err
usr/share/tomcat/logs/catalina.out
usr/share/tomcat/logs/catalina.err
usr/share/tomcat6/logs/catalina.out
usr/share/tomcat6/logs/catalina.err
usr/local/apache/logs/mod_jk.log
usr/local/jakarta/tomcat/logs/mod_jk.log
usr/local/jakarta/dist/tomcat/logs/mod_jk.log
opt/[jboss]/server/default/conf/jboss-minimal.xml
opt/[jboss]/server/default/conf/jboss-service.xml
opt/[jboss]/server/default/conf/jndi.properties
opt/[jboss]/server/default/conf/log4j.xml
opt/[jboss]/server/default/conf/login-config.xml
opt/[jboss]/server/default/conf/standardjaws.xml
opt/[jboss]/server/default/conf/standardjboss.xml
opt/[jboss]/server/default/conf/server.log.properties
opt/[jboss]/server/default/deploy/jboss-logging.xml
usr/local/[jboss]/server/default/conf/jboss-minimal.xml
usr/local/[jboss]/server/default/conf/jboss-service.xml
usr/local/[jboss]/server/default/conf/jndi.properties
usr/local/[jboss]/server/default/conf/log4j.xml
usr/local/[jboss]/server/default/conf/login-config.xml
usr/local/[jboss]/server/default/conf/standardjaws.xml
usr/local/[jboss]/server/default/conf/standardjboss.xml
usr/local/[jboss]/server/default/conf/server.log.properties
usr/local/[jboss]/server/default/deploy/jboss-logging.xml
private/tmp/[jboss]/server/default/conf/jboss-minimal.xml
private/tmp/[jboss]/server/default/conf/jboss-service.xml
private/tmp/[jboss]/server/default/conf/jndi.properties
private/tmp/[jboss]/server/default/conf/log4j.xml
private/tmp/[jboss]/server/default/conf/login-config.xml
private/tmp/[jboss]/server/default/conf/standardjaws.xml
private/tmp/[jboss]/server/default/conf/standardjboss.xml
private/tmp/[jboss]/server/default/conf/server.log.properties
private/tmp/[jboss]/server/default/deploy/jboss-logging.xml
tmp/[jboss]/server/default/conf/jboss-minimal.xml
tmp/[jboss]/server/default/conf/jboss-service.xml
tmp/[jboss]/server/default/conf/jndi.properties
tmp/[jboss]/server/default/conf/log4j.xml
tmp/[jboss]/server/default/conf/login-config.xml
tmp/[jboss]/server/default/conf/standardjaws.xml
tmp/[jboss]/server/default/conf/standardjboss.xml
tmp/[jboss]/server/default/conf/server.log.properties
tmp/[jboss]/server/default/deploy/jboss-logging.xml
program files/[jboss]/server/default/conf/jboss-minimal.xml
program files/[jboss]/server/default/conf/jboss-service.xml
program files/[jboss]/server/default/conf/jndi.properties
program files/[jboss]/server/default/conf/log4j.xml
program files/[jboss]/server/default/conf/login-config.xml
program files/[jboss]/server/default/conf/standardjaws.xml
program files/[jboss]/server/default/conf/standardjboss.xml
program files/[jboss]/server/default/conf/server.log.properties
program files/[jboss]/server/default/deploy/jboss-logging.xml
[jboss]/server/default/conf/jboss-minimal.xml
[jboss]/server/default/conf/jboss-service.xml
[jboss]/server/default/conf/jndi.properties
[jboss]/server/default/conf/log4j.xml
[jboss]/server/default/conf/login-config.xml
[jboss]/server/default/conf/standardjaws.xml
[jboss]/server/default/conf/standardjboss.xml
[jboss]/server/default/conf/server.log.properties
[jboss]/server/default/deploy/jboss-logging.xml
opt/[jboss]/server/default/log/server.log
opt/[jboss]/server/default/log/boot.log
usr/local/[jboss]/server/default/log/server.log
usr/local/[jboss]/server/default/log/boot.log
private/tmp/[jboss]/server/default/log/server.log
private/tmp/[jboss]/server/default/log/boot.log
tmp/[jboss]/server/default/log/server.log
tmp/[jboss]/server/default/log/boot.log
program files/[jboss]/server/default/log/server.log
program files/[jboss]/server/default/log/boot.log
[jboss]/server/default/log/server.log
[jboss]/server/default/log/boot.log
var/log/lighttpd.error.log
var/log/lighttpd.access.log
var/lighttpd.log
var/logs/access.log
var/log/lighttpd/
var/log/lighttpd/error.log
var/log/lighttpd/access.www.log
var/log/lighttpd/error.www.log
var/log/lighttpd/access.log
usr/local/apache2/logs/lighttpd.error.log
usr/local/apache2/logs/lighttpd.log
usr/local/apache/logs/lighttpd.error.log
usr/local/apache/logs/lighttpd.log
usr/local/lighttpd/log/lighttpd.error.log
usr/local/lighttpd/log/access.log
var/log/lighttpd/{domain}/access.log
var/log/lighttpd/{domain}/error.log
usr/home/<USER>/var/log/lighttpd.error.log
usr/home/<USER>/var/log/apache.log
home/user/lighttpd/lighttpd.conf
usr/home/<USER>/lighttpd/lighttpd.conf
etc/lighttpd/lighthttpd.conf
usr/local/etc/lighttpd.conf
usr/local/lighttpd/conf/lighttpd.conf
usr/local/etc/lighttpd.conf.new
var/www/.lighttpdpassword
var/log/nginx/access_log
var/log/nginx/error_log
var/log/nginx/access.log
var/log/nginx/error.log
var/log/nginx.access_log
var/log/nginx.error_log
logs/access_log
logs/error_log
etc/nginx/nginx.conf
usr/local/etc/nginx/nginx.conf
usr/local/nginx/conf/nginx.conf
usr/local/zeus/web/global.cfg
usr/local/zeus/web/log/errors
opt/lsws/conf/httpd_conf.xml
usr/local/lsws/conf/httpd_conf.xml
opt/lsws/logs/error.log
opt/lsws/logs/access.log
usr/local/lsws/logs/error.log
usr/local/logs/access.log
usr/local/samba/lib/log.user
usr/local/logs/samba.log
var/log/samba/log.smbd
var/log/samba/log.nmbd
var/log/samba.log
var/log/samba.log1
var/log/samba.log2
var/log/log.smb
etc/samba/netlogon
etc/smbpasswd
etc/smb.conf
etc/samba/dhcp.conf
etc/samba/smb.conf
etc/samba/samba.conf
etc/samba/smb.conf.user
etc/samba/smbpasswd
etc/samba/smbusers
etc/samba/private/smbpasswd
usr/local/etc/smb.conf
usr/local/samba/lib/smb.conf.user
etc/dhcp3/dhclient.conf
etc/dhcp3/dhcpd.conf
etc/dhcp/dhclient.conf
program files/vidalia bundle/polipo/polipo.conf
etc/tor/tor-tsocks.conf
etc/stunnel/stunnel.conf
etc/tsocks.conf
etc/tinyproxy/tinyproxy.conf
etc/miredo-server.conf
etc/miredo.conf
etc/miredo/miredo-server.conf
etc/miredo/miredo.conf
etc/wicd/dhclient.conf.template.default
etc/wicd/manager-settings.conf
etc/wicd/wired-settings.conf
etc/wicd/wireless-settings.conf
var/log/ipfw.log
var/log/ipfw
var/log/ipfw/ipfw.log
var/log/ipfw.today
etc/ipfw.rules
etc/ipfw.conf
etc/firewall.rules
winnt/system32/logfiles/firewall/pfirewall.log
winnt/system32/logfiles/firewall/pfirewall.log.old
windows/system32/logfiles/firewall/pfirewall.log
windows/system32/logfiles/firewall/pfirewall.log.old
etc/clamav/clamd.conf
etc/clamav/freshclam.conf
etc/x11/xorg.conf
etc/x11/xorg.conf-vesa
etc/x11/xorg.conf-vmware
etc/x11/xorg.conf.beforevmwaretoolsinstall
etc/x11/xorg.conf.orig
etc/bluetooth/input.conf
etc/bluetooth/main.conf
etc/bluetooth/network.conf
etc/bluetooth/rfcomm.conf
proc/self/environ
proc/self/mounts
proc/self/stat
proc/self/status
proc/self/cmdline
proc/self/fd/0
proc/self/fd/1
proc/self/fd/2
proc/self/fd/3
proc/self/fd/4
proc/self/fd/5
proc/self/fd/6
proc/self/fd/7
proc/self/fd/8
proc/self/fd/9
proc/self/fd/10
proc/self/fd/11
proc/self/fd/12
proc/self/fd/13
proc/self/fd/14
proc/self/fd/15
proc/version
proc/devices
proc/cpuinfo
proc/meminfo
proc/net/tcp
proc/net/udp
etc/bash_completion.d/debconf
root/.bash_logout
root/.bash_history
root/.bash_config
root/.bashrc
etc/bash.bashrc
var/adm/syslog
var/adm/sulog
var/adm/utmp
var/adm/utmpx
var/adm/wtmp
var/adm/wtmpx
var/adm/lastlog/username
usr/spool/lp/log
var/adm/lp/lpd-errs
usr/lib/cron/log
var/adm/loginlog
var/adm/pacct
var/adm/dtmp
var/adm/acct/sum/loginlog
var/adm/x0msgs
var/adm/crash/vmcore
var/adm/crash/unix
etc/newsyslog.conf
var/adm/qacct
var/adm/ras/errlog
var/adm/ras/bootlog
var/adm/cron/log
etc/utmp
etc/security/lastlog
etc/security/failedlogin
usr/spool/mqueue/syslog
var/adm/messages
var/adm/aculogs
var/adm/aculog
var/adm/vold.log
var/adm/log/asppp.log
var/log/poplog
var/log/authlog
var/lp/logs/lpsched
var/lp/logs/lpnet
var/lp/logs/requests
var/cron/log
var/saf/_log
var/saf/port/log
var/log/news.all
var/log/news/news.all
var/log/news/news.crit
var/log/news/news.err
var/log/news/news.notice
var/log/news/suck.err
var/log/news/suck.notice
var/log/messages
var/log/messages.1
var/log/user.log
var/log/user.log.1
var/log/auth.log
var/log/pm-powersave.log
var/log/xorg.0.log
var/log/daemon.log
var/log/daemon.log.1
var/log/kern.log
var/log/kern.log.1
var/log/mail.err
var/log/mail.info
var/log/mail.warn
var/log/ufw.log
var/log/boot.log
var/log/syslog
var/log/syslog.1
tmp/access.log
etc/sensors.conf
etc/sensors3.conf
etc/host.conf
etc/pam.conf
etc/resolv.conf
etc/apt/apt.conf
etc/inetd.conf
etc/syslog.conf
etc/sysctl.conf
etc/sysctl.d/10-console-messages.conf
etc/sysctl.d/10-network-security.conf
etc/sysctl.d/10-process-security.conf
etc/sysctl.d/wine.sysctl.conf
etc/security/access.conf
etc/security/group.conf
etc/security/limits.conf
etc/security/namespace.conf
etc/security/pam_env.conf
etc/security/sepermit.conf
etc/security/time.conf
etc/ssh/sshd_config
etc/adduser.conf
etc/deluser.conf
etc/avahi/avahi-daemon.conf
etc/ca-certificates.conf
etc/ca-certificates.conf.dpkg-old
etc/casper.conf
etc/chkrootkit.conf
etc/debconf.conf
etc/dns2tcpd.conf
etc/e2fsck.conf
etc/esound/esd.conf
etc/etter.conf
etc/fuse.conf
etc/foremost.conf
etc/hdparm.conf
etc/kernel-img.conf
etc/kernel-pkg.conf
etc/ld.so.conf
etc/ltrace.conf
etc/mail/sendmail.conf
etc/manpath.config
etc/kbd/config
etc/ldap/ldap.conf
etc/logrotate.conf
etc/mtools.conf
etc/smi.conf
etc/updatedb.conf
etc/pulse/client.conf
usr/share/adduser/adduser.conf
etc/hostname
etc/networks
etc/timezone
etc/modules
etc/passwd
etc/passwd~
etc/passwd-
etc/shadow
etc/shadow~
etc/shadow-
etc/fstab
etc/motd
etc/hosts
etc/group
etc/group-
etc/alias
etc/crontab
etc/crypttab
etc/exports
etc/mtab
etc/hosts.allow
etc/hosts.deny
etc/os-release
etc/password.master
etc/profile
etc/default/grub
etc/resolvconf/update-libc.d/sendmail
etc/inittab
etc/issue
etc/issue.net
etc/login.defs
etc/sudoers
etc/sysconfig/network-scripts/ifcfg-eth0
etc/redhat-release
etc/debian_version
etc/fedora-release
etc/mandrake-release
etc/slackware-release
etc/suse-release
etc/security/group
etc/security/passwd
etc/security/user
etc/security/environ
etc/security/limits
etc/security/opasswd
boot/grub/grub.cfg
boot/grub/menu.lst
root/.ksh_history
root/.xauthority
usr/lib/security/mkuser.default
var/log/squirrelmail.log
var/log/apache2/squirrelmail.log
var/log/apache2/squirrelmail.err.log
var/lib/squirrelmail/prefs/squirrelmail.log
var/log/mail.log
etc/squirrelmail/apache.conf
etc/squirrelmail/config_local.php
etc/squirrelmail/default_pref
etc/squirrelmail/index.php
etc/squirrelmail/config_default.php
etc/squirrelmail/config.php
etc/squirrelmail/filters_setup.php
etc/squirrelmail/sqspell_config.php
etc/squirrelmail/config/config.php
etc/httpd/conf.d/squirrelmail.conf
usr/share/squirrelmail/config/config.php
private/etc/squirrelmail/config/config.php
srv/www/htdos/squirrelmail/config/config.php
var/www/squirrelmail/config/config.php
var/www/html/squirrelmail/config/config.php
var/www/html/squirrelmail-1.2.9/config/config.php
usr/share/squirrelmail/plugins/squirrel_logger/setup.php
usr/local/squirrelmail/www/readme
windows/system32/drivers/etc/hosts
windows/system32/drivers/etc/lmhosts.sam
windows/system32/drivers/etc/networks
windows/system32/drivers/etc/protocol
windows/system32/drivers/etc/services
/boot.ini
windows/debug/netsetup.log
windows/comsetup.log
windows/repair/setup.log
windows/setupact.log
windows/setupapi.log
windows/setuperr.log
windows/updspapi.log
windows/wmsetup.log
windows/windowsupdate.log
windows/odbc.ini
usr/local/psa/admin/htdocs/domains/databases/phpmyadmin/libraries/config.default.php
etc/apache2/conf.d/phpmyadmin.conf
etc/phpmyadmin/config.inc.php
etc/openldap/ldap.conf
etc/cups/acroread.conf
etc/cups/cupsd.conf
etc/cups/cupsd.conf.default
etc/cups/pdftops.conf
etc/cups/printers.conf
windows/system32/macromed/flash/flashinstall.log
windows/system32/macromed/flash/install.log
etc/cvs-cron.conf
etc/cvs-pserver.conf
etc/subversion/config
etc/modprobe.d/vmware-tools.conf
etc/updatedb.conf.beforevmwaretoolsinstall
etc/vmware-tools/config
etc/vmware-tools/tpvmlp.conf
etc/vmware-tools/vmware-tools-libraries.conf
var/log/vmware/hostd.log
var/log/vmware/hostd-1.log
# Wordpress
wp-config.php
wp-config.bak
wp-config.old
wp-config.temp
wp-config.tmp
wp-config.txt
# Symfony
config.yml
config_dev.yml
config_prod.yml
config_test.yml
parameters.yml
routing.yml
security.yml
services.yml
# Drupal
sites/default/default.settings.php
sites/default/settings.php
sites/default/settings.local.php
# Magento
app/etc/local.xml
# Sublime Text
sftp-config.json
# ASP.NET
Web.config
# vBulletin
includes/config.php
# OSCommerce
includes/configure.php
# phpMyAdmin
config.inc.php
# MediaWiki
LocalSettings.php
# MyBB
inc/config.php
# TYPO3
typo3conf/localconf.php
# Laravel
# Note: these entries might be benign in REQUEST_FILENAME
config/app.php
config/custom.php
config/database.php
# Joomla
# Note: this string might be benign in REQUEST_FILENAME
/configuration.php
# phpBB
# Note: this string might be benign in REQUEST_FILENAME
/config.php
#Missing Debian based sensitive directories
var/mail/www-data
etc/network/
etc/init/
#IIS
inetpub/wwwroot/global.asa
system32/inetsrv/config/applicationhost.config
system32/inetsrv/config/administration.config
system32/inetsrv/config/redirection.config
#Windows files
system32/config/default
system32/config/sam
system32/config/system
system32/config/software
winnt/repair/sam._
# Node
package.json
package-lock.json
gruntfile.js
npm-debug.log
ormconfig.json
tsconfig.json
webpack.config.js
yarn.lock