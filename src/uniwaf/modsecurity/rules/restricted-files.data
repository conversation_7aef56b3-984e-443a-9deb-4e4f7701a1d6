# Apache
# (no slash; also guards against old.htaccess, old.htpasswd, etc.)
.htaccess
.htdigest
.htpasswd
# home level dotfiles (keep in sync with lfi-os-files.data)
.aptitude/config
.bash_config
.bash_history
.bash_logout
.bash_profile
.bashrc
.cache/notify-osd.log
.config/odesk/odesk team.conf
.cshrc
.drush/
.gitconfig
.gnupg/
.hplip/hplip.conf
.ksh_history
.lesshst
.lftp/
.lhistory
.lldb-history
.local/share/mc/
.my.cnf
.mysql_history
.nano_history
.node_repl_history
.nsr
.pearrc
.php_history
.pki/
.profile
.psql_history
.python_history
.rediscli_history
.Rhistory
.sh_history
.sqlite_history
.ssh/authorized_keys
.ssh/config
.ssh/id_dsa
.ssh/id_dsa.pub
.ssh/id_rsa
.ssh/id_rsa.pub
.ssh/identity
.ssh/identity.pub
.ssh/known_hosts
.subversion/auth
.subversion/config
.subversion/servers
.tconn/tconn.conf
.tcshrc
.vidalia/vidalia.conf
.viminfo
.vimrc
.xauthority
.zhistory
.zshrc
.zsh_history
.nsconfig
# Version control
/.git/
/.gitignore
/.hg/
/.hgignore
/.svn/
# Wordpress
wp-config.php
wp-config.bak
wp-config.old
wp-config.temp
wp-config.tmp
wp-config.txt
# Symfony
/config/config.yml
/config/config_dev.yml
/config/config_prod.yml
/config/config_test.yml
/config/parameters.yml
/config/routing.yml
/config/security.yml
/config/services.yml
# Drupal
/sites/default/default.settings.php
/sites/default/settings.php
/sites/default/settings.local.php
# Magento
/app/etc/local.xml
# Sublime Text
/sftp-config.json
# ASP.NET
/Web.config
# Node
/package.json
/package-lock.json
/gruntfile.js
/npm-debug.log
/ormconfig.json
/tsconfig.json
/webpack.config.js
/yarn.lock
# Composer
/composer.json
/composer.lock
/packages.json
# dotenv
/.env
# OSX
/.DS_Store
# WS FTP
/.ws_ftp.ini
# common, old network config file
.netrc
# New Top Level dotfiles
.thunderbird/
.vmware/
.kube/
.java/
.anydesk/
.docker/
.npm/
.nvm/
.minikube/
.atom/
.aws/config
.aws/credentials
.cups/
.dbus/
.boto
.gem/
.gnonme/
.gsutil/
# New Per-Project Files
.idea
nbproject/
bower.json
.bowerrc
.eslintrc
.jshintrc
.gitlab-ci.yml
.travis.yml
database.yml
Dockerfile
