###
# ------------------------------------------------------------------------
# OWASP ModSecurity Core Rule Set ver.3.2.0
# Copyright (c) 2006-2019 Trustwave and contributors. All rights reserved.
#
# The OWASP ModSecurity Core Rule Set is distributed under
# Apache Software License (ASL) version 2
# Please see the enclosed LICENSE file for full details.
# ------------------------------------------------------------------------

#
# -= Paranoia Level 0 (empty) =- (apply unconditionally)
#



SecRule TX:EXECUTING_PARANOIA_LEVEL "@lt 1" "id:913011,phase:1,pass,nolog,skipAfter:END-REQUEST-913-SCANNER-DETECTION"
SecRule TX:EXECUTING_PARANOIA_LEVEL "@lt 1" "id:913012,phase:2,pass,nolog,skipAfter:END-REQUEST-913-SCANNER-DETECTION"
#
# -= Paranoia Level 1 (default) =- (apply only when tx.executing_paranoia_level is sufficiently high: 1 or higher)
#

#
# -=[ Vulnerability Scanner Checks ]=-
#
# These rules inspect the default User-Agent and Header values sent by
# various commercial and open source vuln scanners.
#
# The following rules contain User-Agent lists:
# 913100 - security scanners (data file scanners-user-agents.data)
# 913101 - scripting/generic HTTP clients (data file scripting-user-agents.data)
# 913102 - web crawlers/bots (data file crawlers-user-agents.data)
#
SecRule REQUEST_HEADERS:User-Agent "@pmFromFile scanners-user-agents.data" \
    "id:913100,\
    phase:2,\
    block,\
    capture,\
    t:none,t:lowercase,\
    msg:'找到与安全扫描程序关联的用户代理',\
    logdata:'Matched Data: %{TX.0} found within %{MATCHED_VAR_NAME}: %{MATCHED_VAR}',\
    tag:'application-multi',\
    tag:'language-multi',\
    tag:'platform-multi',\
    tag:'attack-reputation-scanner',\
    tag:'OWASP_CRS',\
    tag:'OWASP_CRS/AUTOMATION/SECURITY_SCANNER',\
    tag:'WASCTC/WASC-21',\
    tag:'OWASP_TOP_10/A7',\
    tag:'PCI/6.5.10',\
    ver:'OWASP_CRS/3.2.0',\
    severity:'CRITICAL',\
    setvar:'tx.anomaly_score_pl1=+%{tx.critical_anomaly_score}',\
    setvar:'ip.reput_block_flag=1',\
    setvar:'ip.reput_block_reason=%{rule.msg}',\
    expirevar:'ip.reput_block_flag=%{tx.reput_block_duration}'"

SecRule REQUEST_HEADERS_NAMES|REQUEST_HEADERS "@pmFromFile scanners-headers.data" \
    "id:913110,\
    phase:2,\
    block,\
    capture,\
    t:none,t:lowercase,\
    msg:'找到与安全扫描程序相关联的请求标头',\
    logdata:'Matched Data: %{TX.0} found within %{MATCHED_VAR_NAME}: %{MATCHED_VAR}',\
    tag:'application-multi',\
    tag:'language-multi',\
    tag:'platform-multi',\
    tag:'attack-reputation-scanner',\
    tag:'OWASP_CRS',\
    tag:'OWASP_CRS/AUTOMATION/SECURITY_SCANNER',\
    tag:'WASCTC/WASC-21',\
    tag:'OWASP_TOP_10/A7',\
    tag:'PCI/6.5.10',\
    ver:'OWASP_CRS/3.2.0',\
    severity:'CRITICAL',\
    setvar:'tx.anomaly_score_pl1=+%{tx.critical_anomaly_score}',\
    setvar:'ip.reput_block_flag=1',\
    setvar:'ip.reput_block_reason=%{rule.msg}',\
    expirevar:'ip.reput_block_flag=%{tx.reput_block_duration}'"



SecRule REQUEST_FILENAME|ARGS "@pmFromFile scanners-urls.data" \
    "id:913120,\
    phase:2,\
    block,\
    capture,\
    t:none,t:lowercase,\
    msg:'找到与安全扫描器关联的请求文件名/参数',\
    logdata:'Matched Data: %{TX.0} found within %{MATCHED_VAR_NAME}: %{MATCHED_VAR}',\
    tag:'application-multi',\
    tag:'language-multi',\
    tag:'platform-multi',\
    tag:'attack-reputation-scanner',\
    tag:'OWASP_CRS',\
    tag:'OWASP_CRS/AUTOMATION/SECURITY_SCANNER',\
    tag:'WASCTC/WASC-21',\
    tag:'OWASP_TOP_10/A7',\
    tag:'PCI/6.5.10',\
    ver:'OWASP_CRS/3.2.0',\
    severity:'CRITICAL',\
    setvar:'tx.anomaly_score_pl1=+%{tx.critical_anomaly_score}',\
    setvar:'ip.reput_block_flag=1',\
    setvar:'ip.reput_block_reason=%{rule.msg}',\
    expirevar:'ip.reput_block_flag=%{tx.reput_block_duration}'"


SecRule TX:EXECUTING_PARANOIA_LEVEL "@lt 2" "id:913013,phase:1,pass,nolog,skipAfter:END-REQUEST-913-SCANNER-DETECTION"
SecRule TX:EXECUTING_PARANOIA_LEVEL "@lt 2" "id:913014,phase:2,pass,nolog,skipAfter:END-REQUEST-913-SCANNER-DETECTION"
#
# -= Paranoia Level 2 =- (apply only when tx.executing_paranoia_level is sufficiently high: 2 or higher)
#


#
# -=[ Scripting/Generic User-Agents ]=-
#
# This rule detects user-agents associated with various HTTP client libraries
# and scripting languages. Detection suggests attempted access by some
# automated tool.
#
# This rule is a sibling of rule 913100.
#
SecRule REQUEST_HEADERS:User-Agent "@pmFromFile scripting-user-agents.data" \
    "id:913101,\
    phase:2,\
    block,\
    capture,\
    t:none,t:lowercase,\
    msg:'找到与脚本/通用 HTTP 客户端关联的用户代理',\
    logdata:'Matched Data: %{TX.0} found within %{MATCHED_VAR_NAME}: %{MATCHED_VAR}',\
    tag:'application-multi',\
    tag:'language-multi',\
    tag:'platform-multi',\
    tag:'attack-reputation-scripting',\
    tag:'OWASP_CRS',\
    tag:'OWASP_CRS/AUTOMATION/SCRIPTING',\
    tag:'WASCTC/WASC-21',\
    tag:'OWASP_TOP_10/A7',\
    tag:'PCI/6.5.10',\
    tag:'paranoia-level/2',\
    ver:'OWASP_CRS/3.2.0',\
    severity:'CRITICAL',\
    setvar:'tx.anomaly_score_pl2=+%{tx.critical_anomaly_score}',\
    setvar:'ip.reput_block_flag=1',\
    setvar:'ip.reput_block_reason=%{rule.msg}',\
    expirevar:'ip.reput_block_flag=%{tx.reput_block_duration}'"



#
# -=[ Crawler User-Agents ]=-
#
# This rule detects user-agents associated with various crawlers, SEO tools,
# and bots, which have been reported to potentially misbehave.
# These crawlers can have legitimate uses when used with authorization.
#
# This rule is a sibling of rule 913100.
#
SecRule REQUEST_HEADERS:User-Agent "@pmFromFile crawlers-user-agents.data" \
    "id:913102,\
    phase:2,\
    block,\
    capture,\
    t:none,t:lowercase,\
    msg:'找到与网络爬虫/机器人关联的用户代理',\
    logdata:'Matched Data: %{TX.0} found within %{MATCHED_VAR_NAME}: %{MATCHED_VAR}',\
    tag:'application-multi',\
    tag:'language-multi',\
    tag:'platform-multi',\
    tag:'attack-reputation-crawler',\
    tag:'OWASP_CRS',\
    tag:'OWASP_CRS/AUTOMATION/CRAWLER',\
    tag:'WASCTC/WASC-21',\
    tag:'OWASP_TOP_10/A7',\
    tag:'PCI/6.5.10',\
    tag:'paranoia-level/2',\
    ver:'OWASP_CRS/3.2.0',\
    severity:'CRITICAL',\
    setvar:'tx.anomaly_score_pl2=+%{tx.critical_anomaly_score}',\
    setvar:'ip.reput_block_flag=1',\
    setvar:'ip.reput_block_reason=%{rule.msg}',\
    expirevar:'ip.reput_block_flag=%{tx.reput_block_duration}'"


SecRule TX:EXECUTING_PARANOIA_LEVEL "@lt 3" "id:913015,phase:1,pass,nolog,skipAfter:END-REQUEST-913-SCANNER-DETECTION"
SecRule TX:EXECUTING_PARANOIA_LEVEL "@lt 3" "id:913016,phase:2,pass,nolog,skipAfter:END-REQUEST-913-SCANNER-DETECTION"
#
# -= Paranoia Level 3 =- (apply only when tx.executing_paranoia_level is sufficiently high: 3 or higher)
#



SecRule TX:EXECUTING_PARANOIA_LEVEL "@lt 4" "id:913017,phase:1,pass,nolog,skipAfter:END-REQUEST-913-SCANNER-DETECTION"
SecRule TX:EXECUTING_PARANOIA_LEVEL "@lt 4" "id:913018,phase:2,pass,nolog,skipAfter:END-REQUEST-913-SCANNER-DETECTION"
#
# -= Paranoia Level 4 =- (apply only when tx.executing_paranoia_level is sufficiently high: 4 or higher)
#



#
# -= Paranoia Levels Finished =-
#
SecMarker "END-REQUEST-913-SCANNER-DETECTION"
