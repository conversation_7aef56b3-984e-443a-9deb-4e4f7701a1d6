# ------------------------------------------------------------------------
# OWASP ModSecurity Core Rule Set ver.3.2.0
# Copyright (c) 2006-2019 Trustwave and contributors. All rights reserved.
#
# The OWASP ModSecurity Core Rule Set is distributed under
# Apache Software License (ASL) version 2
# Please see the enclosed LICENSE file for full details.
# ------------------------------------------------------------------------

#
# The purpose of this file is to hold LOCAL exceptions for your site.  The
# types of rules that would go into this file are one where you want to
# short-circuit inspection and allow certain transactions to pass through
# inspection or if you want to alter rules that are applied.
#
# This file is named REQUEST-900-EXCLUSION-RULES-BEFORE-CRS.conf.example for a
# very specific reason. Files affixed with the .example extension are designed
# to contain user created/modified data. The '.example'. extension should be
# renamed to end in .conf. The advantage of this is that when OWASP CRS is
# updated, the updates will not overwrite a user generated configuration file.
#
# As a result of this design paradigm users are encouraged NOT to directly
# modify rules. Instead they should use this
# REQUEST-900-EXCLUSION-RULES-BEFORE-CRS and the
# RESPONSE-999-EXCLUSION-RULES-AFTER-CRS file to modify OWASP rules using
# methods similar to the examples specified below.
#
# REQUEST-900-EXCLUSION-RULES-BEFORE-CRS and
# RESPONSE-999-EXCLUSION-RULES-AFTER-CRS serve different purposes. ModSecurity
# effectively maintains two different context: startup, and per transaction.
# As a rule, directives are processed within the startup context. While they
# can affect the per transaction context they generally remain fixed during the
# execution of ModSecurity.
#
# As a result if one wanted to disable a rule at bootup the SecRuleRemoveById
# directive or one of its siblings would have to be placed AFTER the rule is
# listed, otherwise it will not have knowledge of the rules existence (since
# these rules are read in at the same time). This means that when using
# directives that effect SecRules, these exceptions should be placed AFTER all
# the existing rules. This is why RESPONSE-999-EXCLUSION-RULES-AFTER-CRS is
# designed such that it loads LAST.
#
# Conversely, ModSecurity supports several actions that can change the state of
# the underlying configuration during the per transaction context, this is when
# rules are being processed. Generally, these are accomplished by using the
# 'ctl' action. As these are part of a rule, they will be evaluated in the
# order rules are applied (by physical location, considering phases). As a
# result of this ordering a 'ctl' action should be placed with consideration to
# when it will be executed. This is particularly relevant for the 'ctl' options
# that involve modifying ID's (such as ruleRemoveById). In these cases it is
# important that such rules are placed BEFORE the rule ID they will affect.
# Unlike the setup context, by the time we process rules in the per-transaction
# context, we are already aware of all the rule ID's. It is by this logic that
# we include rules such as this BEFORE all the remaining rules.  As a result
# REQUEST-900-EXCLUSION-RULES-BEFORE-CRS is designed to load FIRST.
#
# As a general rule:
# ctl:ruleEngine            -> place in REQUEST-900-EXCLUSION-RULES-BEFORE-CRS
# ctl:ruleRemoveById        -> place in REQUEST-900-EXCLUSION-RULES-BEFORE-CRS
# ctl:ruleRemoveByMsg       -> place in REQUEST-900-EXCLUSION-RULES-BEFORE-CRS
# ctl:ruleRemoveByTag       -> place in REQUEST-900-EXCLUSION-RULES-BEFORE-CRS
# ctl:ruleRemoveTargetById  -> place in REQUEST-900-EXCLUSION-RULES-BEFORE-CRS
# ctl:ruleRemoveTargetByMsg -> place in REQUEST-900-EXCLUSION-RULES-BEFORE-CRS
# ctl:ruleRemoveTargetByTag -> place in REQUEST-900-EXCLUSION-RULES-BEFORE-CRS
#
# SecRuleRemoveById         -> place in RESPONSE-999-EXCLUSION-RULES-AFTER-CRS
# SecRuleRemoveByMsg        -> place in RESPONSE-999-EXCLUSION-RULES-AFTER-CRS
# SecRuleRemoveByTag        -> place in RESPONSE-999-EXCLUSION-RULES-AFTER-CRS
# SecRuleUpdateActionById   -> place in RESPONSE-999-EXCLUSION-RULES-AFTER-CRS
# SecRuleUpdateTargetById   -> place in RESPONSE-999-EXCLUSION-RULES-AFTER-CRS
# SecRuleUpdateTargetByMsg  -> place in RESPONSE-999-EXCLUSION-RULES-AFTER-CRS
# SecRuleUpdateTargetByTag  -> place in RESPONSE-999-EXCLUSION-RULES-AFTER-CRS
#
#
# What follows are a group of examples that show you how to perform rule
# exclusions.
#
#
# Example Exclusion Rule: Disable inspection for an authorized client
#
# This ruleset allows you to control how ModSecurity will handle traffic
# originating from Authorized Vulnerability Scanning (AVS) sources.  See
# related blog post -
# http://blog.spiderlabs.com/2010/12/advanced-topic-of-the-week-handling-authorized-scanning-traffic.html
#
# White-list ASV network block (no blocking or logging of AVS traffic) Update
# IP network block as appropriate for your AVS traffic
#
# ModSec Rule Exclusion: Disable Rule Engine for known ASV IP
# SecRule REMOTE_ADDR "@ipMatch *************" \
#     "id:1000,\
#     phase:1,\
#     pass,\
#     nolog,\
#     ctl:ruleEngine=Off"
#
#
# Example Exclusion Rule: Removing a specific ARGS parameter from inspection
#                         for an individual rule
#
# This rule shows how to conditionally exclude the "password"
# parameter for rule 942100 when the REQUEST_URI is /index.php
# ModSecurity Rule Exclusion: 942100 SQL Injection Detected via libinjection
##
#SecRule SERVER_ADDR "@ipMatch 服务器IP" \
#    "id:1001, \
#    phase:1, \
#    pass, \
#    nolog, \
#    ctl:ruleRemoveByTag=attack-dos"
# SecRule REQUEST_URI "@beginsWith /index.php" \
#     "id:1001,\
#     phase:1,\
#     pass,\
#     nolog,\
#     ctl:ruleRemoveTargetById=942100;ARGS:password"
#
#
# Example Exclusion Rule: Removing a specific ARGS parameter from inspection
#                         for only certain attacks
#
# Attack rules within the CRS are tagged, with tags such as 'attack-lfi',
# 'attack-sqli', 'attack-xss', 'attack-injection-php', et cetera.
#
# ModSecurity Rule Exclusion: Disable inspection of ARGS:pwd
#                             for all rules tagged attack-sqli
# SecRule REQUEST_FILENAME "@endsWith /wp-login.php" \
#     "id:1002,\
#     phase:2,\
#     pass,\
#     nolog,\
#     ctl:ruleRemoveTargetByTag=attack-sqli;ARGS:pwd"
#

# Example Exclusion Rule: Removing a specific ARGS parameter from inspection
#                         for all CRS rules
#
# This rule illustrates that we can use tagging very effectively to whitelist a
# common false positive across an entire ModSecurity instance. This can be done
# because every rule in OWASP_CRS is tagged with OWASP_CRS. This will NOT
# affect custom rules.
#
# ModSecurity Rule Exclusion: Disable inspection of ARGS:pwd
#                             for all CRS rules
# SecRule REQUEST_FILENAME "@endsWith /wp-login.php" \
#     "id:1003,\
#     phase:2,\
#     pass,\
#     nolog,\
#     ctl:ruleRemoveTargetByTag=OWASP_CRS;ARGS:pwd"

#
# Example Exclusion Rule: Removing a range of rules
#
# This rule illustrates that we can remove a rule range via a ctl action.
# This uses the fact, that rules are grouped by topic in rule files covering
# a certain id range.
#
# ModSecurity Rule Exclusion: Disable all SQLi and XSS rules
# SecRule REQUEST_FILENAME "@beginsWith /admin" \
#     "id:1004,\
#     phase:2,\
#     pass,\
#     nolog,\
#     ctl:ruleRemoveById=941000-942999"
#
#
# The application specific rule exclusion files
# REQUEST-903.9001-DRUPAL-EXCLUSION-RULES.conf
# REQUEST-903.9002-WORDPRESS-EXCLUSION-RULES.conf
# bring additional examples which can be useful then tuning a service.

SecRule REMOTE_ADDR "@pmFromFile white_list.data"
    "id:1000,phase:1,pass,nolog,ctl:ruleEngine=Off"

SecRule REQUEST_URI "@pmFromFile white_url.data"
    "id:10000,phase:1,pass,nolog,ctl:ruleEngine=Off"

# Log4j RCE attempt
SecRule REQUEST_URI|ARGS|REQUEST_BODY|REQUEST_HEADERS "\$\{jndi:(ldap[s]?|rmi|dns)://" \
    "id:1000000,\
    phase:2,\
    block,\
    t:none,t:urlDecodeUni,t:lowercase,\
    log,\
    msg:'Potential Log4j RCE Attempt Detected',\
    severity:'CRITICAL',\
    tag:'application-multi',\
    tag:'language-java',\
    tag:'platform-multi',\
    tag:'attack-rce',\
    tag:'OWASP_CRS',\
    tag:'capec/1000/152/137/6',\
    ver:'log4j-rce/1.0',\
    logdata:'Matched Data: %{MATCHED_VAR} found within %{MATCHED_VAR_NAME}'"

