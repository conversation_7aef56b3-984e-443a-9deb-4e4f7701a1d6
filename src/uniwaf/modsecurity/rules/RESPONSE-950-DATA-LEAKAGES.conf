###
# ------------------------------------------------------------------------
# OWASP ModSecurity Core Rule Set ver.3.2.0
# Copyright (c) 2006-2019 Trustwave and contributors. All rights reserved.
#
# The OWASP ModSecurity Core Rule Set is distributed under
# Apache Software License (ASL) version 2
# Please see the enclosed LICENSE file for full details.
# ------------------------------------------------------------------------

# The paranoia level skip rules 950020, 950021 and 950022 have odd
# numbers not in sync with other paranoia level skip rules in other
# files. This is done to avoid rule id collisions with CRSv2.
# This is also true for rule 950130.

#
# -= Paranoia Level 0 (empty) =- (apply unconditionally)
#



SecRule TX:EXECUTING_PARANOIA_LEVEL "@lt 1" "id:950020,phase:3,pass,nolog,skipAfter:END-RESPONSE-950-DATA-LEAKAGES"
SecRule TX:EXECUTING_PARANOIA_LEVEL "@lt 1" "id:950021,phase:4,pass,nolog,skipAfter:END-RESPONSE-950-DATA-LEAKAGES"
#
# -= Paranoia Level 1 (default) =- (apply only when tx.executing_paranoia_level is sufficiently high: 1 or higher)
#

#
# -=[ Directory Listing ]=-
#
SecRule RESPONSE_BODY "@rx (?:<(?:TITLE>Index of.*?<H|title>Index of.*?<h)1>Index of|>\[To Parent Directory\]<\/[Aa]><br>)" \
    "id:950130,\
    phase:4,\
    block,\
    capture,\
    t:none,\
    msg:'目录清单',\
    logdata:'Matched Data: %{TX.0} found within %{MATCHED_VAR_NAME}: %{MATCHED_VAR}',\
    tag:'application-multi',\
    tag:'language-multi',\
    tag:'platform-multi',\
    tag:'attack-disclosure',\
    tag:'OWASP_CRS',\
    tag:'OWASP_CRS/LEAKAGE/INFO_DIRECTORY_LISTING',\
    tag:'WASCTC/WASC-13',\
    tag:'OWASP_TOP_10/A6',\
    tag:'PCI/6.5.6',\
    ctl:auditLogParts=+E,\
    ver:'OWASP_CRS/3.2.0',\
    severity:'ERROR',\
    setvar:'tx.outbound_anomaly_score_pl1=+%{tx.error_anomaly_score}',\
    setvar:'tx.anomaly_score_pl1=+%{tx.error_anomaly_score}'"

#
# -=[ CGI Source Code Leakage ]=-
#
# A CGI script begins normally with #! and the interpreter,
# for example:
#
# #!/usr/bin/perl
# #!/usr/bin/python
# #!/usr/bin/ruby
#
# If the CGI script processors or MIME type handlers are misconfigured,
# the script's source code could be erroneously returned to the client.
SecRule RESPONSE_BODY "@rx ^#\!\s?/" \
    "id:950140,\
    phase:4,\
    block,\
    capture,\
    t:none,\
    msg:'CGI源代码泄漏',\
    logdata:'Matched Data: %{TX.0} found within %{MATCHED_VAR_NAME}: %{MATCHED_VAR}',\
    tag:'application-multi',\
    tag:'language-multi',\
    tag:'platform-multi',\
    tag:'attack-disclosure',\
    tag:'OWASP_CRS',\
    tag:'OWASP_CRS/LEAKAGE/SOURCE_CODE_CGI',\
    tag:'WASCTC/WASC-13',\
    tag:'OWASP_TOP_10/A6',\
    tag:'PCI/6.5.6',\
    ctl:auditLogParts=+E,\
    ver:'OWASP_CRS/3.2.0',\
    severity:'ERROR',\
    setvar:'tx.outbound_anomaly_score_pl1=+%{tx.error_anomaly_score}',\
    setvar:'tx.anomaly_score_pl1=+%{tx.error_anomaly_score}'"


SecRule TX:EXECUTING_PARANOIA_LEVEL "@lt 2" "id:950013,phase:3,pass,nolog,skipAfter:END-RESPONSE-950-DATA-LEAKAGES"
SecRule TX:EXECUTING_PARANOIA_LEVEL "@lt 2" "id:950014,phase:4,pass,nolog,skipAfter:END-RESPONSE-950-DATA-LEAKAGES"
#
# -= Paranoia Level 2 =- (apply only when tx.executing_paranoia_level is sufficiently high: 2 or higher)
#

#
# -=[ The application is not available - 5xx level status code ]=-
#
SecRule RESPONSE_STATUS "@rx ^5\d{2}$" \
    "id:950100,\
    phase:4,\
    block,\
    capture,\
    t:none,\
    msg:'应用程序返回了一个500级状态码',\
    logdata:'Matched Data: %{TX.0} found within %{MATCHED_VAR_NAME}: %{MATCHED_VAR}',\
    tag:'application-multi',\
    tag:'language-multi',\
    tag:'platform-multi',\
    tag:'attack-disclosure',\
    tag:'WASCTC/WASC-13',\
    tag:'OWASP_TOP_10/A6',\
    tag:'PCI/6.5.6',\
    tag:'paranoia-level/2',\
    ctl:auditLogParts=+E,\
    ver:'OWASP_CRS/3.2.0',\
    severity:'ERROR',\
    setvar:'tx.outbound_anomaly_score_pl2=+%{tx.error_anomaly_score}',\
    setvar:'tx.anomaly_score_pl2=+%{tx.error_anomaly_score}'"



SecRule TX:EXECUTING_PARANOIA_LEVEL "@lt 3" "id:950015,phase:3,pass,nolog,skipAfter:END-RESPONSE-950-DATA-LEAKAGES"
SecRule TX:EXECUTING_PARANOIA_LEVEL "@lt 3" "id:950016,phase:4,pass,nolog,skipAfter:END-RESPONSE-950-DATA-LEAKAGES"
#
# -= Paranoia Level 3 =- (apply only when tx.executing_paranoia_level is sufficiently high: 3 or higher)
#



SecRule TX:EXECUTING_PARANOIA_LEVEL "@lt 4" "id:950017,phase:3,pass,nolog,skipAfter:END-RESPONSE-950-DATA-LEAKAGES"
SecRule TX:EXECUTING_PARANOIA_LEVEL "@lt 4" "id:950022,phase:4,pass,nolog,skipAfter:END-RESPONSE-950-DATA-LEAKAGES"
#
# -= Paranoia Level 4 =- (apply only when tx.executing_paranoia_level is sufficiently high: 4 or higher)
#



#
# -= Paranoia Levels Finished =-
#
SecMarker "END-RESPONSE-950-DATA-LEAKAGES"
