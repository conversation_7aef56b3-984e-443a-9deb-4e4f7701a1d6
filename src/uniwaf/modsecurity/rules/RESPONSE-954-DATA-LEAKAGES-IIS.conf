###
# ------------------------------------------------------------------------
# OWASP ModSecurity Core Rule Set ver.3.2.0
# Copyright (c) 2006-2019 Trustwave and contributors. All rights reserved.
#
# The OWASP ModSecurity Core Rule Set is distributed under
# Apache Software License (ASL) version 2
# Please see the enclosed LICENSE file for full details.
# ------------------------------------------------------------------------

#
# -= Paranoia Level 0 (empty) =- (apply unconditionally)
#



SecRule TX:EXECUTING_PARANOIA_LEVEL "@lt 1" "id:954011,phase:3,pass,nolog,skipAfter:END-RESPONSE-954-DATA-LEAKAGES-IIS"
SecRule TX:EXECUTING_PARANOIA_LEVEL "@lt 1" "id:954012,phase:4,pass,nolog,skipAfter:END-RESPONSE-954-DATA-LEAKAGES-IIS"
#
# -= Paranoia Level 1 (default) =- (apply only when tx.executing_paranoia_level is sufficiently high: 1 or higher)
#

# IIS default location
SecRule RESPONSE_BODY "@rx [a-z]:\\\\inetpub\b" \
    "id:954100,\
    phase:4,\
    block,\
    capture,\
    t:none,t:lowercase,\
    msg:'IIS安装位置的披露',\
    logdata:'Matched Data: %{TX.0} found within %{MATCHED_VAR_NAME}: %{MATCHED_VAR}',\
    tag:'application-multi',\
    tag:'language-multi',\
    tag:'platform-iis',\
    tag:'platform-windows',\
    tag:'attack-disclosure',\
    ctl:auditLogParts=+E,\
    ver:'OWASP_CRS/3.2.0',\
    severity:'ERROR',\
    setvar:'tx.outbound_anomaly_score_pl1=+%{tx.error_anomaly_score}',\
    setvar:'tx.anomaly_score_pl1=+%{tx.error_anomaly_score}'"

SecRule RESPONSE_BODY "@rx (?:Microsoft OLE DB Provider for SQL Server(?:<\/font>.{1,20}?error '800(?:04005|40e31)'.{1,40}?Timeout expired| \(0x80040e31\)<br>Timeout expired<br>)|<h1>internal server error<\/h1>.*?<h2>part of the server has crashed or it has a configuration error\.<\/h2>|cannot connect to the server: timed out)" \
    "id:954110,\
    phase:4,\
    block,\
    capture,\
    t:none,\
    msg:'应用程序可用性错误',\
    logdata:'Matched Data: %{TX.0} found within %{MATCHED_VAR_NAME}: %{MATCHED_VAR}',\
    tag:'application-multi',\
    tag:'language-multi',\
    tag:'platform-iis',\
    tag:'platform-windows',\
    tag:'attack-disclosure',\
    tag:'WASCTC/WASC-13',\
    tag:'OWASP_TOP_10/A6',\
    tag:'PCI/6.5.6',\
    ctl:auditLogParts=+E,\
    ver:'OWASP_CRS/3.2.0',\
    severity:'ERROR',\
    setvar:'tx.outbound_anomaly_score_pl1=+%{tx.error_anomaly_score}',\
    setvar:'tx.anomaly_score_pl1=+%{tx.error_anomaly_score}'"

#
# IIS Errors leakage
#
SecRule RESPONSE_BODY "@rx (?:\b(?:A(?:DODB\.Command\b.{0,100}?\b(?:Application uses a value of the wrong type for the current operation\b|error')| trappable error occurred in an external object\. The script cannot continue running\b)|Microsoft VBScript (?:compilation (?:\(0x8|error)|runtime (?:Error|\(0x8))\b|Object required: '|error '800)|<b>Version Information:<\/b>(?:&nbsp;|\s)(?:Microsoft \.NET Framework|ASP\.NET) Version:|>error 'ASP\b|An Error Has Occurred|>Syntax error in string in query expression|\/[Ee]rror[Mm]essage\.aspx?\?[Ee]rror\b)" \
    "id:954120,\
    phase:4,\
    block,\
    capture,\
    t:none,\
    msg:'IIS信息泄露',\
    logdata:'Matched Data: %{TX.0} found within %{MATCHED_VAR_NAME}: %{MATCHED_VAR}',\
    tag:'application-multi',\
    tag:'language-multi',\
    tag:'platform-iis',\
    tag:'platform-windows',\
    tag:'attack-disclosure',\
    tag:'OWASP_CRS',\
    tag:'OWASP_CRS/LEAKAGE/ERRORS_IIS',\
    tag:'WASCTC/WASC-13',\
    tag:'OWASP_TOP_10/A6',\
    tag:'PCI/6.5.6',\
    ctl:auditLogParts=+E,\
    ver:'OWASP_CRS/3.2.0',\
    severity:'ERROR',\
    setvar:'tx.outbound_anomaly_score_pl1=+%{tx.error_anomaly_score}',\
    setvar:'tx.anomaly_score_pl1=+%{tx.error_anomaly_score}'"


SecRule RESPONSE_STATUS "!@rx ^404$" \
    "id:954130,\
    phase:4,\
    block,\
    capture,\
    t:none,\
    msg:'IIS信息泄露',\
    logdata:'Matched Data: %{TX.0} found within %{MATCHED_VAR_NAME}: %{MATCHED_VAR}',\
    tag:'application-multi',\
    tag:'language-multi',\
    tag:'platform-iis',\
    tag:'platform-windows',\
    tag:'attack-disclosure',\
    tag:'OWASP_CRS',\
    tag:'OWASP_CRS/LEAKAGE/ERRORS_IIS',\
    tag:'WASCTC/WASC-13',\
    tag:'OWASP_TOP_10/A6',\
    tag:'PCI/6.5.6',\
    ctl:auditLogParts=+E,\
    ver:'OWASP_CRS/3.2.0',\
    severity:'ERROR',\
    chain"
    SecRule RESPONSE_BODY "@rx \bServer Error in.{0,50}?\bApplication\b" \
        "capture,\
        t:none,\
        setvar:'tx.outbound_anomaly_score_pl1=+%{tx.error_anomaly_score}',\
        setvar:'tx.anomaly_score_pl1=+%{tx.error_anomaly_score}'"



SecRule TX:EXECUTING_PARANOIA_LEVEL "@lt 2" "id:954013,phase:3,pass,nolog,skipAfter:END-RESPONSE-954-DATA-LEAKAGES-IIS"
SecRule TX:EXECUTING_PARANOIA_LEVEL "@lt 2" "id:954014,phase:4,pass,nolog,skipAfter:END-RESPONSE-954-DATA-LEAKAGES-IIS"
#
# -= Paranoia Level 2 =- (apply only when tx.executing_paranoia_level is sufficiently high: 2 or higher)
#



SecRule TX:EXECUTING_PARANOIA_LEVEL "@lt 3" "id:954015,phase:3,pass,nolog,skipAfter:END-RESPONSE-954-DATA-LEAKAGES-IIS"
SecRule TX:EXECUTING_PARANOIA_LEVEL "@lt 3" "id:954016,phase:4,pass,nolog,skipAfter:END-RESPONSE-954-DATA-LEAKAGES-IIS"
#
# -= Paranoia Level 3 =- (apply only when tx.executing_paranoia_level is sufficiently high: 3 or higher)
#



SecRule TX:EXECUTING_PARANOIA_LEVEL "@lt 4" "id:954017,phase:3,pass,nolog,skipAfter:END-RESPONSE-954-DATA-LEAKAGES-IIS"
SecRule TX:EXECUTING_PARANOIA_LEVEL "@lt 4" "id:954018,phase:4,pass,nolog,skipAfter:END-RESPONSE-954-DATA-LEAKAGES-IIS"
#
# -= Paranoia Level 4 =- (apply only when tx.executing_paranoia_level is sufficiently high: 4 or higher)
#



#
# -= Paranoia Levels Finished =-
#
SecMarker "END-RESPONSE-954-DATA-LEAKAGES-IIS"
