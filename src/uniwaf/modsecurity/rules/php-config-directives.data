allow_call_time_pass_reference
allow_url_fopen
allow_url_include
always_populate_raw_post_data
arg_separator.input
arg_separator.output
asp_tags
assert.active
assert.bail
assert.callback
assert.quiet_eval
assert.warning
auto_append_file
auto_detect_line_endings
auto_globals_jit
auto_prepend_file
bcmath.scale
birdstep.max_links
browscap
cgi.fix_pathinfo
cgi.force_redirect
cgi.nph
cgi.redirect_status_env
cgi.rfc2616_headers
com.allow_dcom
com.autoregister_casesensitive
com.autoregister_typelib
com.autoregister_verbose
com.code_page
com.typelib_file
date.default_latitude
date.default_longitude
date.sunrise_zenith
date.sunset_zenith
date.timezone
dba.default_handler
default_charset
default_mimetype
default_socket_timeout
define_syslog_variables
disable_classes
disable_functions
display_errors
display_startup_errors
doc_root
docref_ext
docref_root
enable_dl
error_append_string
error_log
error_prepend_string
error_reporting
exif.decode_jis_intel
exif.decode_jis_motorola
exif.decode_unicode_intel
exif.decode_unicode_motorola
exif.encode_jis
exif.encode_unicode
expose_php
extension_dir
fastcgi.impersonate
fastcgi.logging
file_uploads
filter.default
filter.default_flags
gd.jpeg_ignore_warning
highlight.bg
highlight.comment
highlight.default
highlight.html
highlight.keyword
highlight.string
html_errors
ibase.allow_persistent
ibase.dateformat
ibase.default_charset
ibase.default_db
ibase.default_password
ibase.default_user
ibase.max_links
ibase.max_persistent
ibase.timeformat
ibase.timestampformat
iconv.input_encoding
iconv.internal_encoding
iconv.output_encoding
ignore_repeated_errors
ignore_repeated_source
ignore_user_abort
implicit_flush
include_path
intl.default_locale
intl.error_level
ldap.max_links
log_errors
log_errors_max_len
magic_quotes_gpc
magic_quotes_runtime
magic_quotes_sybase
mail.add_x_header
mail.force_extra_parameters
mail.log
max_execution_time
max_file_uploads
max_input_nesting_level
max_input_time
mbstring.detect_order
mbstring.encoding_translation
mbstring.func_overload
mbstring.http_input
mbstring.http_output
mbstring.http_output_conv_mimetype
mbstring.internal_encoding
mbstring.language
mbstring.script_encoding
mbstring.strict_detection
mbstring.substitute_character
mcrypt.algorithms_dir
mcrypt.modes_dir
memory_limit
mssql.allow_persistent
mssql.batchsize
mssql.charset
mssql.compatability_mode
mssql.connect_timeout
mssql.datetimeconvert
mssql.max_links
mssql.max_persistent
mssql.max_procs
mssql.min_error_severity
mssql.min_message_severity
mssql.secure_connection
mssql.textlimit
mssql.textsize
mssql.timeout
mysql.allow_local_infile
mysql.allow_persistent
mysql.cache_size
mysql.connect_timeout
mysql.default_host
mysql.default_password
mysql.default_port
mysql.default_socket
mysql.default_user
mysql.max_links
mysql.max_persistent
mysql.trace_mode
mysqli.allow_local_infile
mysqli.allow_persistent
mysqli.cache_size
mysqli.default_host
mysqli.default_port
mysqli.default_pw
mysqli.default_socket
mysqli.default_user
mysqli.max_links
mysqli.max_persistent
mysqli.reconnect
mysqlnd.collect_memory_statistics
mysqlnd.collect_statistics
mysqlnd.net_cmd_buffer_size
mysqlnd.net_read_buffer_size
oci8.connection_class
oci8.default_prefetch
oci8.events
oci8.max_persistent
oci8.old_oci_close_semantics
oci8.persistent_timeout
oci8.ping_interval
oci8.privileged_connect
oci8.statement_cache_size
odbc.allow_persistent
odbc.check_persistent
odbc.default_db
odbc.default_pw
odbc.default_user
odbc.defaultbinmode
odbc.defaultlrl
odbc.max_links
odbc.max_persistent
open_basedir
output_buffering
output_handler
pcre.backtrack_limit
pcre.recursion_limit
pdo_mysql.cache_size
pdo_mysql.default_socket
pdo_odbc.connection_pooling
pgsql.allow_persistent
pgsql.auto_reset_persistent
pgsql.ignore_notice
pgsql.log_notice
pgsql.max_links
pgsql.max_persistent
phar.cache_list
phar.readonly
phar.require_hash
post_max_size
realpath_cache_size
realpath_cache_ttl
register_argc_argv
register_globals
register_long_arrays
report_memleaks
report_zend_debug
request_order
safe_mode
safe_mode_allowed_env_vars
safe_mode_exec_dir
safe_mode_gid
safe_mode_include_dir
safe_mode_protected_env_vars
sendmail_from
sendmail_path
serialize_precision
session.auto_start
session.bug_compat_42
session.bug_compat_warn
session.cache_expire
session.cache_limiter
session.cookie_domain
session.cookie_httponly
session.cookie_lifetime
session.cookie_path
session.cookie_secure
session.entropy_file
session.entropy_length
session.gc_divisor
session.gc_maxlifetime
session.gc_probability
session.hash_bits_per_character
session.hash_function
session.name
session.referer_check
session.save_handler
session.save_path
session.serialize_handler
session.use_cookies
session.use_only_cookies
session.use_trans_sid
short_open_tag
soap.wsdl_cache_dir
soap.wsdl_cache_enabled
soap.wsdl_cache_limit
soap.wsdl_cache_ttl
sql.safe_mode
sqlite.assoc_case
sqlite3.extension_dir
sybct.allow_persistent
sybct.deadlock_retry_count
sybct.hostname
sybct.login_timeout
sybct.max_links
sybct.max_persistent
sybct.min_client_severity
sybct.min_server_severity
sybct.timeout
sysvshm.init_mem
tidy.clean_output
tidy.default_config
track_errors
unserialize_callback_func
upload_max_filesize
upload_tmp_dir
url_rewriter.tags
user_agent
user_dir
user_ini.cache_ttl
user_ini.filename
variables_order
xmlrpc_error_number
xmlrpc_errors
y2k_compliance
zlib.output_compression
zlib.output_compression_level
zlib.output_handler
