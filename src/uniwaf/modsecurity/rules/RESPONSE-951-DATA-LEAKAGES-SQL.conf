###
# ------------------------------------------------------------------------
# OWASP ModSecurity Core Rule Set ver.3.2.0
# Copyright (c) 2006-2019 Trustwave and contributors. All rights reserved.
#
# The OWASP ModSecurity Core Rule Set is distributed under
# Apache Software License (ASL) version 2
# Please see the enclosed LICENSE file for full details.
# ------------------------------------------------------------------------

#
# -= Paranoia Level 0 (empty) =- (apply unconditionally)
#



SecRule TX:EXECUTING_PARANOIA_LEVEL "@lt 1" "id:951011,phase:3,pass,nolog,skipAfter:END-RESPONSE-951-DATA-LEAKAGES-SQL"
SecRule TX:EXECUTING_PARANOIA_LEVEL "@lt 1" "id:951012,phase:4,pass,nolog,skipAfter:END-RESPONSE-951-DATA-LEAKAGES-SQL"
#
# -= Paranoia Level 1 (default) =- (apply only when tx.executing_paranoia_level is sufficiently high: 1 or higher)
#

#
# -=[ SQL Error Leakages ]=-
#
# Ref: https://raw.github.com/sqlmapproject/sqlmap/master/xml/errors.xml
# Ref: https://github.com/Arachni/arachni/tree/master/components/checks/active/sql_injection/regexps
#
SecRule RESPONSE_BODY "@pmFromFile sql-errors.data" \
    "id:951100,\
    phase:4,\
    pass,\
    t:none,\
    nolog,\
    tag:'application-multi',\
    tag:'language-multi',\
    tag:'platform-multi',\
    tag:'attack-disclosure',\
    ver:'OWASP_CRS/3.2.0',\
    setvar:'tx.sql_error_match=1'"

SecRule TX:sql_error_match "@eq 1" \
    "id:951110,\
    phase:4,\
    block,\
    capture,\
    t:none,\
    msg:'Microsoft Access SQL信息泄露',\
    logdata:'Matched Data: %{TX.0} found within %{MATCHED_VAR_NAME}: %{MATCHED_VAR}',\
    tag:'application-multi',\
    tag:'language-multi',\
    tag:'platform-msaccess',\
    tag:'attack-disclosure',\
    tag:'OWASP_CRS',\
    tag:'OWASP_CRS/LEAKAGE/ERRORS_SQL',\
    tag:'CWE-209',\
    ctl:auditLogParts=+E,\
    ver:'OWASP_CRS/3.2.0',\
    severity:'CRITICAL',\
    chain"
    SecRule RESPONSE_BODY "@rx (?i:JET Database Engine|Access Database Engine|\[Microsoft\]\[ODBC Microsoft Access Driver\])" \
        "capture,\
        setvar:'tx.outbound_anomaly_score_pl1=+%{tx.critical_anomaly_score}',\
        setvar:'tx.sql_injection_score=+%{tx.critical_anomaly_score}',\
        setvar:'tx.anomaly_score_pl1=+%{tx.critical_anomaly_score}'"

SecRule TX:sql_error_match "@eq 1" \
    "id:951120,\
    phase:4,\
    block,\
    capture,\
    t:none,\
    msg:'Oracle SQL信息泄露',\
    logdata:'Matched Data: %{TX.0} found within %{MATCHED_VAR_NAME}: %{MATCHED_VAR}',\
    tag:'application-multi',\
    tag:'language-multi',\
    tag:'platform-oracle',\
    tag:'attack-disclosure',\
    tag:'OWASP_CRS',\
    tag:'OWASP_CRS/LEAKAGE/ERRORS_SQL',\
    tag:'CWE-209',\
    ctl:auditLogParts=+E,\
    ver:'OWASP_CRS/3.2.0',\
    severity:'CRITICAL',\
    chain"
    SecRule RESPONSE_BODY "@rx (?i:ORA-[0-9][0-9][0-9][0-9]|java\.sql\.SQLException|Oracle error|Oracle.*Driver|Warning.*oci_.*|Warning.*ora_.*)" \
        "capture,\
        setvar:'tx.outbound_anomaly_score_pl1=+%{tx.critical_anomaly_score}',\
        setvar:'tx.sql_injection_score=+%{tx.critical_anomaly_score}',\
        setvar:'tx.anomaly_score_pl1=+%{tx.critical_anomaly_score}'"

SecRule TX:sql_error_match "@eq 1" \
    "id:951130,\
    phase:4,\
    block,\
    capture,\
    t:none,\
    msg:'DB2 SQL信息泄露',\
    logdata:'Matched Data: %{TX.0} found within %{MATCHED_VAR_NAME}: %{MATCHED_VAR}',\
    tag:'application-multi',\
    tag:'language-multi',\
    tag:'platform-db2',\
    tag:'attack-disclosure',\
    tag:'OWASP_CRS',\
    tag:'OWASP_CRS/LEAKAGE/ERRORS_SQL',\
    tag:'CWE-209',\
    ctl:auditLogParts=+E,\
    ver:'OWASP_CRS/3.2.0',\
    severity:'CRITICAL',\
    chain"
    SecRule RESPONSE_BODY "@rx (?i:DB2 SQL error:|\[IBM\]\[CLI Driver\]\[DB2/6000\]|CLI Driver.*DB2|DB2 SQL error|db2_\w+\()" \
        "capture,\
        setvar:'tx.outbound_anomaly_score_pl1=+%{tx.critical_anomaly_score}',\
        setvar:'tx.sql_injection_score=+%{tx.critical_anomaly_score}',\
        setvar:'tx.anomaly_score_pl1=+%{tx.critical_anomaly_score}'"

SecRule TX:sql_error_match "@eq 1" \
    "id:951140,\
    phase:4,\
    block,\
    capture,\
    t:none,\
    msg:'EMC SQL信息泄露',\
    logdata:'Matched Data: %{TX.0} found within %{MATCHED_VAR_NAME}: %{MATCHED_VAR}',\
    tag:'application-multi',\
    tag:'language-multi',\
    tag:'platform-emc',\
    tag:'attack-disclosure',\
    tag:'OWASP_CRS',\
    tag:'OWASP_CRS/LEAKAGE/ERRORS_SQL',\
    tag:'CWE-209',\
    ctl:auditLogParts=+E,\
    ver:'OWASP_CRS/3.2.0',\
    severity:'CRITICAL',\
    chain"
    SecRule RESPONSE_BODY "@rx (?i:\[DM_QUERY_E_SYNTAX\]|has occurred in the vicinity of:)" \
        "capture,\
        setvar:'tx.outbound_anomaly_score_pl1=+%{tx.critical_anomaly_score}',\
        setvar:'tx.sql_injection_score=+%{tx.critical_anomaly_score}',\
        setvar:'tx.anomaly_score_pl1=+%{tx.critical_anomaly_score}'"

SecRule TX:sql_error_match "@eq 1" \
    "id:951150,\
    phase:4,\
    block,\
    capture,\
    t:none,\
    msg:'火鸟SQL信息泄露',\
    logdata:'Matched Data: %{TX.0} found within %{MATCHED_VAR_NAME}: %{MATCHED_VAR}',\
    tag:'application-multi',\
    tag:'language-multi',\
    tag:'platform-firebird',\
    tag:'attack-disclosure',\
    tag:'OWASP_CRS',\
    tag:'OWASP_CRS/LEAKAGE/ERRORS_SQL',\
    tag:'CWE-209',\
    ctl:auditLogParts=+E,\
    ver:'OWASP_CRS/3.2.0',\
    severity:'CRITICAL',\
    chain"
    SecRule RESPONSE_BODY "@rx (?i)Dynamic SQL Error" \
        "capture,\
        setvar:'tx.outbound_anomaly_score_pl1=+%{tx.critical_anomaly_score}',\
        setvar:'tx.sql_injection_score=+%{tx.critical_anomaly_score}',\
        setvar:'tx.anomaly_score_pl1=+%{tx.critical_anomaly_score}'"


SecRule TX:sql_error_match "@eq 1" \
    "id:951160,\
    phase:4,\
    block,\
    capture,\
    t:none,\
    msg:'数据库SQL信息泄露',\
    logdata:'Matched Data: %{TX.0} found within %{MATCHED_VAR_NAME}: %{MATCHED_VAR}',\
    tag:'application-multi',\
    tag:'language-multi',\
    tag:'platform-frontbase',\
    tag:'attack-disclosure',\
    tag:'OWASP_CRS',\
    tag:'OWASP_CRS/LEAKAGE/ERRORS_SQL',\
    tag:'CWE-209',\
    ctl:auditLogParts=+E,\
    ver:'OWASP_CRS/3.2.0',\
    severity:'CRITICAL',\
    chain"
    SecRule RESPONSE_BODY "@rx (?i)Exception (?:condition )?\d+\. Transaction rollback\." \
        "capture,\
        setvar:'tx.outbound_anomaly_score_pl1=+%{tx.critical_anomaly_score}',\
        setvar:'tx.sql_injection_score=+%{tx.critical_anomaly_score}',\
        setvar:'tx.anomaly_score_pl1=+%{tx.critical_anomaly_score}'"

SecRule TX:sql_error_match "@eq 1" \
    "id:951170,\
    phase:4,\
    block,\
    capture,\
    t:none,\
    msg:'hsqldb SQL信息泄露',\
    logdata:'Matched Data: %{TX.0} found within %{MATCHED_VAR_NAME}: %{MATCHED_VAR}',\
    tag:'application-multi',\
    tag:'language-multi',\
    tag:'platform-hsqldb',\
    tag:'attack-disclosure',\
    tag:'OWASP_CRS',\
    tag:'OWASP_CRS/LEAKAGE/ERRORS_SQL',\
    tag:'CWE-209',\
    ctl:auditLogParts=+E,\
    ver:'OWASP_CRS/3.2.0',\
    severity:'CRITICAL',\
    chain"
    SecRule RESPONSE_BODY "@rx (?i)org\.hsqldb\.jdbc" \
        "capture,\
        setvar:'tx.outbound_anomaly_score_pl1=+%{tx.critical_anomaly_score}',\
        setvar:'tx.sql_injection_score=+%{tx.critical_anomaly_score}',\
        setvar:'tx.anomaly_score_pl1=+%{tx.critical_anomaly_score}'"

SecRule TX:sql_error_match "@eq 1" \
    "id:951180,\
    phase:4,\
    block,\
    capture,\
    t:none,\
    msg:'informix SQL信息泄露',\
    logdata:'Matched Data: %{TX.0} found within %{MATCHED_VAR_NAME}: %{MATCHED_VAR}',\
    tag:'application-multi',\
    tag:'language-multi',\
    tag:'platform-informix',\
    tag:'attack-disclosure',\
    tag:'OWASP_CRS',\
    tag:'OWASP_CRS/LEAKAGE/ERRORS_SQL',\
    tag:'CWE-209',\
    ctl:auditLogParts=+E,\
    ver:'OWASP_CRS/3.2.0',\
    severity:'CRITICAL',\
    chain"
    SecRule RESPONSE_BODY "@rx (?i:An illegal character has been found in the statement|com\.informix\.jdbc|Exception.*Informix)" \
        "capture,\
        setvar:'tx.outbound_anomaly_score_pl1=+%{tx.critical_anomaly_score}',\
        setvar:'tx.sql_injection_score=+%{tx.critical_anomaly_score}',\
        setvar:'tx.anomaly_score_pl1=+%{tx.critical_anomaly_score}'"


SecRule TX:sql_error_match "@eq 1" \
    "id:951190,\
    phase:4,\
    block,\
    capture,\
    t:none,\
    msg:'SQL信息泄露',\
    logdata:'Matched Data: %{TX.0} found within %{MATCHED_VAR_NAME}: %{MATCHED_VAR}',\
    tag:'application-multi',\
    tag:'language-multi',\
    tag:'platform-ingres',\
    tag:'attack-disclosure',\
    tag:'OWASP_CRS',\
    tag:'OWASP_CRS/LEAKAGE/ERRORS_SQL',\
    tag:'CWE-209',\
    ctl:auditLogParts=+E,\
    ver:'OWASP_CRS/3.2.0',\
    severity:'CRITICAL',\
    chain"
    SecRule RESPONSE_BODY "@rx (?i:Warning.*ingres_|Ingres SQLSTATE|Ingres\W.*Driver)" \
        "capture,\
        setvar:'tx.outbound_anomaly_score_pl1=+%{tx.critical_anomaly_score}',\
        setvar:'tx.sql_injection_score=+%{tx.critical_anomaly_score}',\
        setvar:'tx.anomaly_score_pl1=+%{tx.critical_anomaly_score}'"


SecRule TX:sql_error_match "@eq 1" \
    "id:951200,\
    phase:4,\
    block,\
    capture,\
    t:none,\
    msg:'数据库间SQL信息泄漏',\
    logdata:'Matched Data: %{TX.0} found within %{MATCHED_VAR_NAME}: %{MATCHED_VAR}',\
    tag:'application-multi',\
    tag:'language-multi',\
    tag:'platform-interbase',\
    tag:'attack-disclosure',\
    tag:'OWASP_CRS',\
    tag:'OWASP_CRS/LEAKAGE/ERRORS_SQL',\
    tag:'CWE-209',\
    ctl:auditLogParts=+E,\
    ver:'OWASP_CRS/3.2.0',\
    severity:'CRITICAL',\
    chain"
    SecRule RESPONSE_BODY "@rx (?i:<b>Warning</b>: ibase_|Unexpected end of command in statement)" \
        "capture,\
        setvar:'tx.outbound_anomaly_score_pl1=+%{tx.critical_anomaly_score}',\
        setvar:'tx.sql_injection_score=+%{tx.critical_anomaly_score}',\
        setvar:'tx.anomaly_score_pl1=+%{tx.critical_anomaly_score}'"

SecRule TX:sql_error_match "@eq 1" \
    "id:951210,\
    phase:4,\
    block,\
    capture,\
    t:none,\
    msg:'maxDB SQL信息泄露',\
    logdata:'Matched Data: %{TX.0} found within %{MATCHED_VAR_NAME}: %{MATCHED_VAR}',\
    tag:'application-multi',\
    tag:'language-multi',\
    tag:'platform-maxdb',\
    tag:'attack-disclosure',\
    tag:'OWASP_CRS',\
    tag:'OWASP_CRS/LEAKAGE/ERRORS_SQL',\
    tag:'CWE-209',\
    ctl:auditLogParts=+E,\
    ver:'OWASP_CRS/3.2.0',\
    severity:'CRITICAL',\
    chain"
    SecRule RESPONSE_BODY "@rx (?i:SQL error.*POS[0-9]+.*|Warning.*maxdb.*)" \
        "capture,\
        setvar:'tx.outbound_anomaly_score_pl1=+%{tx.critical_anomaly_score}',\
        setvar:'tx.sql_injection_score=+%{tx.critical_anomaly_score}',\
        setvar:'tx.anomaly_score_pl1=+%{tx.critical_anomaly_score}'"

SecRule TX:sql_error_match "@eq 1" \
    "id:951220,\
    phase:4,\
    block,\
    capture,\
    t:none,\
    msg:'mssql SQL信息泄露',\
    logdata:'Matched Data: %{TX.0} found within %{MATCHED_VAR_NAME}: %{MATCHED_VAR}',\
    tag:'application-multi',\
    tag:'language-multi',\
    tag:'platform-mssql',\
    tag:'attack-disclosure',\
    tag:'OWASP_CRS',\
    tag:'OWASP_CRS/LEAKAGE/ERRORS_SQL',\
    tag:'CWE-209',\
    ctl:auditLogParts=+E,\
    ver:'OWASP_CRS/3.2.0',\
    severity:'CRITICAL',\
    chain"
    SecRule RESPONSE_BODY "@rx (?i)(?:System\.Data\.OleDb\.OleDbException|\[Microsoft\]\[ODBC SQL Server Driver\]|\[Macromedia\]\[SQLServer JDBC Driver\]|\[SqlException|System\.Data\.SqlClient\.SqlException|Unclosed quotation mark after the character string|'80040e14'|mssql_query\(\)|Microsoft OLE DB Provider for ODBC Drivers|Microsoft OLE DB Provider for SQL Server|Incorrect syntax near|Sintaxis incorrecta cerca de|Syntax error in string in query expression|Procedure or function .* expects parameter|Unclosed quotation mark before the character string|Syntax error .* in query expression|Data type mismatch in criteria expression\.|ADODB\.Field \(0x800A0BCD\)|the used select statements have different number of columns|OLE DB.*SQL Server|Warning.*mssql_.*|Driver.*SQL[\-\_\ ]*Server|SQL Server.*Driver|SQL Server.*[0-9a-fA-F]{8}|Exception.*\WSystem\.Data\.SqlClient\.)" \
        "capture,\
        setvar:'tx.outbound_anomaly_score_pl1=+%{tx.critical_anomaly_score}',\
        setvar:'tx.sql_injection_score=+%{tx.critical_anomaly_score}',\
        setvar:'tx.anomaly_score_pl1=+%{tx.critical_anomaly_score}'"

SecRule TX:sql_error_match "@eq 1" \
    "id:951230,\
    phase:4,\
    block,\
    capture,\
    t:none,\
    msg:'mysql SQL信息泄露',\
    logdata:'Matched Data: %{TX.0} found within %{MATCHED_VAR_NAME}: %{MATCHED_VAR}',\
    tag:'application-multi',\
    tag:'language-multi',\
    tag:'platform-mysql',\
    tag:'attack-disclosure',\
    tag:'OWASP_CRS',\
    tag:'OWASP_CRS/LEAKAGE/ERRORS_SQL',\
    tag:'CWE-209',\
    ctl:auditLogParts=+E,\
    ver:'OWASP_CRS/3.2.0',\
    severity:'CRITICAL',\
    chain"
    SecRule RESPONSE_BODY "@rx (?i)(?:supplied argument is not a valid MySQL|Column count doesn't match value count at row|mysql_fetch_array\(\)|on MySQL result index|You have an error in your SQL syntax;|You have an error in your SQL syntax near|MySQL server version for the right syntax to use|\[MySQL\]\[ODBC|Column count doesn't match|Table '[^']+' doesn't exist|SQL syntax.*MySQL|Warning.*mysql_.*|valid MySQL result|MySqlClient\.)" \
        "capture,\
        setvar:'tx.outbound_anomaly_score_pl1=+%{tx.critical_anomaly_score}',\
        setvar:'tx.sql_injection_score=+%{tx.critical_anomaly_score}',\
        setvar:'tx.anomaly_score_pl1=+%{tx.critical_anomaly_score}'"

SecRule TX:sql_error_match "@eq 1" \
    "id:951240,\
    phase:4,\
    block,\
    capture,\
    t:none,\
    msg:'postgres SQL信息泄露',\
    logdata:'Matched Data: %{TX.0} found within %{MATCHED_VAR_NAME}: %{MATCHED_VAR}',\
    tag:'application-multi',\
    tag:'language-multi',\
    tag:'platform-pgsql',\
    tag:'attack-disclosure',\
    tag:'OWASP_CRS',\
    tag:'OWASP_CRS/LEAKAGE/ERRORS_SQL',\
    tag:'CWE-209',\
    ctl:auditLogParts=+E,\
    ver:'OWASP_CRS/3.2.0',\
    severity:'CRITICAL',\
    chain"
    SecRule RESPONSE_BODY "@rx (?i:PostgreSQL query failed:|pg_query\(\) \[:|pg_exec\(\) \[:|PostgreSQL.*ERROR|Warning.*pg_.*|valid PostgreSQL result|Npgsql\.|PG::[a-zA-Z]*Error|Supplied argument is not a valid PostgreSQL .*? resource|Unable to connect to PostgreSQL server)" \
        "capture,\
        setvar:'tx.outbound_anomaly_score_pl1=+%{tx.critical_anomaly_score}',\
        setvar:'tx.sql_injection_score=+%{tx.critical_anomaly_score}',\
        setvar:'tx.anomaly_score_pl1=+%{tx.critical_anomaly_score}'"

SecRule TX:sql_error_match "@eq 1" \
    "id:951250,\
    phase:4,\
    block,\
    capture,\
    t:none,\
    msg:'sqlite SQL信息泄露',\
    logdata:'Matched Data: %{TX.0} found within %{MATCHED_VAR_NAME}: %{MATCHED_VAR}',\
    tag:'application-multi',\
    tag:'language-multi',\
    tag:'platform-sqlite',\
    tag:'attack-disclosure',\
    tag:'OWASP_CRS',\
    tag:'OWASP_CRS/LEAKAGE/ERRORS_SQL',\
    tag:'CWE-209',\
    ctl:auditLogParts=+E,\
    ver:'OWASP_CRS/3.2.0',\
    severity:'CRITICAL',\
    chain"
    SecRule RESPONSE_BODY "@rx (?i)(?:Warning.*sqlite_.*|Warning.*SQLite3::|SQLite/JDBCDriver|SQLite\.Exception|System\.Data\.SQLite\.SQLiteException)" \
        "capture,\
        setvar:'tx.outbound_anomaly_score_pl1=+%{tx.critical_anomaly_score}',\
        setvar:'tx.sql_injection_score=+%{tx.critical_anomaly_score}',\
        setvar:'tx.anomaly_score_pl1=+%{tx.critical_anomaly_score}'"

SecRule TX:sql_error_match "@eq 1" \
    "id:951260,\
    phase:4,\
    block,\
    capture,\
    t:none,\
    msg:'Sybase SQL信息泄露',\
    logdata:'Matched Data: %{TX.0} found within %{MATCHED_VAR_NAME}: %{MATCHED_VAR}',\
    tag:'application-multi',\
    tag:'language-multi',\
    tag:'platform-sybase',\
    tag:'attack-disclosure',\
    tag:'OWASP_CRS',\
    tag:'OWASP_CRS/LEAKAGE/ERRORS_SQL',\
    tag:'CWE-209',\
    ctl:auditLogParts=+E,\
    ver:'OWASP_CRS/3.2.0',\
    severity:'CRITICAL',\
    chain"
    SecRule RESPONSE_BODY "@rx (?i)(?:Sybase message:|Warning.*sybase.*|Sybase.*Server message.*)" \
        "capture,\
        setvar:'tx.outbound_anomaly_score_pl1=+%{tx.critical_anomaly_score}',\
        setvar:'tx.sql_injection_score=+%{tx.critical_anomaly_score}',\
        setvar:'tx.anomaly_score_pl1=+%{tx.critical_anomaly_score}'"



SecRule TX:EXECUTING_PARANOIA_LEVEL "@lt 2" "id:951013,phase:3,pass,nolog,skipAfter:END-RESPONSE-951-DATA-LEAKAGES-SQL"
SecRule TX:EXECUTING_PARANOIA_LEVEL "@lt 2" "id:951014,phase:4,pass,nolog,skipAfter:END-RESPONSE-951-DATA-LEAKAGES-SQL"
#
# -= Paranoia Level 2 =- (apply only when tx.executing_paranoia_level is sufficiently high: 2 or higher)
#



SecRule TX:EXECUTING_PARANOIA_LEVEL "@lt 3" "id:951015,phase:3,pass,nolog,skipAfter:END-RESPONSE-951-DATA-LEAKAGES-SQL"
SecRule TX:EXECUTING_PARANOIA_LEVEL "@lt 3" "id:951016,phase:4,pass,nolog,skipAfter:END-RESPONSE-951-DATA-LEAKAGES-SQL"
#
# -= Paranoia Level 3 =- (apply only when tx.executing_paranoia_level is sufficiently high: 3 or higher)
#



SecRule TX:EXECUTING_PARANOIA_LEVEL "@lt 4" "id:951017,phase:3,pass,nolog,skipAfter:END-RESPONSE-951-DATA-LEAKAGES-SQL"
SecRule TX:EXECUTING_PARANOIA_LEVEL "@lt 4" "id:951018,phase:4,pass,nolog,skipAfter:END-RESPONSE-951-DATA-LEAKAGES-SQL"
#
# -= Paranoia Level 4 =- (apply only when tx.executing_paranoia_level is sufficiently high: 4 or higher)
#



#
# -= Paranoia Levels Finished =-
#
SecMarker "END-RESPONSE-951-DATA-LEAKAGES-SQL"
