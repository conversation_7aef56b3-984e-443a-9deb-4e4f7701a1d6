com.opensymphony.xwork2
com.sun.org.apache
java.io.BufferedInputStream
java.io.BufferedReader
java.io.ByteArrayInputStream
java.io.ByteArrayOutputStream
java.io.CharArrayReader
java.io.DataInputStream
java.io.File
java.io.FileOutputStream
java.io.FilePermission
java.io.FileWriter
java.io.FilterInputStream
java.io.FilterOutputStream
java.io.FilterReader
java.io.InputStream
java.io.InputStreamReader
java.io.LineNumberReader
java.io.ObjectOutputStream
java.io.OutputStream
java.io.PipedOutputStream
java.io.PipedReader
java.io.PrintStream
java.io.PushbackInputStream
java.io.Reader
java.io.StringReader
java.lang.Class
java.lang.Integer
java.lang.Number
java.lang.Object
java.lang.Process
java.lang.ProcessBuilder
java.lang.reflect
java.lang.Runtime
java.lang.String
java.lang.StringBuilder
java.lang.System
javax.script.ScriptEngineManager
org.apache.commons
org.apache.struts
org.apache.struts2
org.omg.CORBA
java.beans.XMLDecode
