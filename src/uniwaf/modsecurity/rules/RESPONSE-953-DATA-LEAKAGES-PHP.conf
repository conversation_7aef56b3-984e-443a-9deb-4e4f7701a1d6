###
# ------------------------------------------------------------------------
# OWASP ModSecurity Core Rule Set ver.3.2.0
# Copyright (c) 2006-2019 Trustwave and contributors. All rights reserved.
#
# The OWASP ModSecurity Core Rule Set is distributed under
# Apache Software License (ASL) version 2
# Please see the enclosed LICENSE file for full details.
# ------------------------------------------------------------------------

#
# -= Paranoia Level 0 (empty) =- (apply unconditionally)
#



SecRule TX:EXECUTING_PARANOIA_LEVEL "@lt 1" "id:953011,phase:3,pass,nolog,skipAfter:END-RESPONSE-953-DATA-LEAKAGES-PHP"
SecRule TX:EXECUTING_PARANOIA_LEVEL "@lt 1" "id:953012,phase:4,pass,nolog,skipAfter:END-RESPONSE-953-DATA-LEAKAGES-PHP"
#
# -= Paranoia Level 1 (default) =- (apply only when tx.executing_paranoia_level is sufficiently high: 1 or higher)
#

#
# -=[ PHP Error Message Leakage ]=-
#
SecRule RESPONSE_BODY "@pmFromFile php-errors.data" \
    "id:953100,\
    phase:4,\
    block,\
    capture,\
    t:none,\
    msg:'PHP信息泄露',\
    logdata:'Matched Data: %{TX.0} found within %{MATCHED_VAR_NAME}: %{MATCHED_VAR}',\
    tag:'application-multi',\
    tag:'language-php',\
    tag:'platform-multi',\
    tag:'attack-disclosure',\
    tag:'OWASP_CRS',\
    tag:'OWASP_CRS/LEAKAGE/ERRORS_PHP',\
    tag:'WASCTC/WASC-13',\
    tag:'OWASP_TOP_10/A6',\
    tag:'PCI/6.5.6',\
    ctl:auditLogParts=+E,\
    ver:'OWASP_CRS/3.2.0',\
    severity:'ERROR',\
    setvar:'tx.outbound_anomaly_score_pl1=+%{tx.error_anomaly_score}',\
    setvar:'tx.anomaly_score_pl1=+%{tx.error_anomaly_score}'"

#
# -=[ PHP source code leakage ]=-
#
# Detect some common PHP keywords in output.
#
SecRule RESPONSE_BODY "@rx (?:\b(?:f(?:tp_(?:nb_)?f?(?:ge|pu)t|get(?:s?s|c)|scanf|write|open|read)|gz(?:(?:encod|writ)e|compress|open|read)|s(?:ession_start|candir)|read(?:(?:gz)?file|dir)|move_uploaded_file|(?:proc_|bz)open|call_user_func)|\$_(?:(?:pos|ge)t|session))\b" \
    "id:953110,\
    phase:4,\
    block,\
    capture,\
    t:none,\
    msg:'PHP源代码泄漏',\
    logdata:'Matched Data: %{TX.0} found within %{MATCHED_VAR_NAME}: %{MATCHED_VAR}',\
    tag:'application-multi',\
    tag:'language-php',\
    tag:'platform-multi',\
    tag:'attack-disclosure',\
    tag:'OWASP_CRS',\
    tag:'OWASP_CRS/LEAKAGE/SOURCE_CODE_PHP',\
    tag:'WASCTC/WASC-13',\
    tag:'OWASP_TOP_10/A6',\
    tag:'PCI/6.5.6',\
    ctl:auditLogParts=+E,\
    ver:'OWASP_CRS/3.2.0',\
    severity:'ERROR',\
    setvar:'tx.outbound_anomaly_score_pl1=+%{tx.error_anomaly_score}',\
    setvar:'tx.anomaly_score_pl1=+%{tx.error_anomaly_score}'"

# Detect the presence of the PHP open tag "<?" or "<?php" in output.
#
# To prevent false positives due to the short "<?" sequence, an attempt
# is made to stop alerts in binary output. This is done by detecting
# some common binary file format headers, such as gzip (\x1f\x8b\x08),
# png (IHDR), mp3 (ID3), movie formats et cetera.
#
# Not supported by re2 (?!re).
#
SecRule RESPONSE_BODY "@rx <\?(?!xml)" \
    "id:953120,\
    phase:4,\
    block,\
    capture,\
    t:none,\
    msg:'PHP源代码泄漏',\
    logdata:'Matched Data: %{TX.0} found within %{MATCHED_VAR_NAME}: %{MATCHED_VAR}',\
    tag:'application-multi',\
    tag:'language-php',\
    tag:'platform-multi',\
    tag:'attack-disclosure',\
    tag:'OWASP_CRS',\
    tag:'OWASP_CRS/LEAKAGE/SOURCE_CODE_PHP',\
    tag:'WASCTC/WASC-13',\
    tag:'OWASP_TOP_10/A6',\
    tag:'PCI/6.5.6',\
    ctl:auditLogParts=+E,\
    ver:'OWASP_CRS/3.2.0',\
    severity:'ERROR',\
    chain"
    SecRule RESPONSE_BODY "!@rx (?:\x1f\x8b\x08|\b(?:(?:i(?:nterplay|hdr|d3)|m(?:ovi|thd)|r(?:ar!|iff)|(?:ex|jf)if|f(?:lv|ws)|varg|cws)\b|gif)|B(?:%pdf|\.ra)\b)" \
        "capture,\
        t:none,\
        setvar:'tx.outbound_anomaly_score_pl1=+%{tx.error_anomaly_score}',\
        setvar:'tx.anomaly_score_pl1=+%{tx.error_anomaly_score}'"


SecRule TX:EXECUTING_PARANOIA_LEVEL "@lt 2" "id:953013,phase:3,pass,nolog,skipAfter:END-RESPONSE-953-DATA-LEAKAGES-PHP"
SecRule TX:EXECUTING_PARANOIA_LEVEL "@lt 2" "id:953014,phase:4,pass,nolog,skipAfter:END-RESPONSE-953-DATA-LEAKAGES-PHP"
#
# -= Paranoia Level 2 =- (apply only when tx.executing_paranoia_level is sufficiently high: 2 or higher)
#



SecRule TX:EXECUTING_PARANOIA_LEVEL "@lt 3" "id:953015,phase:3,pass,nolog,skipAfter:END-RESPONSE-953-DATA-LEAKAGES-PHP"
SecRule TX:EXECUTING_PARANOIA_LEVEL "@lt 3" "id:953016,phase:4,pass,nolog,skipAfter:END-RESPONSE-953-DATA-LEAKAGES-PHP"
#
# -= Paranoia Level 3 =- (apply only when tx.executing_paranoia_level is sufficiently high: 3 or higher)
#



SecRule TX:EXECUTING_PARANOIA_LEVEL "@lt 4" "id:953017,phase:3,pass,nolog,skipAfter:END-RESPONSE-953-DATA-LEAKAGES-PHP"
SecRule TX:EXECUTING_PARANOIA_LEVEL "@lt 4" "id:953018,phase:4,pass,nolog,skipAfter:END-RESPONSE-953-DATA-LEAKAGES-PHP"
#
# -= Paranoia Level 4 =- (apply only when tx.executing_paranoia_level is sufficiently high: 4 or higher)
#



#
# -= Paranoia Levels Finished =-
#
SecMarker "END-RESPONSE-953-DATA-LEAKAGES-PHP"
