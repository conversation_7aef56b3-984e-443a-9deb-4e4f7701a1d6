###
# ------------------------------------------------------------------------
# OWASP ModSecurity Core Rule Set ver.3.2.0
# Copyright (c) 2006-2019 Trustwave and contributors. All rights reserved.
#
# The OWASP ModSecurity Core Rule Set is distributed under
# Apache Software License (ASL) version 2
# Please see the enclosed LICENSE file for full details.
# ------------------------------------------------------------------------

#
# Some protocol violations are common in application layer attacks.
# Validating HTTP requests eliminates a large number of application layer attacks.
#
# The purpose of this rules file is to enforce HTTP RFC requirements that state how
# the client is supposed to interact with the server.
# https://www.w3.org/Protocols/rfc2616/rfc2616-sec3.html



#
# -= Paranoia Level 0 (empty) =- (apply unconditionally)
#


SecRule TX:EXECUTING_PARANOIA_LEVEL "@lt 1" "id:920011,phase:1,pass,nolog,skipAfter:END-REQUEST-920-PROTOCOL-ENFORCEMENT"
SecRule TX:EXECUTING_PARANOIA_LEVEL "@lt 1" "id:920012,phase:2,pass,nolog,skipAfter:END-REQUEST-920-PROTOCOL-ENFORCEMENT"
#
# -= Paranoia Level 1 (default) =- (apply only when tx.executing_paranoia_level is sufficiently high: 1 or higher)
#

#
# Validate request line against the format specified in the HTTP RFC
#
# -=[ Rule Logic ]=-
#
# Uses rule negation against the regex for positive security.   The regex specifies the proper
# construction of URI request lines such as:
#
#   "http:" "//" host [ ":" port ] [ abs_path [ "?" query ]]
#
# It also outlines proper construction for CONNECT, OPTIONS and GET requests.
#
# -=[ References ]=-
# https://www.w3.org/Protocols/rfc2616/rfc2616-sec3.html#sec3.2.1
# http://capec.mitre.org/data/definitions/272.html
#
SecRule REQUEST_LINE "!@rx ^(?i:(?:[a-z]{3,10}\s+(?:\w{3,7}?://[\w\-\./]*(?::\d+)?)?/[^?#]*(?:\?[^#\s]*)?(?:#[\S]*)?|connect (?:\d{1,3}\.){3}\d{1,3}\.?(?::\d+)?|options \*)\s+[\w\./]+|get /[^?#]*(?:\?[^#\s]*)?(?:#[\S]*)?)$" \
    "id:920100,\
    phase:2,\
    block,\
    t:none,\
    msg:'无效的HTTP请求行',\
    logdata:'%{request_line}',\
    tag:'application-multi',\
    tag:'language-multi',\
    tag:'platform-multi',\
    tag:'attack-protocol',\
    tag:'OWASP_CRS',\
    tag:'OWASP_CRS/PROTOCOL_VIOLATION/INVALID_REQ',\
    tag:'CAPEC-272',\
    ver:'OWASP_CRS/3.2.0',\
    severity:'WARNING',\
    setvar:'tx.anomaly_score_pl1=+%{tx.notice_anomaly_score}'"


#
# Identify multipart/form-data name evasion attempts
#
# There are possible impedance mismatches between how
# ModSecurity interprets multipart file names and how
# a destination app server such as PHP might parse the
# Content-Disposition data:
#
#            filename-parm := "filename" "=" value
#
# -=[ Rule Logic ]=-
# These rules check for the existence of the ' " ; = meta-characters in
# either the file or file name variables.
# HTML entities may lead to false positives, why they are allowed on PL1.
# Negative look behind assertions allow frequently used entities &_;
#
# -=[ Targets, characters and html entities ]=-
#
# 920120: PL1 : FILES_NAMES, FILES
#                ['\";=] but allowed:
#                &[aAoOuUyY]uml); &[aAeEiIoOuU]circ; &[eEiIoOuUyY]acute;
#                &[aAeEiIoOuU]grave; &[cC]cedil; &[aAnNoO]tilde; &amp; &apos;
#
# 920121: PL2 : FILES_NAMES, FILES
#                ['\";=] : ' " ; = meta-characters
#
# Not supported by re2 (?<!re).
#
# -=[ References ]=-
# https://www.owasp.org/index.php/ModSecurity_CRS_RuleID-960000
# http://www.ietf.org/rfc/rfc2183.txt
#
SecRule FILES_NAMES|FILES "@rx (?<!&(?:[aAoOuUyY]uml)|&(?:[aAeEiIoOuU]circ)|&(?:[eEiIoOuUyY]acute)|&(?:[aAeEiIoOuU]grave)|&(?:[cC]cedil)|&(?:[aAnNoO]tilde)|&(?:amp)|&(?:apos));|['\"=]" \
    "id:920120,\
    phase:2,\
    block,\
    t:none,t:urlDecodeUni,\
    msg:'尝试绕过多部分/表单数据',\
    logdata:'%{MATCHED_VAR}',\
    tag:'application-multi',\
    tag:'language-multi',\
    tag:'platform-multi',\
    tag:'attack-protocol',\
    tag:'OWASP_CRS',\
    tag:'OWASP_CRS/PROTOCOL_VIOLATION/INVALID_REQ',\
    tag:'CAPEC-272',\
    ver:'OWASP_CRS/3.2.0',\
    severity:'CRITICAL',\
    setvar:'tx.anomaly_score_pl1=+%{tx.critical_anomaly_score}'"


#
# Accept only digits in content length
#
# -=[ Rule Logic ]=-
# This rule uses ModSecurity's rule negation against the regex meaning if the Content-Length header
# is NOT all digits, then it will match.
#
# -=[ References ]=-
# https://www.w3.org/Protocols/rfc2616/rfc2616-sec14.html#sec14.13
#
SecRule REQUEST_HEADERS:Content-Length "!@rx ^\d+$" \
    "id:920160,\
    phase:1,\
    block,\
    t:none,\
    msg:'内容长度HTTP报头不是数字',\
    logdata:'%{MATCHED_VAR}',\
    tag:'application-multi',\
    tag:'language-multi',\
    tag:'platform-multi',\
    tag:'attack-protocol',\
    tag:'OWASP_CRS',\
    tag:'OWASP_CRS/PROTOCOL_VIOLATION/INVALID_HREQ',\
    tag:'CAPEC-272',\
    ver:'OWASP_CRS/3.2.0',\
    severity:'CRITICAL',\
    setvar:'tx.anomaly_score_pl1=+%{tx.critical_anomaly_score}'"


#
# Do not accept GET or HEAD requests with bodies
# HTTP standard allows GET requests to have a body but this
# feature is not used in real life. Attackers could try to force
# a request body on an unsuspecting web applications.
#
# -=[ Rule Logic ]=-
# This is a chained rule that first checks the Request Method.  If it is a
# GET or HEAD method, then it checks for the existence of a Content-Length
# header.  If the header exists and its payload is either not a 0 digit or not
# empty, then it will match.
#
# -=[ References ]=-
# https://www.w3.org/Protocols/rfc2616/rfc2616-sec4.html#sec4.3
#
SecRule REQUEST_METHOD "@rx ^(?:GET|HEAD)$" \
    "id:920170,\
    phase:2,\
    block,\
    t:none,\
    msg:'GET或HEAD带有Body内容的请求。',\
    logdata:'%{MATCHED_VAR}',\
    tag:'application-multi',\
    tag:'language-multi',\
    tag:'platform-multi',\
    tag:'attack-protocol',\
    tag:'OWASP_CRS',\
    tag:'OWASP_CRS/PROTOCOL_VIOLATION/INVALID_HREQ',\
    tag:'CAPEC-272',\
    ver:'OWASP_CRS/3.2.0',\
    severity:'CRITICAL',\
    chain"
    SecRule REQUEST_HEADERS:Content-Length "!@rx ^0?$" \
        "t:none,\
        setvar:'tx.anomaly_score_pl1=+%{tx.critical_anomaly_score}'"


#
# This is a sibling of rule 920170
#
SecRule REQUEST_METHOD "@rx ^(?:GET|HEAD)$" \
    "id:920171,\
    phase:2,\
    block,\
    t:none,\
    msg:'带有传输编码的GET或HEAD请求。',\
    logdata:'%{MATCHED_VAR}',\
    tag:'application-multi',\
    tag:'language-multi',\
    tag:'platform-multi',\
    tag:'attack-protocol',\
    tag:'OWASP_CRS',\
    tag:'OWASP_CRS/PROTOCOL_VIOLATION/INVALID_HREQ',\
    tag:'CAPEC-272',\
    ver:'OWASP_CRS/3.2.0',\
    severity:'CRITICAL',\
    chain"
    SecRule &REQUEST_HEADERS:Transfer-Encoding "!@eq 0" \
        "t:none,\
        setvar:'tx.anomaly_score_pl1=+%{tx.critical_anomaly_score}'"


#
# Require Content-Length or Transfer-Encoding to be provided with
# every POST request.
#
# -=[ Rule Logic ]=-
# This chained rule checks if the request method is POST, if so,
# it checks that a Content-Length or Transfer-Encoding headers are
# also present.
#
# SecRule REQUEST_METHOD "@rx ^POST$" \
#     "id:920180,\
#     phase:2,\
#     block,\
#     t:none,\
#     msg:'没有内容长度或传输编码头的POST。',\
#     logdata:'%{MATCHED_VAR}',\
#     tag:'application-multi',\
#     tag:'language-multi',\
#     tag:'platform-multi',\
#     tag:'attack-protocol',\
#     tag:'OWASP_CRS',\
#     tag:'OWASP_CRS/PROTOCOL_VIOLATION/INVALID_HREQ',\
#     tag:'CAPEC-272',\
#     ver:'OWASP_CRS/3.2.0',\
#     severity:'WARNING',\
#     chain"
#     SecRule &REQUEST_HEADERS:Content-Length "@eq 0" \
#         "chain"
#         SecRule &REQUEST_HEADERS:Transfer-Encoding "@eq 0" \
#             "setvar:'tx.anomaly_score_pl1=+%{tx.notice_anomaly_score}'"


#
# Range Header Checks
#
# 1. Range Header exists and begins with 0 - normal browsers don't do this.
# Automated programs and bots often do not obey the HTTP RFC
#
# -=[ Rule Logic ]=-
# This rule inspects the Range request header to see if it starts with 0.
#
# -=[ References ]=-
# http://www.bad-behavior.ioerror.us/about/
# https://tools.ietf.org/html/rfc7233
#
# 2. Per RFC 2616 -
#       "If the last-byte-pos value is present, it MUST be greater than or equal to the first-byte-pos in that byte-range-spec,
#       or the byte- range-spec is syntactically invalid."
# -=[ Rule Logic ]=-
# This rule compares the first and second byte ranges and flags when the first value is greater than the second.
#
# -=[ References ]=-
# https://www.w3.org/Protocols/rfc2616/rfc2616-sec14.html
# http://seclists.org/fulldisclosure/2011/Aug/175
#
SecRule REQUEST_HEADERS:Range|REQUEST_HEADERS:Request-Range "@rx (\d+)\-(\d+)\," \
    "id:920190,\
    phase:2,\
    block,\
    capture,\
    t:none,\
    msg:'范围:无效的最后字节值。',\
    logdata:'%{MATCHED_VAR}',\
    tag:'application-multi',\
    tag:'language-multi',\
    tag:'platform-multi',\
    tag:'attack-protocol',\
    tag:'OWASP_CRS',\
    tag:'OWASP_CRS/PROTOCOL_VIOLATION/INVALID_HREQ',\
    ver:'OWASP_CRS/3.2.0',\
    severity:'WARNING',\
    chain"
    SecRule TX:2 "!@ge %{tx.1}" \
        "setvar:'tx.anomaly_score_pl1=+%{tx.warning_anomaly_score}'"


#
# Broken/Malicous clients often have duplicate or conflicting headers
# Automated programs and bots often do not obey the HTTP RFC
#
# -=[ Rule Logic ]=-
# This rule inspects the Connection header and looks for duplicates of the
# keep-alive and close options.
#
# -=[ References ]=-
# http://www.bad-behavior.ioerror.us/about/
# https://tools.ietf.org/html/rfc7233
#
SecRule REQUEST_HEADERS:Connection "@rx \b(?:keep-alive|close),\s?(?:keep-alive|close)\b" \
    "id:920210,\
    phase:2,\
    block,\
    t:none,\
    msg:'发现多个/冲突的连接标头数据',\
    logdata:'%{MATCHED_VAR}',\
    tag:'application-multi',\
    tag:'language-multi',\
    tag:'platform-multi',\
    tag:'attack-protocol',\
    tag:'OWASP_CRS',\
    tag:'OWASP_CRS/PROTOCOL_VIOLATION/INVALID_HREQ',\
    ver:'OWASP_CRS/3.2.0',\
    severity:'WARNING',\
    setvar:'tx.anomaly_score_pl1=+%{tx.warning_anomaly_score}'"

#
# Check URL encodings
#
# -=[ Rule Logic ]=-
# There are two different chained rules.    We need to separate them as we are inspecting two
# different variables - REQUEST_URI and REQUEST_BODY.   For REQUEST_BODY, we only want to
# run the @validateUrlEncoding operator if the content-type is application/x-www-form-urlencoding.
#
# -=[ References ]=-
# http://www.ietf.org/rfc/rfc1738.txt
#
# -=[ Example payload ]=-
# http://localhost/?s=a%20b%20c%'/
# reason: %'/ is not a valid url encoding
#
SecRule REQUEST_URI "@rx \x25" \
    "id:920220,\
    phase:2,\
    block,\
    t:none,\
    msg:'URL编码滥用攻击尝试',\
    logdata:'%{MATCHED_VAR}',\
    tag:'application-multi',\
    tag:'language-multi',\
    tag:'platform-multi',\
    tag:'attack-protocol',\
    tag:'OWASP_CRS',\
    tag:'OWASP_CRS/PROTOCOL_VIOLATION/EVASION',\
    ver:'OWASP_CRS/3.2.0',\
    severity:'WARNING',\
    chain"
    SecRule REQUEST_URI "@validateUrlEncoding" \
        "setvar:'tx.anomaly_score_pl1=+%{tx.warning_anomaly_score}'"

SecRule REQUEST_HEADERS:Content-Type "@rx ^(?i)application/x-www-form-urlencoded" \
    "id:920240,\
    phase:2,\
    block,\
    t:none,\
    msg:'URL编码滥用攻击尝试',\
    logdata:'%{MATCHED_VAR}',\
    tag:'application-multi',\
    tag:'language-multi',\
    tag:'platform-multi',\
    tag:'attack-protocol',\
    tag:'OWASP_CRS',\
    tag:'OWASP_CRS/PROTOCOL_VIOLATION/EVASION',\
    ver:'OWASP_CRS/3.2.0',\
    severity:'WARNING',\
    chain"
    SecRule REQUEST_BODY "@rx \x25" \
        "chain"
        SecRule REQUEST_BODY "@validateUrlEncoding" \
            "setvar:'tx.anomaly_score_pl1=+%{tx.warning_anomaly_score}'"


#
# Check UTF enconding
# We only want to apply this check if UTF-8 encoding is actually used by the site, otherwise
# it will result in false positives.
#
# -=[ Rule Logic ]=-
# This chained rule first checks to see if the admin has set the TX:CRS_VALIDATE_UTF8_ENCODING
# variable in the crs-setup.conf file.
#
SecRule TX:CRS_VALIDATE_UTF8_ENCODING "@eq 1" \
    "id:920250,\
    phase:2,\
    block,\
    t:none,\
    msg:'UTF8编码滥用攻击尝试',\
    logdata:'%{MATCHED_VAR}',\
    tag:'application-multi',\
    tag:'language-multi',\
    tag:'platform-multi',\
    tag:'attack-protocol',\
    tag:'OWASP_CRS',\
    tag:'OWASP_CRS/PROTOCOL_VIOLATION/EVASION',\
    ver:'OWASP_CRS/3.2.0',\
    severity:'WARNING',\
    chain"
    SecRule REQUEST_FILENAME|ARGS|ARGS_NAMES "@validateUtf8Encoding" \
        "setvar:'tx.anomaly_score_pl1=+%{tx.warning_anomaly_score}'"


#
# Disallow use of full-width unicode as decoding evasions may be possible.
#
# -=[ Rule Logic ]=-
# This rule looks for full-width encoding by looking for %u followed by 2 'f'
# characters and then 2 hex characters. It is a vulnerability that affected
# IIS circa 2007.
# The rule will trigger on %uXXXX formatted chars that are full or half
# width, as explained above. This %uXXXX format is passed as a raw parameter
# and is (seemingly only) accepted by IIS (5.0, 6.0, 7.0, and 8.0). Other
# webservers will only process unicode chars presented as hex UTF-8 bytes.
#
# -=[ References ]=-
# http://www.kb.cert.org/vuls/id/739224
# https://www.checkpoint.com/defense/advisories/public/2007/cpai-2007-201.html
# https://github.com/SpiderLabs/owasp-modsecurity-crs/issues/719
#
SecRule REQUEST_URI|REQUEST_BODY "@rx \%u[fF]{2}[0-9a-fA-F]{2}" \
    "id:920260,\
    phase:2,\
    block,\
    t:none,\
    msg:'Unicode 全/半宽滥用攻击尝试',\
    logdata:'%{MATCHED_VAR_NAME}=%{MATCHED_VAR}',\
    tag:'application-multi',\
    tag:'language-multi',\
    tag:'platform-iis',\
    tag:'platform-windows',\
    tag:'attack-protocol',\
    ver:'OWASP_CRS/3.2.0',\
    severity:'WARNING',\
    setvar:'tx.anomaly_score_pl1=+%{tx.warning_anomaly_score}'"


#
# Restrict type of characters sent
#
# This is a rule with multiple stricter siblings that grows more
# restrictive in higher paranoia levels.
#
# -=[ Rule Logic ]=-
# This rule uses the @validateByteRange operator to restrict the request
# payloads.
#
# -=[ Targets and ASCII Ranges ]=-
#
# 920270: PL1 : REQUEST_URI, REQUEST_HEADERS, ARGS and ARGS_NAMES
#       ASCII 1-255 : Full ASCII range without null character
#
# 920271: PL2 : REQUEST_URI, REQUEST_HEADERS, ARGS and ARGS_NAMES
#       ASCII 9,10,13,32-126,128-255 : Full visible ASCII range, tab, newline
#
# 920272: PL3 : REQUEST_URI, REQUEST_HEADERS, ARGS, ARGS_NAMES and REQUEST_BODY
#       ASCII 32-36,38-126 : Visible lower ASCII range without percent symbol
#
# 920273: PL4 : ARGS, ARGS_NAMES and REQUEST_BODY
#       ASCII 38,44-46,48-58,61,65-90,95,97-122
#       A-Z a-z 0-9 = - _ . , : &
#
# 920274: PL4 : REQUEST_HEADERS without User-Agent, Referer and Cookie
#       ASCII 32,34,38,42-59,61,65-90,95,97-122
#       A-Z a-z 0-9 = - _ . , : & " * + / SPACE
#
# REQUEST_URI and REQUEST_HEADERS User-Agent, Referer and Cookie are very hard
# to restrict beyond the limits in 920272.
#
# 920274 generally has few positives. However, it would detect rare attacks
# on Accept request headers and friends.

SecRule REQUEST_URI|REQUEST_HEADERS|ARGS|ARGS_NAMES "@validateByteRange 1-255" \
    "id:920270,\
    phase:2,\
    block,\
    t:none,t:urlDecodeUni,\
    msg:'请求中的无效字符(空字符)',\
    logdata:'%{MATCHED_VAR_NAME}=%{MATCHED_VAR}',\
    tag:'application-multi',\
    tag:'language-multi',\
    tag:'platform-multi',\
    tag:'attack-protocol',\
    tag:'OWASP_CRS',\
    tag:'OWASP_CRS/PROTOCOL_VIOLATION/EVASION',\
    ver:'OWASP_CRS/3.2.0',\
    severity:'CRITICAL',\
    setvar:'tx.anomaly_score_pl1=+%{tx.error_anomaly_score}'"


#
# Do not accept requests without common headers.
# All normal web browsers include Host, User-Agent and Accept headers.
# Implies either an attacker or a legitimate automation client.
#

#
# Missing/Empty Host Header
#
# -=[ Rule Logic ]=-
# These rules will first check to see if a Host header is present.
# The second check is to see if a Host header exists but is empty.
#
SecRule &REQUEST_HEADERS:Host "@eq 0" \
    "id:920280,\
    phase:2,\
    pass,\
    t:none,\
    msg:'请求缺少主机标头',\
    tag:'application-multi',\
    tag:'language-multi',\
    tag:'platform-multi',\
    tag:'attack-protocol',\
    tag:'OWASP_CRS',\
    tag:'OWASP_CRS/PROTOCOL_VIOLATION/MISSING_HEADER_HOST',\
    tag:'WASCTC/WASC-21',\
    tag:'OWASP_TOP_10/A7',\
    tag:'PCI/6.5.10',\
    ver:'OWASP_CRS/3.2.0',\
    severity:'WARNING',\
    setvar:'tx.anomaly_score_pl1=+%{tx.warning_anomaly_score}',\
    skipAfter:END-HOST-CHECK"


SecRule REQUEST_HEADERS:Host "@rx ^$" \
    "id:920290,\
    phase:2,\
    pass,\
    t:none,\
    msg:'空主机头',\
    tag:'application-multi',\
    tag:'language-multi',\
    tag:'platform-multi',\
    tag:'attack-protocol',\
    tag:'OWASP_CRS',\
    tag:'OWASP_CRS/PROTOCOL_VIOLATION/MISSING_HEADER_HOST',\
    ver:'OWASP_CRS/3.2.0',\
    severity:'WARNING',\
    setvar:'tx.anomaly_score_pl1=+%{tx.warning_anomaly_score}'"

SecMarker "END-HOST-CHECK"


#
# Empty Accept Header
#
# -=[ Rule Logic ]=-
# This rule checks if an Accept header exists, but has an empty value.
# This is only allowed in combination with the OPTIONS method.
# Additionally, there are some clients sending empty Accept headers.
# They are covered in another chained rule checking the User-Agent.
# This technique demands a separate rule to detect an empty
# Accept header if there is no user agent. This is checked via
# the separate rule 920311.
#
# Exclude some common broken clients sending empty Accept header:
# "Business/******* CFNetwork/758.5.3 Darwin/15.6.0" (CRS issue #515)
# "Entreprise/********* CFNetwork/758.4.3 Darwin/15.5.0" (CRS issue #366)
#
# -=[ References ]=-
# https://github.com/SpiderLabs/owasp-modsecurity-crs/issues/366
#

SecRule REQUEST_HEADERS:Accept "@rx ^$" \
    "id:920310,\
    phase:2,\
    pass,\
    t:none,\
    msg:'请求有一个空的接受标头',\
    tag:'application-multi',\
    tag:'language-multi',\
    tag:'platform-multi',\
    tag:'attack-protocol',\
    tag:'OWASP_CRS',\
    tag:'OWASP_CRS/PROTOCOL_VIOLATION/MISSING_HEADER_ACCEPT',\
    ver:'OWASP_CRS/3.2.0',\
    severity:'NOTICE',\
    chain"
    SecRule REQUEST_METHOD "!@rx ^OPTIONS$" \
        "chain"
        SecRule REQUEST_HEADERS:User-Agent "!@pm AppleWebKit Android Business Enterprise Entreprise" \
            "t:none,\
            setvar:'tx.anomaly_score_pl1=+%{tx.notice_anomaly_score}'"

#
# This rule is a sibling of rule 920310.
#
SecRule REQUEST_HEADERS:Accept "@rx ^$" \
    "id:920311,\
    phase:2,\
    pass,\
    t:none,\
    msg:'请求有一个空的接受标头',\
    tag:'application-multi',\
    tag:'language-multi',\
    tag:'platform-multi',\
    tag:'attack-protocol',\
    tag:'OWASP_CRS',\
    tag:'OWASP_CRS/PROTOCOL_VIOLATION/MISSING_HEADER_ACCEPT',\
    ver:'OWASP_CRS/3.2.0',\
    severity:'NOTICE',\
    chain"
    SecRule REQUEST_METHOD "!@rx ^OPTIONS$" \
        "chain"
        SecRule &REQUEST_HEADERS:User-Agent "@eq 0" \
            "t:none,\
            setvar:'tx.anomaly_score_pl1=+%{tx.notice_anomaly_score}'"


#
# Empty User-Agent Header
#
# -=[ Rule Logic ]=-
# This rules will check to see if the User-Agent header is empty.
#
# Note that there is a second rule, 920320, which will check for
# the existence of the User-Agent header.
#

SecRule REQUEST_HEADERS:User-Agent "@rx ^$" \
    "id:920330,\
    phase:2,\
    pass,\
    t:none,\
    msg:'空用户代理头',\
    tag:'application-multi',\
    tag:'language-multi',\
    tag:'platform-multi',\
    tag:'attack-protocol',\
    tag:'OWASP_CRS',\
    tag:'OWASP_CRS/PROTOCOL_VIOLATION/EMPTY_HEADER_UA',\
    ver:'OWASP_CRS/3.2.0',\
    severity:'NOTICE',\
    setvar:'tx.anomaly_score_pl1=+%{tx.notice_anomaly_score}'"

#
# Missing Content-Type Header with Request Body
#
# -=[ Rule Logic]=-
# This rule will first check to see if the value of the Content-Length header is
# non-equal to 0. The chained rule is then checking the existence of the
# Content-Type header. The RFCs do not state there must be a
# Content-Type header. However, a request missing a Content-Header is a
# strong indication of a non-compliant browser.
#
# Also, omitting the CT header allows to bypass the Request Body Processor
# unless you set the optional tx.enforce_bodyproc_urlencoded variable.
#
# Note: in default settings, this behavior only provides a NOTICE and will
# not cause a request to be blocked. However, in paranoia level 2 or
# higher, we run sibling 920341, which DOES block these requests.
#
# -=[ References ]=-
# http://httpwg.org/specs/rfc7231.html#header.content-type

SecRule REQUEST_HEADERS:Content-Length "!@rx ^0$" \
    "id:920340,\
    phase:2,\
    pass,\
    t:none,\
    msg:'请求包含内容，但缺少内容类型标头',\
    tag:'application-multi',\
    tag:'language-multi',\
    tag:'platform-multi',\
    tag:'attack-protocol',\
    ver:'OWASP_CRS/3.2.0',\
    severity:'NOTICE',\
    chain"
    SecRule &REQUEST_HEADERS:Content-Type "@eq 0" \
        "t:none,\
        setvar:'tx.anomaly_score_pl1=+%{tx.notice_anomaly_score}'"

# Check that the host header is not an IP address
# This is not an HTTP RFC violation but it is indicative of automated client access.
# Many web-based worms propagate by scanning IP address blocks.
#
# -=[ Rule Logic ]=-
# This rule triggers if the Host header contains all digits (and possible port)
#
# -=[ References ]=-
# https://technet.microsoft.com/en-us/magazine/2005.01.hackerbasher.aspx
#

#SecRule REQUEST_HEADERS:Host "@rx ^[\d.:]+$" \
#    "id:920350,\
#    phase:2,\
#    block,\
#    t:none,\
#    msg:'主机头是一个数字IP地址',\
#    logdata:'%{MATCHED_VAR}',\
#    tag:'application-multi',\
#    tag:'language-multi',\
#    tag:'platform-multi',\
#    tag:'attack-protocol',\
#    tag:'OWASP_CRS',\
#    tag:'OWASP_CRS/PROTOCOL_VIOLATION/IP_HOST',\
#    tag:'WASCTC/WASC-21',\
#    tag:'OWASP_TOP_10/A7',\
#    tag:'PCI/6.5.10',\
#    ver:'OWASP_CRS/3.2.0',\
#    severity:'WARNING',\
#    setvar:'tx.anomaly_score_pl1=+%{tx.warning_anomaly_score}'"


# In most cases, you should expect a certain volume of each a request on your
# website. For example, a request with 400 arguments, can be suspicious.
# This file creates limitations on the request.
#
# TODO Look at the rules in this file, and define the sizes you'd like to enforce.
#           Note that most of the rules are commented out by default.
#           Uncomment the rules you need
#


#
# Maximum number of arguments in request limited
#
SecRule &TX:MAX_NUM_ARGS "@eq 1" \
    "id:920380,\
    phase:2,\
    block,\
    t:none,\
    msg:'请求中的参数太多',\
    logdata:'%{MATCHED_VAR_NAME}=%{MATCHED_VAR}',\
    tag:'application-multi',\
    tag:'language-multi',\
    tag:'platform-multi',\
    tag:'attack-protocol',\
    tag:'OWASP_CRS',\
    tag:'OWASP_CRS/POLICY/SIZE_LIMIT',\
    ver:'OWASP_CRS/3.2.0',\
    severity:'CRITICAL',\
    chain"
    SecRule &ARGS "@gt %{tx.max_num_args}" \
        "t:none,\
        setvar:'tx.anomaly_score_pl1=+%{tx.critical_anomaly_score}'"

## -- Arguments limits --
#
# Limit argument name length
#
SecRule &TX:ARG_NAME_LENGTH "@eq 1" \
    "id:920360,\
    phase:2,\
    block,\
    t:none,\
    msg:'参数名称太长',\
    logdata:'%{MATCHED_VAR_NAME}=%{MATCHED_VAR}',\
    tag:'application-multi',\
    tag:'language-multi',\
    tag:'platform-multi',\
    tag:'attack-protocol',\
    tag:'OWASP_CRS',\
    tag:'OWASP_CRS/POLICY/SIZE_LIMIT',\
    ver:'OWASP_CRS/3.2.0',\
    severity:'CRITICAL',\
    chain"
    SecRule ARGS_NAMES "@gt %{tx.arg_name_length}" \
        "t:none,t:length,\
        setvar:'tx.anomaly_score_pl1=+%{tx.critical_anomaly_score}'"

#
# Limit argument value length
#
# This rule is also triggered by an Apache Struts Remote Code Execution exploit:
# [ Apache Struts vulnerability CVE-2017-9791 - Exploit tested: https://www.exploit-db.com/exploits/42324 ]
#
SecRule &TX:ARG_LENGTH "@eq 1" \
    "id:920370,\
    phase:2,\
    block,\
    t:none,\
    msg:'参数值太长',\
    logdata:'%{MATCHED_VAR_NAME}=%{MATCHED_VAR}',\
    tag:'application-multi',\
    tag:'language-multi',\
    tag:'platform-multi',\
    tag:'attack-protocol',\
    tag:'OWASP_CRS',\
    tag:'OWASP_CRS/POLICY/SIZE_LIMIT',\
    ver:'OWASP_CRS/3.2.0',\
    severity:'CRITICAL',\
    chain"
    SecRule ARGS "@gt %{tx.arg_length}" \
        "t:none,t:length,\
        setvar:'tx.anomaly_score_pl1=+%{tx.critical_anomaly_score}'"

#
# Limit arguments total length
#
SecRule &TX:TOTAL_ARG_LENGTH "@eq 1" \
    "id:920390,\
    phase:2,\
    block,\
    t:none,\
    msg:'总参数大小超过',\
    logdata:'%{MATCHED_VAR_NAME}=%{MATCHED_VAR}',\
    tag:'application-multi',\
    tag:'language-multi',\
    tag:'platform-multi',\
    tag:'attack-protocol',\
    tag:'OWASP_CRS',\
    tag:'OWASP_CRS/POLICY/SIZE_LIMIT',\
    ver:'OWASP_CRS/3.2.0',\
    severity:'CRITICAL',\
    chain"
    SecRule ARGS_COMBINED_SIZE "@gt %{tx.total_arg_length}" \
        "t:none,\
        setvar:'tx.anomaly_score_pl1=+%{tx.critical_anomaly_score}'"


#
# -- File upload limits --
#
# Individual file size is limited
SecRule &TX:MAX_FILE_SIZE "@eq 1" \
    "id:920400,\
    phase:2,\
    block,\
    t:none,\
    msg:'上传的文件太大',\
    tag:'application-multi',\
    tag:'language-multi',\
    tag:'platform-multi',\
    tag:'attack-protocol',\
    tag:'OWASP_CRS',\
    tag:'OWASP_CRS/POLICY/SIZE_LIMIT',\
    ver:'OWASP_CRS/3.2.0',\
    severity:'CRITICAL',\
    chain"
    SecRule REQUEST_HEADERS:Content-Type "@rx ^(?i)multipart/form-data" \
        "chain"
        SecRule REQUEST_HEADERS:Content-Length "@gt %{tx.max_file_size}" \
            "t:none,\
            setvar:'tx.anomaly_score_pl1=+%{tx.critical_anomaly_score}'"

#
# Combined file size is limited
#
SecRule &TX:COMBINED_FILE_SIZES "@eq 1" \
    "id:920410,\
    phase:2,\
    block,\
    t:none,\
    msg:'上传的文件总大小太大',\
    logdata:'%{MATCHED_VAR_NAME}=%{MATCHED_VAR}',\
    tag:'application-multi',\
    tag:'language-multi',\
    tag:'platform-multi',\
    tag:'attack-protocol',\
    tag:'OWASP_CRS',\
    tag:'OWASP_CRS/POLICY/SIZE_LIMIT',\
    ver:'OWASP_CRS/3.2.0',\
    severity:'CRITICAL',\
    chain"
    SecRule FILES_COMBINED_SIZE "@gt %{tx.combined_file_sizes}" \
        "t:none,\
        setvar:'tx.anomaly_score_pl1=+%{tx.critical_anomaly_score}'"



#
# Restrict which content-types we accept.
#

# Restrict Content-Type header to established patterns.
#
# This provides generic whitelist protection against vulnerabilities like
# Apache Struts Content-Type arbitrary command execution (CVE-2017-5638).
#
# Examples of allowed patterns:
# - text/plain
# - text/plain; charset="UTF-8"
# - multipart/form-data; boundary=----WebKitFormBoundary12345
#
SecRule REQUEST_HEADERS:Content-Type "!@rx ^[\w\d/\.\-\+]+(?:\s?;\s?(?:boundary|charset)\s?=\s?['\"\w\d\.\-]+)?$" \
    "id:920470,\
    phase:1,\
    block,\
    t:none,t:lowercase,\
    msg:'非法内容类型报头',\
    logdata:'%{MATCHED_VAR}',\
    tag:'application-multi',\
    tag:'language-multi',\
    tag:'platform-multi',\
    tag:'attack-protocol',\
    tag:'OWASP_CRS',\
    tag:'OWASP_CRS/PROTOCOL_VIOLATION/CONTENT_TYPE',\
    tag:'WASCTC/WASC-20',\
    tag:'OWASP_TOP_10/A1',\
    tag:'OWASP_AppSensor/EE2',\
    tag:'PCI/12.1',\
    ver:'OWASP_CRS/3.2.0',\
    severity:'CRITICAL',\
    setvar:'tx.anomaly_score_pl1=+%{tx.critical_anomaly_score}'"

# In case Content-Type header can be parsed, check the mime-type against
# the policy defined in the 'allowed_request_content_type' variable.
# To change your policy, edit crs-setup.conf and activate rule 900220.
SecRule REQUEST_HEADERS:Content-Type "@rx ^[^;\s]+" \
    "id:920420,\
    phase:2,\
    block,\
    capture,\
    t:none,\
    msg:'策略不允许请求内容类型',\
    logdata:'%{MATCHED_VAR}',\
    tag:'application-multi',\
    tag:'language-multi',\
    tag:'platform-multi',\
    tag:'attack-protocol',\
    tag:'OWASP_CRS',\
    tag:'OWASP_CRS/POLICY/CONTENT_TYPE_NOT_ALLOWED',\
    tag:'WASCTC/WASC-20',\
    tag:'OWASP_TOP_10/A1',\
    tag:'OWASP_AppSensor/EE2',\
    tag:'PCI/12.1',\
    ver:'OWASP_CRS/3.2.0',\
    severity:'CRITICAL',\
    chain"
    SecRule TX:0 "!@rx ^%{tx.allowed_request_content_type}$" \
        "t:none,\
        ctl:forceRequestBodyVariable=On,\
        setvar:'tx.anomaly_score_pl1=+%{tx.critical_anomaly_score}'"


#
# Restrict charset parameter within the content-type header
#
SecRule REQUEST_HEADERS:Content-Type "@rx charset\s*=\s*([^;\s]+)" \
    "id:920480,\
    phase:1,\
    block,\
    capture,\
    t:none,t:lowercase,\
    msg:'策略不允许请求内容类型字符集',\
    logdata:'%{MATCHED_VAR}',\
    tag:'application-multi',\
    tag:'language-multi',\
    tag:'platform-multi',\
    tag:'attack-protocol',\
    tag:'OWASP_CRS',\
    tag:'OWASP_CRS/PROTOCOL_VIOLATION/CONTENT_TYPE_CHARSET',\
    tag:'WASCTC/WASC-20',\
    tag:'OWASP_TOP_10/A1',\
    tag:'OWASP_AppSensor/EE2',\
    tag:'PCI/12.1',\
    ver:'OWASP_CRS/3.2.0',\
    severity:'CRITICAL',\
    chain"
    SecRule TX:1 "!@rx ^%{tx.allowed_request_content_type_charset}$" \
        "t:none,\
        ctl:forceRequestBodyVariable=On,\
        setvar:'tx.anomaly_score_pl1=+%{tx.critical_anomaly_score}'"


#
# Restrict protocol versions.
#
SecRule REQUEST_PROTOCOL "!@within %{tx.allowed_http_versions}" \
    "id:920430,\
    phase:1,\
    block,\
    t:none,\
    msg:'HTTP协议版本不被策略允许',\
    logdata:'%{MATCHED_VAR}',\
    tag:'application-multi',\
    tag:'language-multi',\
    tag:'platform-multi',\
    tag:'attack-protocol',\
    tag:'OWASP_CRS',\
    tag:'OWASP_CRS/POLICY/PROTOCOL_NOT_ALLOWED',\
    tag:'WASCTC/WASC-21',\
    tag:'OWASP_TOP_10/A6',\
    tag:'PCI/6.5.10',\
    ver:'OWASP_CRS/3.2.0',\
    severity:'CRITICAL',\
    setvar:'tx.anomaly_score_pl1=+%{tx.critical_anomaly_score}'"

#
# Restrict file extension
#
SecRule REQUEST_BASENAME "@rx \.([^.]+)$" \
    "id:920440,\
    phase:2,\
    block,\
    capture,\
    t:none,\
    msg:'URL文件扩展名受策略限制',\
    logdata:'%{TX.0}',\
    tag:'application-multi',\
    tag:'language-multi',\
    tag:'platform-multi',\
    tag:'attack-protocol',\
    tag:'OWASP_CRS',\
    tag:'OWASP_CRS/POLICY/EXT_RESTRICTED',\
    tag:'WASCTC/WASC-15',\
    tag:'OWASP_TOP_10/A7',\
    tag:'PCI/6.5.10',\
    ver:'OWASP_CRS/3.2.0',\
    severity:'CRITICAL',\
    setvar:'tx.extension=.%{tx.1}/',\
    chain"
    SecRule TX:EXTENSION "@within %{tx.restricted_extensions}" \
        "t:none,t:urlDecodeUni,t:lowercase,\
        setvar:'tx.anomaly_score_pl1=+%{tx.critical_anomaly_score}'"

#
# Restricted HTTP headers
#
# -=[ Rule Logic ]=-
# The use of certain headers is restricted. They are listed in the variable
# TX.restricted_headers.
#
# The headers are transformed into lowercase before the match.  In order to
# make sure that only complete header names are matching, the names in
# TX.restricted_headers are wrapped in slashes. This guarantees that the
# header Range (-> /range/) is not matching the restricted header
# /content-range/ for example.
#
# This is a chained rule, where the first rule fills a set of variables of the
# form TX.header_name_<HEADER_NAME>. The second rule is then executed for all
# variables of the form TX.header_name_<HEADER_NAME>.
#
# As a consequence of the construction of the rule, the alert message and the
# alert data will not display the original header name Content-Range, but
# /content-range/ instead.
#
#
# -=[ References ]=-
# https://access.redhat.com/security/vulnerabilities/httpoxy (Header Proxy)
#
SecRule REQUEST_HEADERS_NAMES "@rx ^.*$" \
    "id:920450,\
    phase:2,\
    block,\
    capture,\
    t:none,t:lowercase,\
    msg:'HTTP 标头受策略限制(%{MATCHED_VAR})',\
    logdata:'Restricted header detected: %{MATCHED_VAR}',\
    tag:'application-multi',\
    tag:'language-multi',\
    tag:'platform-multi',\
    tag:'attack-protocol',\
    tag:'OWASP_CRS',\
    tag:'OWASP_CRS/POLICY/HEADER_RESTRICTED',\
    tag:'WASCTC/WASC-21',\
    tag:'OWASP_TOP_10/A7',\
    tag:'PCI/12.1',\
    tag:'WASCTC/WASC-15',\
    tag:'OWASP_TOP_10/A7',\
    tag:'PCI/12.1',\
    ver:'OWASP_CRS/3.2.0',\
    severity:'CRITICAL',\
    setvar:'tx.header_name_%{tx.0}=/%{tx.0}/',\
    chain"
    SecRule TX:/^HEADER_NAME_/ "@within %{tx.restricted_headers}" \
        "setvar:'tx.anomaly_score_pl1=+%{tx.critical_anomaly_score}'"


SecRule TX:EXECUTING_PARANOIA_LEVEL "@lt 2" "id:920013,phase:1,pass,nolog,skipAfter:END-REQUEST-920-PROTOCOL-ENFORCEMENT"
SecRule TX:EXECUTING_PARANOIA_LEVEL "@lt 2" "id:920014,phase:2,pass,nolog,skipAfter:END-REQUEST-920-PROTOCOL-ENFORCEMENT"
#
# -= Paranoia Level 2 =- (apply only when tx.executing_paranoia_level is sufficiently high: 2 or higher)
#

#
# -=[ Rule Logic ]=-
#
# Check the number of range fields in the Range request header.
#
# An excessive number of Range request headers can be used to DoS a server.
# The original CVE proposed an arbitrary upper limit of 5 range fields.
#
# Several clients are known to request PDF fields with up to 62 range
# fields. Therefore the standard rule does not cover PDF files. This is
# performed in two separate (stricter) siblings of this rule.
#
# 920200: PL2: Limit of 5 range header fields for all filenames outside of PDFs
# 920201: PL2: Limit of 62 range header fields for PDFs
# 920202: PL4: Limit of 5 range header fields for PDFs
#
# -=[ References ]=-
# https://httpd.apache.org/security/CVE-2011-3192.txt


SecRule REQUEST_HEADERS:Range|REQUEST_HEADERS:Request-Range "@rx ^bytes=(?:(?:\d+)?\-(?:\d+)?\s*,?\s*){6}" \
    "id:920200,\
    phase:2,\
    block,\
    t:none,\
    msg:'范围:字段太多(6个或更多)',\
    logdata:'%{MATCHED_VAR}',\
    tag:'application-multi',\
    tag:'language-multi',\
    tag:'platform-multi',\
    tag:'attack-protocol',\
    tag:'OWASP_CRS',\
    tag:'OWASP_CRS/PROTOCOL_VIOLATION/INVALID_HREQ',\
    tag:'paranoia-level/2',\
    ver:'OWASP_CRS/3.2.0',\
    severity:'WARNING',\
    chain"
    SecRule REQUEST_BASENAME "!@endsWith .pdf" \
        "setvar:'tx.anomaly_score_pl2=+%{tx.warning_anomaly_score}'"

#
# This is a sibling of rule 920200
#

SecRule REQUEST_BASENAME "@endsWith .pdf" \
    "id:920201,\
    phase:2,\
    block,\
    t:none,\
    msg:'范围:pdf请求的字段太多(63或更多)',\
    logdata:'%{MATCHED_VAR}',\
    tag:'application-multi',\
    tag:'language-multi',\
    tag:'platform-multi',\
    tag:'attack-protocol',\
    tag:'OWASP_CRS',\
    tag:'OWASP_CRS/PROTOCOL_VIOLATION/INVALID_HREQ',\
    tag:'paranoia-level/2',\
    ver:'OWASP_CRS/3.2.0',\
    severity:'WARNING',\
    chain"
    SecRule REQUEST_HEADERS:Range|REQUEST_HEADERS:Request-Range "@rx ^bytes=(?:(?:\d+)?\-(?:\d+)?\s*,?\s*){63}" \
        "setvar:'tx.anomaly_score_pl2=+%{tx.warning_anomaly_score}'"


SecRule ARGS "@rx %[0-9a-fA-F]{2}" \
    "id:920230,\
    phase:2,\
    block,\
    t:none,\
    msg:'检测到多个URL编码',\
    logdata:'%{MATCHED_VAR}',\
    tag:'application-multi',\
    tag:'language-multi',\
    tag:'platform-multi',\
    tag:'attack-protocol',\
    tag:'OWASP_CRS',\
    tag:'OWASP_CRS/PROTOCOL_VIOLATION/EVASION',\
    tag:'paranoia-level/2',\
    ver:'OWASP_CRS/3.2.0',\
    severity:'WARNING',\
    setvar:'tx.anomaly_score_pl2=+%{tx.warning_anomaly_score}'"


#
# Missing Accept Header
#
# -=[ Rule Logic ]=-
# This rule generates a notice if the Accept header is missing.
#
SecRule &REQUEST_HEADERS:Accept "@eq 0" \
    "id:920300,\
    phase:2,\
    pass,\
    t:none,\
    msg:'请求缺少接受标头',\
    tag:'application-multi',\
    tag:'language-multi',\
    tag:'platform-multi',\
    tag:'attack-protocol',\
    tag:'OWASP_CRS',\
    tag:'OWASP_CRS/PROTOCOL_VIOLATION/MISSING_HEADER_ACCEPT',\
    tag:'WASCTC/WASC-21',\
    tag:'OWASP_TOP_10/A7',\
    tag:'PCI/6.5.10',\
    tag:'paranoia-level/2',\
    ver:'OWASP_CRS/3.2.0',\
    severity:'NOTICE',\
    chain"
    SecRule REQUEST_METHOD "!@rx ^OPTIONS$" \
        "chain"
        SecRule REQUEST_HEADERS:User-Agent "!@pm AppleWebKit Android" \
            "t:none,\
            setvar:'tx.anomaly_score_pl2=+%{tx.notice_anomaly_score}'"

#
# PL2: This is a stricter sibling of 920270.
#
SecRule REQUEST_URI|REQUEST_HEADERS|ARGS|ARGS_NAMES "@validateByteRange 9,10,13,32-126,128-255" \
    "id:920271,\
    phase:2,\
    block,\
    t:none,t:urlDecodeUni,\
    msg:'请求中的无效字符(不可打印字符)',\
    logdata:'%{MATCHED_VAR_NAME}=%{MATCHED_VAR}',\
    tag:'application-multi',\
    tag:'language-multi',\
    tag:'platform-multi',\
    tag:'attack-protocol',\
    tag:'OWASP_CRS',\
    tag:'OWASP_CRS/PROTOCOL_VIOLATION/EVASION',\
    tag:'paranoia-level/2',\
    ver:'OWASP_CRS/3.2.0',\
    severity:'CRITICAL',\
    setvar:'tx.anomaly_score_pl2=+%{tx.critical_anomaly_score}'"



#
# Missing User-Agent Header
#
# -=[ Rule Logic ]=-
# This rules will check to see if there is a User-Agent header or not.
#

SecRule &REQUEST_HEADERS:User-Agent "@eq 0" \
    "id:920320,\
    phase:2,\
    pass,\
    t:none,\
    msg:'丢失用户代理标头',\
    tag:'application-multi',\
    tag:'language-multi',\
    tag:'platform-multi',\
    tag:'attack-protocol',\
    tag:'OWASP_CRS',\
    tag:'OWASP_CRS/PROTOCOL_VIOLATION/MISSING_HEADER_UA',\
    tag:'WASCTC/WASC-21',\
    tag:'OWASP_TOP_10/A7',\
    tag:'PCI/6.5.10',\
    tag:'paranoia-level/2',\
    ver:'OWASP_CRS/3.2.0',\
    severity:'NOTICE',\
    setvar:'tx.anomaly_score_pl2=+%{tx.notice_anomaly_score}'"


#
# PL2: This is a stricter sibling of 920120.
#
SecRule FILES_NAMES|FILES "@rx ['\";=]" \
    "id:920121,\
    phase:2,\
    block,\
    t:none,t:urlDecodeUni,\
    msg:'尝试绕过多部分/表单数据',\
    logdata:'%{MATCHED_VAR}',\
    tag:'application-multi',\
    tag:'language-multi',\
    tag:'platform-multi',\
    tag:'attack-protocol',\
    tag:'OWASP_CRS',\
    tag:'OWASP_CRS/PROTOCOL_VIOLATION/INVALID_REQ',\
    tag:'CAPEC-272',\
    tag:'paranoia-level/2',\
    ver:'OWASP_CRS/3.2.0',\
    severity:'CRITICAL',\
    setvar:'tx.anomaly_score_pl2=+%{tx.critical_anomaly_score}'"


#
# PL2: Block on Missing Content-Type Header with Request Body
# This is a stricter sibling of rule 920340.
#
# -=[ References ]=-
# http://httpwg.org/specs/rfc7231.html#header.content-type

SecRule REQUEST_HEADERS:Content-Length "!@rx ^0$" \
    "id:920341,\
    phase:2,\
    block,\
    t:none,\
    msg:'包含内容的请求需要内容类型标头',\
    tag:'application-multi',\
    tag:'language-multi',\
    tag:'platform-multi',\
    tag:'attack-protocol',\
    tag:'paranoia-level/2',\
    ver:'OWASP_CRS/3.2.0',\
    severity:'CRITICAL',\
    chain"
    SecRule &REQUEST_HEADERS:Content-Type "@eq 0" \
        "t:none,\
        setvar:'tx.anomaly_score_pl2=+%{tx.critical_anomaly_score}'"


SecRule TX:EXECUTING_PARANOIA_LEVEL "@lt 3" "id:920015,phase:1,pass,nolog,skipAfter:END-REQUEST-920-PROTOCOL-ENFORCEMENT"
SecRule TX:EXECUTING_PARANOIA_LEVEL "@lt 3" "id:920016,phase:2,pass,nolog,skipAfter:END-REQUEST-920-PROTOCOL-ENFORCEMENT"
#
# -= Paranoia Level 3 =- (apply only when tx.executing_paranoia_level is sufficiently high: 3 or higher)
#

#
# PL 3: This is a stricter sibling of 920270. Ascii range: Printable characters in the low range
#
# This rule is also triggered by the following exploit(s):
# [ SAP CRM Java vulnerability CVE-2018-2380 - Exploit tested: https://www.exploit-db.com/exploits/44292 ]
#
SecRule REQUEST_URI|REQUEST_HEADERS|ARGS|ARGS_NAMES|REQUEST_BODY "@validateByteRange 32-36,38-126" \
    "id:920272,\
    phase:2,\
    block,\
    t:none,t:urlDecodeUni,\
    msg:'请求中的无效字符(在ascii 127以下的可打印字符之外)',\
    logdata:'%{MATCHED_VAR_NAME}=%{MATCHED_VAR}',\
    tag:'application-multi',\
    tag:'language-multi',\
    tag:'platform-multi',\
    tag:'attack-protocol',\
    tag:'OWASP_CRS',\
    tag:'OWASP_CRS/PROTOCOL_VIOLATION/EVASION',\
    tag:'paranoia-level/3',\
    ver:'OWASP_CRS/3.2.0',\
    severity:'CRITICAL',\
    setvar:'tx.anomaly_score_pl3=+%{tx.critical_anomaly_score}'"


#
# PL3: The little known x-up-devcap-post-charset request header can be used to submit
# a request with a different encoding as an alternative to the charset parameter in
# the Content-Type header. This can be used to circumvent charset restrictions on
# the Content-Type header in ASP.NET.
# Note that this only works in combination with a User-Agent prefix.
#
# This rule is based on a blog post by Soroush Dalili at
# https://soroush.secproject.com/blog/2019/05/x-up-devcap-post-charset-header-in-aspnet-to-bypass-wafs-again/
#
SecRule &REQUEST_HEADERS:x-up-devcap-post-charset "@ge 1" \
    "id:920490,\
    phase:1,\
    block,\
    t:none,\
    msg:'检测到请求标头 x-up-devcap-post-charset 与 User-Agent 的前缀“UP”组合',\
    logdata:'%{MATCHED_VAR_NAME}=%{MATCHED_VAR}',\
    tag:'language-aspnet',\
    tag:'platform-windows',\
    tag:'attack-protocol',\
    tag:'OWASP_CRS',\
    tag:'OWASP_CRS/PROTOCOL_VIOLATION/EVASION',\
    tag:'paranoia-level/3',\
    ver:'OWASP_CRS/3.2.0',\
    severity:'CRITICAL',\
    chain"
    SecRule REQUEST_HEADERS:User-Agent "@rx ^(?i)up" \
        "t:none,\
        setvar:'tx.anomaly_score_pl3=+%{tx.critical_anomaly_score}'"


SecRule TX:EXECUTING_PARANOIA_LEVEL "@lt 4" "id:920017,phase:1,pass,nolog,skipAfter:END-REQUEST-920-PROTOCOL-ENFORCEMENT"
SecRule TX:EXECUTING_PARANOIA_LEVEL "@lt 4" "id:920018,phase:2,pass,nolog,skipAfter:END-REQUEST-920-PROTOCOL-ENFORCEMENT"
#
# -= Paranoia Level 4 =- (apply only when tx.executing_paranoia_level is sufficiently high: 4 or higher)
#

#
# This is a stricter sibling of rule 920200
#

SecRule REQUEST_BASENAME "@endsWith .pdf" \
    "id:920202,\
    phase:2,\
    block,\
    t:none,\
    msg:'范围:pdf请求的字段太多(6个或更多)',\
    logdata:'%{MATCHED_VAR}',\
    tag:'application-multi',\
    tag:'language-multi',\
    tag:'platform-multi',\
    tag:'attack-protocol',\
    tag:'OWASP_CRS',\
    tag:'OWASP_CRS/PROTOCOL_VIOLATION/INVALID_HREQ',\
    tag:'paranoia-level/4',\
    ver:'OWASP_CRS/3.2.0',\
    severity:'WARNING',\
    chain"
    SecRule REQUEST_HEADERS:Range|REQUEST_HEADERS:Request-Range "@rx ^bytes=(?:(?:\d+)?\-(?:\d+)?\s*,?\s*){6}" \
        "setvar:'tx.anomaly_score_pl4=+%{tx.warning_anomaly_score}'"


#
# This is a stricter sibling of 920270.
#
# This rule is also triggered by the following exploit(s):
# [ SAP CRM Java vulnerability CVE-2018-2380 - Exploit tested: https://www.exploit-db.com/exploits/44292 ]
#
SecRule ARGS|ARGS_NAMES|REQUEST_BODY "@validateByteRange 38,44-46,48-58,61,65-90,95,97-122" \
    "id:920273,\
    phase:2,\
    block,\
    t:none,t:urlDecodeUni,\
    msg:'请求中的无效字符(超出了非常严格的集合)',\
    logdata:'%{MATCHED_VAR_NAME}=%{MATCHED_VAR}',\
    tag:'application-multi',\
    tag:'language-multi',\
    tag:'platform-multi',\
    tag:'attack-protocol',\
    tag:'OWASP_CRS',\
    tag:'OWASP_CRS/PROTOCOL_VIOLATION/EVASION',\
    tag:'paranoia-level/4',\
    ver:'OWASP_CRS/3.2.0',\
    severity:'CRITICAL',\
    setvar:'tx.anomaly_score_pl4=+%{tx.critical_anomaly_score}'"

#
# This is a stricter sibling of 920270.
#
SecRule REQUEST_HEADERS|!REQUEST_HEADERS:User-Agent|!REQUEST_HEADERS:Referer|!REQUEST_HEADERS:Cookie|!REQUEST_HEADERS:Sec-Fetch-User "@validateByteRange 32,34,38,42-59,61,65-90,95,97-122" \
    "id:920274,\
    phase:2,\
    block,\
    t:none,t:urlDecodeUni,\
    msg:'请求标头中无效字符(超出非常严格的集合)',\
    logdata:'%{MATCHED_VAR_NAME}=%{MATCHED_VAR}',\
    tag:'application-multi',\
    tag:'language-multi',\
    tag:'platform-multi',\
    tag:'attack-protocol',\
    tag:'OWASP_CRS',\
    tag:'OWASP_CRS/PROTOCOL_VIOLATION/EVASION',\
    tag:'paranoia-level/4',\
    ver:'OWASP_CRS/3.2.0',\
    severity:'CRITICAL',\
    setvar:'tx.anomaly_score_pl4=+%{tx.critical_anomaly_score}'"

#
# This is a stricter sibling of 920270.
# The 'Sec-Fetch-User' header may contain the '?' (63) character.
# Therefore we exclude this header from rule 920274 which forbids '?'.
# https://www.w3.org/TR/fetch-metadata/#http-headerdef-sec-fetch-user
#
SecRule REQUEST_HEADERS:Sec-Fetch-User "@validateByteRange 32,34,38,42-59,61,63,65-90,95,97-122" \
    "id:920275,\
    phase:2,\
    block,\
    t:none,t:urlDecodeUni,\
    msg:'请求标头中无效字符(超出非常严格的集合)',\
    logdata:'%{MATCHED_VAR_NAME}=%{MATCHED_VAR}',\
    tag:'application-multi',\
    tag:'language-multi',\
    tag:'platform-multi',\
    tag:'attack-protocol',\
    tag:'OWASP_CRS',\
    tag:'OWASP_CRS/PROTOCOL_VIOLATION/EVASION',\
    tag:'paranoia-level/4',\
    ver:'OWASP_CRS/3.2.0',\
    severity:'CRITICAL',\
    setvar:'tx.anomaly_score_pl4=+%{tx.critical_anomaly_score}'"

# -=[ Abnormal Character Escapes ]=-
#
# [ Rule Logic ]
# Consider the following payload: arg=cat+/e\tc/pa\ssw\d
# Here, \s and \d were only used to obfuscate the string passwd and a lot of
# parsers will silently ignore the non-necessary escapes. The case with \t is
# a bit different though, as \t is a natural escape for the TAB character,
# so we will avoid this (and \n, \r, etc.).
#
# This rule aims to detect non-necessary, abnormal escapes. You could say it is
# a nice way to forbid the backslash character where it is not needed.
#
# This is a new rule at paranoia level 4. We expect quite a few false positives
# for this rule and we will later evaluate if the rule makes any sense at all.
# The rule is redundant with 920273 and 920274 in PL4. But if the rule proofs
# to be useful and false positives remain at a reasonable level, then it might
# be shifted to PL3 in a future release, where it would be the only rule
# covering the backslash escape.
#
# We forbid backslashes followed by a list of basic ascii characters - unless
# the backslash is preceded by another backslash.
#
# This rule is also triggered by the following exploit(s):
# [ SAP CRM Java vulnerability CVE-2018-2380 - Exploit tested: https://www.exploit-db.com/exploits/44292 ]
#
SecRule REQUEST_URI|REQUEST_HEADERS|ARGS|ARGS_NAMES "@rx (?:^|[^\\\\])\\\\[cdeghijklmpqwxyz123456789]" \
    "id:920460,\
    phase:2,\
    block,\
    capture,\
    t:none,t:urlDecodeUni,t:htmlEntityDecode,t:lowercase,\
    log,\
    msg:'请求中出现异常字符转义',\
    logdata:'Matched Data: %{TX.0} found within %{MATCHED_VAR_NAME}: %{MATCHED_VAR}',\
    tag:'application-multi',\
    tag:'language-multi',\
    tag:'platform-multi',\
    tag:'attack-protocol',\
    tag:'paranoia-level/4',\
    ctl:auditLogParts=+E,\
    ver:'OWASP_CRS/3.2.0',\
    severity:'CRITICAL',\
    setvar:'tx.http_violation_score=+%{tx.critical_anomaly_score}',\
    setvar:'tx.anomaly_score_pl4=+%{tx.critical_anomaly_score}'"


#
# -= Paranoia Levels Finished =-
#
SecMarker "END-REQUEST-920-PROTOCOL-ENFORCEMENT"
