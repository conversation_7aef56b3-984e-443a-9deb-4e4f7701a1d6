###
# ------------------------------------------------------------------------
# OWASP ModSecurity Core Rule Set ver.3.2.0
# Copyright (c) 2006-2019 Trustwave and contributors. All rights reserved.
#
# The OWASP ModSecurity Core Rule Set is distributed under
# Apache Software License (ASL) version 2
# Please see the enclosed LICENSE file for full details.
# ------------------------------------------------------------------------

#
# -= Paranoia Level 0 (empty) =- (apply unconditionally)
#



SecRule TX:EXECUTING_PARANOIA_LEVEL "@lt 1" "id:911011,phase:1,pass,nolog,skipAfter:END-REQUEST-911-METHOD-ENFORCEMENT"
SecRule TX:EXECUTING_PARANOIA_LEVEL "@lt 1" "id:911012,phase:2,pass,nolog,skipAfter:END-REQUEST-911-METHOD-ENFORCEMENT"
#
# -= Paranoia Level 1 (default) =- (apply only when tx.executing_paranoia_level is sufficiently high: 1 or higher)
#

#
# -=[ Allowed Request Methods ]=-
#
# tx.allowed_methods is defined in the crs-setup.conf file
#
# SecRule REQUEST_METHOD "!@within %{tx.allowed_methods}" \
#     "id:911100,\
#     phase:2,\
#     block,\
#     msg:'方法是不被策略允许的',\
#     logdata:'%{MATCHED_VAR}',\
#     tag:'application-multi',\
#     tag:'language-multi',\
#     tag:'platform-multi',\
#     tag:'attack-generic',\
#     tag:'OWASP_CRS',\
#     tag:'OWASP_CRS/POLICY/METHOD_NOT_ALLOWED',\
#     tag:'WASCTC/WASC-15',\
#     tag:'OWASP_TOP_10/A6',\
#     tag:'OWASP_AppSensor/RE1',\
#     tag:'PCI/12.1',\
#     ver:'OWASP_CRS/3.2.0',\
#     severity:'CRITICAL',\
#     setvar:'tx.anomaly_score_pl1=+%{tx.critical_anomaly_score}'"




SecRule TX:EXECUTING_PARANOIA_LEVEL "@lt 2" "id:911013,phase:1,pass,nolog,skipAfter:END-REQUEST-911-METHOD-ENFORCEMENT"
SecRule TX:EXECUTING_PARANOIA_LEVEL "@lt 2" "id:911014,phase:2,pass,nolog,skipAfter:END-REQUEST-911-METHOD-ENFORCEMENT"
#
# -= Paranoia Level 2 =- (apply only when tx.executing_paranoia_level is sufficiently high: 2 or higher)
#



SecRule TX:EXECUTING_PARANOIA_LEVEL "@lt 3" "id:911015,phase:1,pass,nolog,skipAfter:END-REQUEST-911-METHOD-ENFORCEMENT"
SecRule TX:EXECUTING_PARANOIA_LEVEL "@lt 3" "id:911016,phase:2,pass,nolog,skipAfter:END-REQUEST-911-METHOD-ENFORCEMENT"
#
# -= Paranoia Level 3 =- (apply only when tx.executing_paranoia_level is sufficiently high: 3 or higher)
#



SecRule TX:EXECUTING_PARANOIA_LEVEL "@lt 4" "id:911017,phase:1,pass,nolog,skipAfter:END-REQUEST-911-METHOD-ENFORCEMENT"
SecRule TX:EXECUTING_PARANOIA_LEVEL "@lt 4" "id:911018,phase:2,pass,nolog,skipAfter:END-REQUEST-911-METHOD-ENFORCEMENT"
#
# -= Paranoia Level 4 =- (apply only when tx.executing_paranoia_level is sufficiently high: 4 or higher)
#



#
# -= Paranoia Levels Finished =-
#
SecMarker "END-REQUEST-911-METHOD-ENFORCEMENT"
