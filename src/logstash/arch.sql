CREATE TABLE `ARCH_HOST` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `timestamp` INT NOT NULL,
    `message` VARCHAR(255) NOT NULL,
    `host` VA<PERSON>HAR(255) NOT NULL,
    `ARCH_SIZE` INT NOT NULL,
    `ARCH_TYPE` VARCHAR(10) NOT NULL,
    `ARCH_START` DATETIME NOT NULL,
    `ARCH_END` DATETIME NOT NULL,
    `ARCH_COUNT` INT NOT NULL,
    `source_collector` VARCHAR(255),        
    `SYSTEM_NAME` VARCHAR(255),
    `HOST_SYSTEM` VARCHAR(255),
    `SOURCE` VARCHAR(255),
    `HOST_BYTES` DOUBLE,
    `HOST_NET_DELAY_AVG` DOUBLE,
    `HOST_NET_DELAY_MAX` DOUBLE,
    `HOST_CPU_CORE` DOUBLE,                  
    `HOST_MEM_TOTAL` DOUBL<PERSON>,                 
    `HOST_DISK_TOTAL` DOUBLE,                
    `HOST_CPU_AVG` DOUBLE,                   
    `HOST_MEM_AVG` DOUBLE,                   
    `HOST_DISK_AVG` DOUBLE,                  
    `HOST_CPU_MAX` DOUBLE,                   
    `HOST_MEM_MAX` DOUBLE,                   
    `HOST_DISK_MAX` DOUBLE,                  
    `HOST_CPU_CORE_USE` DOUBLE               
) ENGINE=MyISAM DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;

CREATE TABLE `ARCH_MODULE` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `timestamp` INT NOT NULL,
    `message` VARCHAR(255) NOT NULL,
    `host` VARCHAR(255) NOT NULL,
    `ARCH_SIZE` INT NOT NULL,
    `ARCH_TYPE` VARCHAR(10) NOT NULL,
    `ARCH_START` DATETIME NOT NULL,
    `ARCH_END` DATETIME NOT NULL,
    `MODULE` VARCHAR(255),
    `APP_NAME` VARCHAR(255),
    `APP_ID` VARCHAR(50),
    `APP_HTTP_500_COUNT` INT NOT NULL,
    `ARCH_COUNT` INT NOT NULL,
    `APP_HTTP_400_COUNT` INT NOT NULL,
    `APP_HTTP_SLOW_COUNT` INT NOT NULL,
    `APP_HTTP_SLOW_AVG` DOUBLE,
    `APP_HTTP_SLOW_MAX` DOUBLE,
    `APP_BYTES` DOUBLE,
    `APP_HTTP_RESPONSE_MAX` DOUBLE,
    `APP_HTTP_RESPONSE_AVG` DOUBLE,
    `APP_HOME_PAGE_COUNT` INT NOT NULL,
    `APP_HTTP_COUNT` INT NOT NULL
) ENGINE=MyISAM DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;

CREATE TABLE `ARCH_SYSTEM` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `timestamp` INT NOT NULL,
    `message` VARCHAR(255) NOT NULL,
    `host` VARCHAR(255) NOT NULL,
    `ARCH_SIZE` INT NOT NULL,
    `ARCH_TYPE` VARCHAR(10) NOT NULL,
    `ARCH_START` DATETIME NOT NULL,
    `ARCH_END` DATETIME NOT NULL,
    `ARCH_COUNT` INT NOT NULL,
    `SYSTEM` VARCHAR(255),
    `HOST_SYSTEM` VARCHAR(50),
    `SOURCE` VARCHAR(255),
    `SOURCE_ID` VARCHAR(50),
    `SYSTEM_CPU_CORE_TOTAL` DOUBLE,
    `SYSTEM_MEM_TOTAL` DOUBLE,
    `SYSTEM_DISK_TOTAL` DOUBLE,
    `SYSTEM_MEM_USAGE` DOUBLE,
    `SYSTEM_DISK_USAGE` DOUBLE,
    `SYSTEM_MEM` DOUBLE,
    `SYSTEM_DISK` DOUBLE
) ENGINE=MyISAM DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;

CREATE TABLE `ARCH_NETWORK` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `timestamp` INT NOT NULL,
    `message` VARCHAR(255) NOT NULL,
    `host` VARCHAR(255) NOT NULL,
    `ARCH_SIZE` INT NOT NULL,
    `ARCH_TYPE` VARCHAR(10) NOT NULL,
    `ARCH_START` DATETIME NOT NULL,
    `ARCH_END` DATETIME NOT NULL,
    `ARCH_COUNT` INT NOT NULL,
    `NETWORK` VARCHAR(255),
    `APP_NAME` VARCHAR(255),
    `APP_ID` VARCHAR(50),
    `APP_BYTES` DOUBLE,
    `APP_HOME_PAGE_COUNT` INT,
    `APP_HTTP_COUNT` INT,
    `APP_HTTP_500_COUNT` INT NOT NULL,
    `APP_HTTP_400_COUNT` INT NOT NULL,
    `APP_HTTP_SLOW_COUNT` INT NOT NULL
) ENGINE=MyISAM DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;

CREATE TABLE `ARCH_APP` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `timestamp` INT NOT NULL,
    `message` VARCHAR(255) NOT NULL,
    `host` VARCHAR(255) NOT NULL,
    `ARCH_SIZE` INT NOT NULL,
    `ARCH_TYPE` VARCHAR(10) NOT NULL,
    `ARCH_START` DATETIME NOT NULL,
    `ARCH_END` DATETIME NOT NULL,
    `APP_NAME` VARCHAR(255),
    `APP_ID` VARCHAR(50),
    `APP_HTTP_500_COUNT` INT NOT NULL,
    `ARCH_COUNT` INT NOT NULL,
    `APP_HTTP_400_COUNT` INT NOT NULL,
    `APP_HTTP_SLOW_COUNT` INT NOT NULL,
    `APP_HTTP_SLOW_AVG` DOUBLE,
    `APP_HTTP_SLOW_MAX` DOUBLE,
    `APP_BYTES` DOUBLE,
    `APP_HTTP_RESPONSE_MAX` DOUBLE,
    `APP_HTTP_RESPONSE_AVG` DOUBLE,
    `APP_HOME_PAGE_COUNT` INT NOT NULL,
    `APP_HTTP_COUNT` INT NOT NULL
) ENGINE=MyISAM DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;

CREATE TABLE `ARCH_DEPARTMENT` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `timestamp` INT NOT NULL,
    `message` VARCHAR(255) NOT NULL,
    `host` VARCHAR(255) NOT NULL,
    `ARCH_SIZE` INT NOT NULL,
    `ARCH_TYPE` VARCHAR(10) NOT NULL,
    `ARCH_START` DATETIME NOT NULL,
    `ARCH_END` DATETIME NOT NULL,
    `ARCH_COUNT` INT NOT NULL,
    `DEPARTMENT` VARCHAR(255),
    `SOURCE_ID` VARCHAR(50),
    `DEPARTMENT_CPU_CORE_TOTAL` DOUBLE,
    `DEPARTMENT_MEM_TOTAL` DOUBLE,
    `DEPARTMENT_DISK_TOTAL` DOUBLE,
    `DEPARTMENT_MEM_USAGE` DOUBLE,
    `DEPARTMENT_DISK_USAGE` DOUBLE,
    `DEPARTMENT_MEM` DOUBLE,
    `DEPARTMENT_DISK` DOUBLE
) ENGINE=MyISAM DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
