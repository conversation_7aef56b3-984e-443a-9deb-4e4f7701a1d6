import sys
import logging

# 配置 logging
logging.basicConfig(level=logging.INFO)

# 获取 root logger
logger = logging.getLogger()

# 清空已有的处理器，避免重复添加
logger.handlers = []

# 创建一个输出处理器
handler = logging.StreamHandler(sys.stdout)

# 创建一个格式化器，并设置为立即刷新
formatter = logging.Formatter('%(asctime)s - %(message)s', datefmt='%Y-%m-%d %H:%M:%S')
handler.setFormatter(formatter)
handler.flush = sys.stdout.flush  # 设置为立即刷新

# 添加新的输出处理器
logger.addHandler(handler)
