import re
from datetime import datetime
from log import logger

def create_metrics_dict(group_title_list, alg_title_list, group_field_list, start, end, arch_type):
  start_str = datetime.fromtimestamp(start).strftime("%Y-%m-%d %H:%M:%S")
  end_str   = datetime.fromtimestamp(end).strftime("%Y-%m-%d %H:%M:%S")
  result = {
    # 必须字段
    "timestamp": start,
    "message": group_field_list[0],
    "host": group_field_list[0],
    # 归档信息
    "ARCH_SIZE": 3600,
    "ARCH_TYPE": arch_type,
    "ARCH_START": start_str,
    "ARCH_END": end_str,
    "ARCH_COUNT": 0
  }
  # 分组字段赋值
  for i in range(len(group_title_list)):
    try: result[group_title_list[i]] = group_field_list[i]
    except: result[group_title_list[i]] = None
  # 算法字段初始化
  for i in range(len(alg_title_list)): 
    result[alg_title_list[i]] = 0
  return result

def calc_metrics_ratio(metrics_dict, numerator, denominator, result):
  if numerator in metrics_dict and denominator in metrics_dict and metrics_dict[denominator] != 0:
    metrics_dict[result] = metrics_dict[numerator] / metrics_dict[denominator]
    return True
  else: 
    metrics_dict[result] = 0
    return False
