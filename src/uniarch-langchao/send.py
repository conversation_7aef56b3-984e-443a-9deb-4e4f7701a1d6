import sys
import json
import requests

filepath = sys.argv[1]

UNISERVER_ADDR = "172.31.0.13"
UNISERVER_DATA_PORT = 12201

gelf_url = f'http://{UNISERVER_ADDR}:{UNISERVER_DATA_PORT}/gelf'

with open(filepath, 'r', encoding='utf-8') as file:
  agg_result = json.load(file)
  with requests.Session() as session:
    for key, val in agg_result.items():
      message = json.dumps(val)
      session.post(gelf_url, data=message, headers={'Content-Type': 'application/json'})
