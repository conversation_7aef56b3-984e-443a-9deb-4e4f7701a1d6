import os
import k8sclient

# 容器IP就是主机IP，使用主机网络
curr_node_ip = os.getenv('POD_IP')

es_discovery_hosts = []
es_initial_master_nodes = []
              
node_info = k8sclient.get_node_info()

for ip, node in node_info.items():
  es_discovery_hosts.append(f"{ip}")      
  es_initial_master_nodes.append(f"es-{ip}")
                                      
print(f'node.name: es-{curr_node_ip}')
print(f'discovery.zen.ping.unicast.hosts: {es_discovery_hosts}')
print(f'cluster.initial_master_nodes: {es_initial_master_nodes}')
