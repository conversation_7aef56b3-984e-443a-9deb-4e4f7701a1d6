import os
import k8sclient

elasticsearch_hosts = []

# 容器IP就是主机IP，使用主机网络
curr_node_ip = os.getenv('POD_IP')

def gen_uniserver_conf(node_info):
  for ip, node in node_info.items():
    elasticsearch_hosts.append(f"http://{ip}:9200")
  elasticsearch_hosts_str = ",".join(elasticsearch_hosts)

  is_master = 'false'
  for ip, node in node_info.items():
    if node['is_master'] and curr_node_ip == node['ip']: 
      is_master = 'true'

  return elasticsearch_hosts_str, is_master

node_info = k8sclient.get_node_info()
es_str, is_master = gen_uniserver_conf(node_info)

print(f'elasticsearch_hosts = {es_str}')
print(f'is_master = {is_master}')
