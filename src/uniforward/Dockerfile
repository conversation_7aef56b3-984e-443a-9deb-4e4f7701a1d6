FROM registry.jxit.net.cn:5000/alpine:3.19.1
# FROM registry.jxit.net.cn:5000/alpine:3.19.1-arm

RUN sed -i '<EMAIL>@mirrors.tencent.com@g' /etc/apk/repositories && \
    apk update && \
    apk add tcpdump vim curl bash python3 py3-pip py3-flask tzdata && \
    cp /usr/share/zoneinfo/Asia/Shanghai /etc/localtime && \
    echo "Asia/Shanghai" > /etc/timezone

RUN pip3 install kafka-python --break-system-packages -i https://pypi.tuna.tsinghua.edu.cn/simple

WORKDIR /app

COPY . /app
