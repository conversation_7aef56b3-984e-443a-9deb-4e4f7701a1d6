import sys
import json
from flask import <PERSON>lask, request
from kafka import KafkaProducer

app = Flask(__name__)

if len(sys.argv) != 5:
  print("Usage: script.py <http_ip> <http_port> <kafka_broker> <kafka_topic>")
  sys.exit(1)

HTTP_IP = sys.argv[1]
HTTP_PORT = int(sys.argv[2])
KAFKA_BROKER = sys.argv[3]
KAFKA_TOPIC = sys.argv[4]

producer = KafkaProducer(bootstrap_servers=[KAFKA_BROKER],
                         value_serializer=lambda v: json.dumps(v).encode('utf-8'))

@app.route('/uniforward/handleUniserverAlert', methods=['POST'])
def handle_uniserver_alert():
  request_data = request.get_data(as_text=True)
  try:
    data = json.loads(request_data)
    group_by_fields = data["event"]["group_by_fields"]
    group_by_fields["MESSAGE"] = data["event_definition_title"]
    if "端口" in group_by_fields["MESSAGE"]:
      group_by_fields["MESSAGETYPE"] = "sensitiveport"
      group_by_fields["RULENAME"] = "敏感端口连接"
      group_by_fields["SEVERITY"] = "1"
    elif "外网" in group_by_fields["MESSAGE"]:
      group_by_fields["MESSAGETYPE"] = "outbound"
      group_by_fields["RULENAME"] = "外联行为"
      group_by_fields["SEVERITY"] = "1"

    group_by_fields = {key.upper().replace('_', ''): value for key, value in group_by_fields.items()}
    
    producer.send(KAFKA_TOPIC, group_by_fields)
    producer.flush()
  except json.JSONDecodeError:
    print("Invalid JSON format")

  return 'Request received', 200

if __name__ == '__main__':
  app.run(host=HTTP_IP, port=HTTP_PORT)

