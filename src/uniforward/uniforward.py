import socket
import gzip
import json
import io
import sys
from kafka import KafkaProducer

send_field_list = [
    "app_proto",
    "feature_field",
    "feature_payload",
    "host",
    "http_uri",
    "proto",
    "source",
    "src_ip",
    "src_port",
    "timestamp",
    "message",
    "dest_ip",
    "dest_port",
    "cookie",
    "method",
    "referer",
    "uri",
    "user_agent",
    "xff",
    "request_headers",
    "data",
    "response_headers",
    "status",
    "attacker_ip",
    "victim_ip",
    "rule_name",
    "severity",
    "attack_result",
    "http_response_body",
    "attack_method"
]

if len(sys.argv) != 5:
  print("Usage: script.py <udp_ip> <udp_port> <kafka_broker> <kafka_topic>")
  sys.exit(1)

UDP_IP = sys.argv[1]
UDP_PORT = int(sys.argv[2])
KAFKA_BROKER = sys.argv[3]
KAFKA_TOPIC = sys.argv[4]

sock = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
sock.bind((UDP_IP, UDP_PORT))

print(f"Listening on UDP port {UDP_PORT}")

producer = KafkaProducer(bootstrap_servers=[KAFKA_BROKER],
                         value_serializer=lambda v: json.dumps(v).encode('utf-8'))

while True:
  data, addr = sock.recvfrom(10240)

  try:
    with gzip.GzipFile(fileobj=io.BytesIO(data)) as f:
      decompressed_data = f.read()
    json_data = json.loads(decompressed_data)

    clean_data = {}
    for field in send_field_list:
        if field in json_data:
            clean_data[field.upper().replace('_', '')] = json_data[field]
        elif f'_{field}' in json_data:  # 使用elif避免重复赋值
            clean_data[field.upper().replace('_', '')] = json_data[f'_{field}']
        else:  # 使用else来处理未找到的情况
            clean_data[field.upper().replace('_', '')] = ''

    clean_data['MESSAGETYPE'] = 'common'
    producer.send(KAFKA_TOPIC, clean_data)
    producer.flush()
  except:
    pass

