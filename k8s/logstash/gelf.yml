apiVersion: v1
kind: ConfigMap
metadata:
  name: logstash-config
  namespace: uniserver
data:
  logstash.conf: |
    input {
      gelf {
        port => 58001
        # GELF input configuration for receiving messages in GELF format
        codec => json
        tags => ["port_58001"]
      }
      gelf {
        port => 15157
        # GELF input configuration for receiving messages in GELF format
        codec => json
        tags => ["port_15157"]
      }
    }
    
    filter {
      if "port_15157" in [tags] {
        # First ensure all fields exist with empty string values if they're missing
        mutate {
          add_field => {
            "[@metadata][required_fields]" => [
              "app_proto", "feature_field", "feature_payload", "host", "http_uri", "proto",
              "source", "src_ip", "src_port", "timestamp", "message", "dest_ip", "dest_port",
              "cookie", "method", "referer", "uri", "user_agent", "xff", "request_headers",
              "data", "response_headers", "status", "attacker_ip", "victim_ip", "rule_name",
              "severity", "attack_result", "http_response_body", "attack_method", "detail_info",
              "bulletin", "vuln_desc", "killchain", "vuln_harm", "direction", "vuln_type",
              "att_ck", "attack_type", "version", "flow_id", "source_host", "rule_id", "enable"
            ]
          }
        }
    
        ruby {
          code => '
            required_fields = event.get("[@metadata][required_fields]")
            required_fields.each do |field|
              if !event.include?(field)
                event.set(field, "")
              end
            end
            event.remove("[@metadata][required_fields]")
          '
        }
    
        # Then perform the rename operation
        mutate {
          rename => {
            "app_proto" => "APPPROTO"
            "feature_field" => "FEATUREFIELD"
            "feature_payload" => "FEATUREPAYLOAD"
            "host" => "HOST"
            "http_uri" => "HTTPURI"
            "proto" => "PROTO"
            "source" => "SOURCE"
            "src_ip" => "SRCIP"
            "src_port" => "SRCPORT"
            "timestamp" => "TIMESTAMP"
            "message" => "MESSAGE"
            "dest_ip" => "DESTIP"
            "dest_port" => "DESTPORT"
            "cookie" => "COOKIE"
            "method" => "METHOD"
            "referer" => "REFERER"
            "uri" => "URI"
            "user_agent" => "USERAGENT"
            "xff" => "XFF"
            "request_headers" => "REQUESTHEADERS"
            "data" => "DATA"
            "response_headers" => "RESPONSEHEADERS"
            "status" => "STATUS"
            "attacker_ip" => "ATTACKERIP"
            "victim_ip" => "VICTIMIP"
            "rule_name" => "RULENAME"
            "severity" => "SEVERITY"
            "attack_result" => "ATTACKRESULT"
            "http_response_body" => "HTTPRESPONSEBODY"
            "attack_method" => "ATTACKMETHOD"
            "detail_info" => "DETAILINFO"
            "bulletin" => "BULLETIN"
            "vuln_desc" => "VULNDESC"
            "killchain" => "KILLCHAIN"
            "vuln_harm" => "VULNHARM"
            "direction" => "DIRECTION"
            "vuln_type" => "VULNTYPE"
            "att_ck" => "ATTCK"
            "attack_type" => "ATTACKTYPE"
            "version" => "VERSION"
            "flow_id" => "FLOWID"
            "source_host" => "SOURCEHOST"
            "rule_id" => "RULEID"
            "enable" => "ENABLE"
          }
        }
        
        if [SRCIP] == [DESTIP] {
          drop { }
        }

        # Add fixed field
        mutate {
          add_field => { "[@metadata][message_type]" => "common" }
        }
      }
    }
    
    output {
      # Send output to 127.0.0.1:14444 over UDP with JSON format
      if "port_58001" in [tags] {
        udp {
          host => "************"
          port => 58001
          codec => json
        }
      }
      if "port_15157" in [tags] {
        kafka {
          codec => json
          topic_id => "jt_gzy"
          bootstrap_servers => "************:9092"
          # 使用元数据字段（可选）
          message_key => "%{[@metadata][message_type]}"
        }
        stdout { codec => rubydebug }
      }
    }

---
apiVersion: apps/v1
kind: DaemonSet
metadata:
  name: logstash
  namespace: uniserver
spec:
  selector:
    matchLabels:
      app: logstash
  template:
    metadata:
      labels:
        app: logstash
    spec:
      hostNetwork: true
      dnsPolicy: ClusterFirstWithHostNet
      containers:
      - name: logstash
        image: registry.jxit.net.cn:5000/uniserver/logstash-8.15.3-gelf-mysql:git-5
        ports:
        - containerPort: 8881
          hostPort: 8881
          name: gelf
        env:
        - name: TZ
          value: "Asia/Shanghai"
        volumeMounts:
        - name: logstash-config
          mountPath: /usr/share/logstash/pipeline/logstash.conf
          subPath: logstash.conf
      volumes:
      - name: logstash-config
        configMap:
          name: logstash-config

