apiVersion: v1
kind: ConfigMap
metadata:
  name: logstash-config
  namespace: uniserver
data:
  logstash.conf: |
    input {
      udp {
        port => 8881
        # codec => "gzip_lines"  # 使用 GZIP codec 直接处理 GZIP 格式的输入
      }
    }
    
    filter {
      json {
        source => "message"
      }
    }
    
    output {
      # stdout {
      #   codec => rubydebug
      # }
      # 插入到 ARCH_HOST 表
      if [ARCH_TYPE] == "HOST" {
        jdbc {
          driver_jar_path => "/opt/mysql-connector-j-8.4.0.jar"
          driver_class => "com.mysql.cj.jdbc.Driver"
          connection_string => "****************************"
          username => "jxadmin"
          password => "123456Ww"
          statement => ["INSERT INTO ARCH_HOST (timestamp, message, host, ARCH_SIZE, ARCH_TYPE, ARCH_START, ARCH_END, ARCH_COUNT, source_collector, SYSTEM_NAME, HOST_SYSTEM, SOURCE, HOST_BYTES, HOST_NET_DELAY_AVG, HOST_NET_DELAY_MAX, HOST_CPU_CORE, HOST_MEM_TOTAL, HOST_DISK_TOTAL, HOST_CPU_AVG, HOST_MEM_AVG, HOST_DISK_AVG, HOST_CPU_MAX, HOST_MEM_MAX, HOST_DISK_MAX, HOST_CPU_CORE_USE) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)", "timestamp", "message", "host", "ARCH_SIZE", "ARCH_TYPE", "ARCH_START", "ARCH_END", "ARCH_COUNT", "source_collector", "SYSTEM_NAME", "HOST_SYSTEM", "SOURCE", "HOST_BYTES", "HOST_NET_DELAY_AVG", "HOST_NET_DELAY_MAX", "HOST_CPU_CORE", "HOST_MEM_TOTAL", "HOST_DISK_TOTAL", "HOST_CPU_AVG", "HOST_MEM_AVG", "HOST_DISK_AVG", "HOST_CPU_MAX", "HOST_MEM_MAX", "HOST_DISK_MAX", "HOST_CPU_CORE_USE"]

        }
      }
    
      # 插入到 ARCH_MODULE 表
      if [ARCH_TYPE] == "MODULE" {
        jdbc {
          driver_jar_path => "/opt/mysql-connector-j-8.4.0.jar"
          driver_class => "com.mysql.cj.jdbc.Driver"
          connection_string => "****************************"
          username => "jxadmin"
          password => "123456Ww"
          statement => ["INSERT INTO ARCH_MODULE (timestamp, message, host, ARCH_SIZE, ARCH_TYPE, ARCH_START, ARCH_END, MODULE, APP_NAME, APP_ID, APP_HTTP_500_COUNT, ARCH_COUNT, APP_HTTP_400_COUNT, APP_HTTP_SLOW_COUNT, APP_HTTP_SLOW_AVG, APP_HTTP_SLOW_MAX, APP_BYTES, APP_HTTP_RESPONSE_MAX, APP_HTTP_RESPONSE_AVG, APP_HOME_PAGE_COUNT, APP_HTTP_COUNT) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)", "timestamp", "message", "host", "ARCH_SIZE", "ARCH_TYPE", "ARCH_START", "ARCH_END", "MODULE", "APP_NAME", "APP_ID", "APP_HTTP_500_COUNT", "ARCH_COUNT", "APP_HTTP_400_COUNT", "APP_HTTP_SLOW_COUNT", "APP_HTTP_SLOW_AVG", "APP_HTTP_SLOW_MAX", "APP_BYTES", "APP_HTTP_RESPONSE_MAX", "APP_HTTP_RESPONSE_AVG", "APP_HOME_PAGE_COUNT", "APP_HTTP_COUNT"]

        }
      }
    
      # 插入到 ARCH_SYSTEM 表
      if [ARCH_TYPE] == "SYSTEM" {
        jdbc {
          driver_jar_path => "/opt/mysql-connector-j-8.4.0.jar"
          driver_class => "com.mysql.cj.jdbc.Driver"
          connection_string => "****************************"
          username => "jxadmin"
          password => "123456Ww"
          statement => ["INSERT INTO ARCH_SYSTEM (timestamp, message, host, ARCH_SIZE, ARCH_TYPE, ARCH_START, ARCH_END, ARCH_COUNT, `SYSTEM`, HOST_SYSTEM, SOURCE, SOURCE_ID, SYSTEM_CPU_CORE_TOTAL, SYSTEM_MEM_TOTAL, SYSTEM_DISK_TOTAL, SYSTEM_MEM_USAGE, SYSTEM_DISK_USAGE, SYSTEM_MEM, SYSTEM_DISK) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)", "timestamp", "message", "host", "ARCH_SIZE", "ARCH_TYPE", "ARCH_START", "ARCH_END", "ARCH_COUNT", "SYSTEM", "HOST_SYSTEM", "SOURCE", "SOURCE_ID", "SYSTEM_CPU_CORE_TOTAL", "SYSTEM_MEM_TOTAL", "SYSTEM_DISK_TOTAL", "SYSTEM_MEM_USAGE", "SYSTEM_DISK_USAGE", "SYSTEM_MEM", "SYSTEM_DISK"]
        }
      }
    
      # 插入到 ARCH_NETWORK 表
      if [ARCH_TYPE] == "NETWORK" {
        jdbc {
          driver_jar_path => "/opt/mysql-connector-j-8.4.0.jar"
          driver_class => "com.mysql.cj.jdbc.Driver"
          connection_string => "****************************"
          username => "jxadmin"
          password => "123456Ww"
          statement => ["INSERT INTO ARCH_NETWORK (timestamp, message, host, ARCH_SIZE, ARCH_TYPE, ARCH_START, ARCH_END, ARCH_COUNT, NETWORK, APP_NAME, APP_ID, APP_BYTES, APP_HOME_PAGE_COUNT, APP_HTTP_COUNT, APP_HTTP_500_COUNT, APP_HTTP_400_COUNT, APP_HTTP_SLOW_COUNT) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)", "timestamp", "message", "host", "ARCH_SIZE", "ARCH_TYPE", "ARCH_START", "ARCH_END", "ARCH_COUNT", "NETWORK", "APP_NAME", "APP_ID", "APP_BYTES", "APP_HOME_PAGE_COUNT", "APP_HTTP_COUNT", "APP_HTTP_500_COUNT", "APP_HTTP_400_COUNT", "APP_HTTP_SLOW_COUNT"]

        }
      }
    
      # 插入到 ARCH_APP 表
      if [ARCH_TYPE] == "APP" {
        jdbc {
          driver_jar_path => "/opt/mysql-connector-j-8.4.0.jar"
          driver_class => "com.mysql.cj.jdbc.Driver"
          connection_string => "****************************"
          username => "jxadmin"
          password => "123456Ww"
          statement => ["INSERT INTO ARCH_APP (timestamp, message, host, ARCH_SIZE, ARCH_TYPE, ARCH_START, ARCH_END, APP_NAME, APP_ID, APP_HTTP_500_COUNT, ARCH_COUNT, APP_HTTP_400_COUNT, APP_HTTP_SLOW_COUNT, APP_HTTP_SLOW_AVG, APP_HTTP_SLOW_MAX, APP_BYTES, APP_HTTP_RESPONSE_MAX, APP_HTTP_RESPONSE_AVG, APP_HOME_PAGE_COUNT, APP_HTTP_COUNT) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)", "timestamp", "message", "host", "ARCH_SIZE", "ARCH_TYPE", "ARCH_START", "ARCH_END", "APP_NAME", "APP_ID", "APP_HTTP_500_COUNT", "ARCH_COUNT", "APP_HTTP_400_COUNT", "APP_HTTP_SLOW_COUNT", "APP_HTTP_SLOW_AVG", "APP_HTTP_SLOW_MAX", "APP_BYTES", "APP_HTTP_RESPONSE_MAX", "APP_HTTP_RESPONSE_AVG", "APP_HOME_PAGE_COUNT", "APP_HTTP_COUNT"]
        }
      }
    
      # 插入到 ARCH_DEPARTMENT 表
      if [ARCH_TYPE] == "DEPARTMENT" {
        jdbc {
          driver_jar_path => "/opt/mysql-connector-j-8.4.0.jar"
          driver_class => "com.mysql.cj.jdbc.Driver"
          connection_string => "****************************"
          username => "jxadmin"
          password => "123456Ww"
          statement => ["INSERT INTO ARCH_DEPARTMENT (timestamp, message, host, ARCH_SIZE, ARCH_TYPE, ARCH_START, ARCH_END, ARCH_COUNT, DEPARTMENT, SOURCE_ID, DEPARTMENT_CPU_CORE_TOTAL, DEPARTMENT_MEM_TOTAL, DEPARTMENT_DISK_TOTAL, DEPARTMENT_MEM_USAGE, DEPARTMENT_DISK_USAGE, DEPARTMENT_MEM, DEPARTMENT_DISK) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)", "timestamp", "message", "host", "ARCH_SIZE", "ARCH_TYPE", "ARCH_START", "ARCH_END", "ARCH_COUNT", "DEPARTMENT", "SOURCE_ID", "DEPARTMENT_CPU_CORE_TOTAL", "DEPARTMENT_MEM_TOTAL", "DEPARTMENT_DISK_TOTAL", "DEPARTMENT_MEM_USAGE", "DEPARTMENT_DISK_USAGE", "DEPARTMENT_MEM", "DEPARTMENT_DISK"]
        }
      }
    }

---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: logstash
  namespace: uniserver
spec:
  replicas: 1
  selector:
    matchLabels:
      app: logstash
  template:
    metadata:
      labels:
        app: logstash
    spec:
      hostNetwork: true
      dnsPolicy: ClusterFirstWithHostNet
      nodeSelector:
        node-role.kubernetes.io/master: "true"
      containers:
      - name: logstash
        image: registry.jxit.net.cn:5000/uniserver/logstash-8.15.3-gelf-mysql:git-5
        ports:
        - containerPort: 8881
          hostPort: 8881
          name: gelf
        env:
        - name: TZ
          value: "Asia/Shanghai"
        volumeMounts:
        - name: logstash-config
          mountPath: /usr/share/logstash/pipeline/logstash.conf
          subPath: logstash.conf
      volumes:
      - name: logstash-config
        configMap:
          name: logstash-config

