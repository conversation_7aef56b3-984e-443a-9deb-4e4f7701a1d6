apiVersion: apps/v1
kind: Deployment
metadata:
  name: mysql
  namespace: uniserver
  labels:
    app: mysql
spec:
  replicas: 1
  selector:
    matchLabels:
      app: mysql
  template:
    metadata:
      labels:
        app: mysql
    spec:
      nodeSelector:
        node-role.kubernetes.io/master: "true"
      containers:
      - name: mysql
        image: registry.jxit.net.cn:5000/mysql:8.0.36
        env:
        - name: MYSQL_ROOT_PASSWORD
          value: "123456Ww"
        ports:
        - containerPort: 3306
          name: tcp
          protocol: TCP
---
apiVersion: v1
kind: Service
metadata:
  name: mysql
  namespace: uniserver
  labels:
    app: mysql
spec:
  selector:
    app: mysql
  ports:
  - name: db
    port: 3306
    protocol: TCP
