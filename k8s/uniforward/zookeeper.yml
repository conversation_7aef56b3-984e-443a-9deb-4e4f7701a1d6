apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: zookeeper
  namespace: uniserver
spec:
  serviceName: "zookeeper"
  replicas: 1
  selector:
    matchLabels:
      app: zookeeper
  template:
    metadata:
      labels:
        app: zookeeper
    spec:
      hostNetwork: true
      containers:
      - name: zookeeper
        image: registry.jxit.net.cn:5000/bitnami/zookeeper:latest
        ports:
        - containerPort: 2181
          name: client
        env:
        - name: ALLOW_ANONYMOUS_LOGIN
          value: "yes"

