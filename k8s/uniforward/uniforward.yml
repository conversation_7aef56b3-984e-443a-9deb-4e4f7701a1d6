apiVersion: apps/v1
kind: DaemonSet
metadata:
  name: uniforward
  namespace: uniserver
spec:
  selector:
    matchLabels:
      app: uniforward
  template:
    metadata:
      labels:
        app: uniforward
    spec:
      hostNetwork: true
      dnsPolicy: ClusterFirstWithHostNet
      containers:
      - name: uniforward
        image: registry.jxit.net.cn:5000/uniserver/uniforward:git-28
        command:
        - sh
        - -c
        - |
          python3 handleUniserverAlert.py $UDP_IP $UDP_PORT $KAFKA_BROKER $KAFKA_TOPIC
        env:   
        - name: UDP_IP
          value: 127.0.0.1
        - name: UDP_PORT     
          value: "15159"                   
        - name: KAFKA_BROKER
          value: ************:9092
        - name: KAFKA_TOPIC
          value: gzytj
        ports:
        - containerPort: 15159
          hostPort: 15159
          name: syslog
