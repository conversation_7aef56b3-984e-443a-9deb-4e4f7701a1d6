apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: kafka
  namespace: uniserver
spec:
  serviceName: "kafka"
  replicas: 1
  selector:
    matchLabels:
      app: kafka
  template:
    metadata:
      labels:
        app: kafka
    spec:
      hostNetwork: true
      containers:
      - name: kafka
        image: registry.jxit.net.cn:5000/bitnami/kafka:latest
        ports:
        - containerPort: 9092
          name: client
        env:
        - name: KAFKA_BROKER_ID
          value: "1"
        - name: KAFKA_ZOOKEEPER_CONNECT
          value: "127.0.0.1:2181"
        - name: ALLOW_PLAINTEXT_LISTENER
          value: "yes"
        - name: <PERSON><PERSON><PERSON>_ADVERTISED_LISTENERS
          value: "PLAINTEXT://127.0.0.1:9092"

