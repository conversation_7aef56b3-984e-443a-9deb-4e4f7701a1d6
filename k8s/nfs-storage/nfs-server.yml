apiVersion: apps/v1
kind: Deployment
metadata:
  name: nfs-server
  namespace: kube-system
  labels:
    app: nfs-server
spec:
  replicas: 1
  selector:
    matchLabels:
      app: nfs-server
  template:
    metadata:
      labels:
        app: nfs-server
    spec:
      hostNetwork: true
      nodeSelector:
        node-role.kubernetes.io/master: "true"
      initContainers:
      - name: nfs-server-permissions
        image: registry.jxit.net.cn:5000/alpine:3.19.1
        command: ["sh", "-c", "chmod 777 /data; chown -R 1000:1000 /data"]
        securityContext:
          privileged: true
        volumeMounts:
        - name: nfs-server
          mountPath: /data
      containers:
      - name: nfs-server
        image: registry.jxit.net.cn:5000/itsthenetwork/nfs-server-alpine:12
        env:
        - name: SHARED_DIRECTORY
          value: "/data"
        ports:
        - containerPort: 2049
          name: nfs
          hostPort: 2049
          protocol: TCP
        livenessProbe:
          tcpSocket:
            port: 2049
          initialDelaySeconds: 30
          periodSeconds: 20
        securityContext:
          privileged: true
        volumeMounts:
        - name: nfs-server
          mountPath: /data
      volumes:
      - name: nfs-server
        hostPath:
          path: /data/nfs-server
          type: DirectoryOrCreate
