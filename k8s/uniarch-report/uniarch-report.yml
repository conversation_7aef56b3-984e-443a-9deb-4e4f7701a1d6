apiVersion: apps/v1
kind: Deployment
metadata:
  name: uniarch-report
  namespace: uniserver
  labels:
    app: uniarch-report
spec:
  replicas: 1
  selector:
    matchLabels:
      app: uniarch-report
  template:
    metadata:
      labels:
        app: uniarch-report
    spec:
      nodeSelector:
        node-role.kubernetes.io/master: "true"
      hostNetwork: true
      containers:
      - name: uniarch-report
        image: registry.jxit.net.cn:5000/uniserver/uniarch-report:git-8
        command:
        - "sh"
        - "-xc"
        - |
          sleep 888888888888888
        volumeMounts:
        - name: uniarch-report
          mountPath: /uniarch-report
      volumes:
      - name: uniarch-report
        persistentVolumeClaim:
          claimName: uniarch-report-pvc
