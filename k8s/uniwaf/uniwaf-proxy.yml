apiVersion: v1
kind: ConfigMap
metadata:
  name: uniwaf-proxy-config
  namespace: uniserver
data:
  nginx.conf: |
    user  root;
    worker_processes  auto;
    
    events {
        worker_connections  65536;
    }
    
    http {
        include       mime.types;
        default_type  application/octet-stream;
        
        modsecurity on;
        modsecurity_rules_file /usr/local/openresty/nginx/conf/modsecurity/modsecurity.conf;
    
        sendfile        on;
        keepalive_timeout  65;
    
        server {
            listen       _LISTEN_PORT;
            server_name  _SERVER_NAME;
            charset utf-8;
            location / {
                proxy_pass http://_BACKEND;
            }
        }
    }
---
apiVersion: apps/v1
kind: DaemonSet
metadata:
  name: uniwaf-proxy
  namespace: uniserver
spec:
  selector:
    matchLabels:
      app: uniwaf-proxy
  template:
    metadata:
      labels:
        app: uniwaf-proxy
    spec:
      hostNetwork: true
      dnsPolicy: ClusterFirstWithHostNet
      initContainers:
      - name: init-uniwaf-config
        image: registry.jxit.net.cn:5000/uniserver/uniwaf-proxy:git-11
        command:
          - sh
          - -xc
          - |
            if [ ! -e "/uniwaf-config/modsecurity.conf" ]; then
              cp -r /usr/local/openresty/nginx/conf/modsecurity/* /uniwaf-config
            fi
        volumeMounts:
        - name: uniwaf-config
          mountPath: /uniwaf-config
      containers:
      - name: uniwaf-proxy
        image: registry.jxit.net.cn:5000/uniserver/uniwaf-proxy:git-11
        command:
        - "sh"
        - "-xc"
        - |
          src=/uniwaf-proxy-config/nginx.conf
          conf=/usr/local/openresty/nginx/conf/nginx.conf
          cat $src > $conf
          sed -i "s/_LISTEN_PORT/$LISTEN_PORT/g" $conf
          sed -i "s/_SERVER_NAME/$SERVER_NAME/g" $conf
          sed -i "s/_BACKEND/$BACKEND/g" $conf
          /usr/local/openresty/nginx/sbin/nginx
          python3 /app/sendmsg.py
          sleep 8888888888888
        securityContext:
          privileged: true
        env:
        - name: TZ
          value: "Asia/Shanghai"
        - name: LISTEN_PORT
          value: "80"
        - name: SERVER_NAME
          value: "_"
        - name: BACKEND
          value: "127.0.0.1:9999"
        volumeMounts:
        - name: uniwaf-proxy-config
          mountPath: /uniwaf-proxy-config
        - name: uniwaf-config
          mountPath: /usr/local/openresty/nginx/conf/modsecurity
      volumes:
      - name: uniwaf-proxy-config
        configMap:
          name: uniwaf-proxy-config
      - name: uniwaf-config
        persistentVolumeClaim:
          claimName: uniwaf-pvc
