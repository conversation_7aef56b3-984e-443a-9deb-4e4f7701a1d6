apiVersion: apps/v1
kind: DaemonSet
metadata:
  name: unistix
  namespace: uniserver
spec:
  selector:
    matchLabels:
      app: unistix
  template:
    metadata:
      labels:
        app: unistix
    spec:
      hostNetwork: true
      dnsPolicy: ClusterFirstWithHostNet
      containers:
      - name: unistix
        image: registry.jxit.net.cn:5000/uniserver/unistix:git-26
        command:
        - "sh"
        - "-xc"
        - |
          url_stix="http://127.0.0.1:9000/up-install/stix.tar.gz"
          url_mine="http://127.0.0.1:9000/up-install/mine.tar.gz"
          while true; do
            unistix -l 15156 -m stix-file/result -s 127.0.0.1:12201 -p 127.0.0.1 -r 2>&1 > /dev/null &
            sleep 86400
            curl -s "$url_stix" -o stix.tar.gz && gunzip stix.tar.gz
            curl -s "$url_mine" -o mine.tar.gz && gunzip mine.tar.gz
            fstix=$(tar -xvf stix.tar)
            fmine=$(tar -xvf mine.tar)

            mv stix-file stix-file-bak
            tmp=$(mkfile -m "$fmine" -p "$fstix" | grep Success)
            if [ -n "$tmp" ]; then
              rm -rf stix-file-bak
            else
              mv stix-file-bak stix-file
            fi

            killall -9 unistix
            rm -rf *.tar *.json
          done
        securityContext:
          privileged: true
        ports:
        - containerPort: 15156
          hostPort: 15156
          name: gelf
        env:
        - name: TZ
          value: "Asia/Shanghai"
