apiVersion: apps/v1
kind: DaemonSet
metadata:
  name: uniwaf
  namespace: uniserver
spec:
  selector:
    matchLabels:
      app: uniwaf
  template:
    metadata:
      labels:
        app: uniwaf
    spec:
      hostNetwork: true
      dnsPolicy: ClusterFirstWithHostNet
      initContainers:
      - name: init-uniwaf-config
        image: registry.jxit.net.cn:5000/uniserver/uniwaf:git-30
        command:
          - sh
          - -xc
          - |
            if [ ! -e "/uniwaf-config/modsecurity.conf" ]; then
              mkdir -p /uniwaf-config
              cp -r /usr/local/openresty/nginx/conf/modsecurity/* /uniwaf-config
            fi
        volumeMounts:
        - name: uniwaf-config
          mountPath: /uniwaf-config
      containers:
      - name: uniwaf
        image: registry.jxit.net.cn:5000/uniserver/uniwaf:git-30
        command:
        - "sh"
        - "-xc"
        - |
          msconf=/usr/local/openresty/nginx/conf/modsecurity/modsecurity.conf
          /usr/local/bin/uniwaf -a -l 15155 -c $msconf -s 127.0.0.1:12201 -p 127.0.0.1
          sleep 88888888888888
        securityContext:
          privileged: true
        ports:
        - containerPort: 15155
          hostPort: 15155
          name: gelf
        env:
        - name: TZ
          value: "Asia/Shanghai"
        volumeMounts:
        - name: uniwaf-config
          mountPath: /usr/local/openresty/nginx/conf/modsecurity
      volumes:
      - name: uniwaf-config
        persistentVolumeClaim:
          claimName: uniwaf-pvc
