apiVersion: apps/v1
kind: Deployment
metadata:
  name: mongo
  namespace: uniserver
  labels:
    app: mongo
spec:
  replicas: 1
  selector:
    matchLabels:
      app: mongo
  template:
    metadata:
      labels:
        app: mongo
    spec:
      nodeSelector:
        node-role.kubernetes.io/master: "true"
      containers:
      - name: mongo
        image: registry.jxit.net.cn:5000/uniserver/mongodb:4.2
        resources:
          requests:
            memory: "500Mi"
          limits:
            memory: "1Gi"
        ports:
        - containerPort: 27017
          name: tcp
          protocol: TCP
        volumeMounts:
        - name: mongo-data
          mountPath: /data/db
      volumes:
      - name: mongo-data
        persistentVolumeClaim:
          claimName: mongo-pvc
---
apiVersion: v1
kind: Service
metadata:
  name: mongo
  namespace: uniserver
  labels:
    app: mongo
spec:
  selector:
    app: mongo
  ports:
  - name: db
    port: 27017
    protocol: TCP
