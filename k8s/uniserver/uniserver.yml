apiVersion: v1
kind: ServiceAccount
metadata:
  name: uniserver
  namespace: uniserver
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: uniserver-role
rules:
- apiGroups: [""]
  resources: ["pods"]
  verbs: ["get", "list", "watch"]
- apiGroups: [""]
  resources: ["pods/log"]
  verbs: ["get"]
- apiGroups: [""]
  resources: ["nodes"]
  verbs: ["get", "list", "watch"]
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: uniserver-binding
subjects:
- kind: ServiceAccount
  name: uniserver
  namespace: uniserver
roleRef:
  kind: ClusterRole
  name: uniserver-role
  apiGroup: rbac.authorization.k8s.io
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: uniserver-logo
  namespace: uniserver
data:
  config.json: |
    {
      "loginName": "Uniserver",
      "loginLogo": {
    	  "width": "40",
          "path": "/up-install/logo.png"
      },
      "navLogo": {
    	  "width": "125",
    	  "dark": "/up-install/logo-nav.png",
    	  "light": "/up-install/logo-nav.png"
      },
      "tabName": "Uniserver",
      "tabLogo": "/up-install/logo.png",
      "nickName": "Uniserver",
      "footerVersion": "Uniserver",
    }
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: uniserver-healthy
  namespace: uniserver
data:
  healthy.sh: |
    if [ ! -e "/tmp/login_ok" ]; then
      res=$(curl 'http://127.0.0.1:9000/api/system/sessions' \
        -H 'Accept: application/json' \
        -H 'Accept-Language: zh-CN,zh;q=0.9' \
        -H 'Connection: keep-alive' \
        -H 'Content-Type: application/json' \
        -H 'Origin: http://127.0.0.1:9000' \
        -H 'Referer: http://127.0.0.1:9000/' \
        -H 'User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \
        -H 'X-Requested-By: XMLHttpRequest' \
        -H 'X-Requested-With: XMLHttpRequest' \
        --data-raw '{"username":"admin","password":"asdfaf","host":"127.0.0.1:9000"}')
      if [ -z "$res" ]; then
        touch /tmp/login_ok
        exit 0
      else
        exit 1
      fi
    fi
    exit 0
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: uniserver-config
  namespace: uniserver
data:
  graylog.conf: |
    node_id_file = /usr/share/graylog/data/config/node-id
    bin_dir = /usr/share/graylog/bin
    data_dir = /usr/share/graylog/data
    plugin_dir = /usr/share/graylog/plugin
    http_bind_address = 0.0.0.0:9000
    stale_master_timeout = 30000
    rotation_strategy = count
    elasticsearch_hosts = http://elasticsearch:9200
    elasticsearch_max_docs_per_index = 20000000
    elasticsearch_max_number_of_indices = 5
    retention_strategy = delete
    elasticsearch_shards = 1
    elasticsearch_replicas = 0
    elasticsearch_index_prefix = graylog
    allow_leading_wildcard_searches = false
    allow_highlighting = false
    elasticsearch_analyzer = standard
    output_batch_size = 10000
    output_flush_interval = 1
    output_fault_count_threshold = 5
    output_fault_penalty_seconds = 30
    udp_recvbuffer_sizes = 1073741824
    processbuffer_processors = 5
    outputbuffer_processors = 10
    processor_wait_strategy = blocking
    ring_size = 131072
    inputbuffer_ring_size = 131072
    inputbuffer_processors = 10
    inputbuffer_wait_strategy = blocking
    message_journal_enabled = true
    message_journal_dir = data/journal
    message_journal_max_age = 6h
    message_journal_max_size = 5gb
    message_journal_flush_age = 1m
    message_journal_flush_interval = 100000000
    message_journal_segment_age = 1h
    message_journal_segment_size = 1gb
    lb_recognition_period_seconds = 3
    mongodb_max_connections = 1000
    mongodb_threads_allowed_to_block_multiplier = 5
    proxied_requests_thread_pool_size = 32
    stream_processing_timeout = 2000
    stream_processing_max_faults = 30
---
apiVersion: apps/v1
kind: DaemonSet
metadata:
  name: uniserver
  namespace: uniserver
spec:
  selector:
    matchLabels:
      app: uniserver
  template:
    metadata:
      labels:
        app: uniserver
    spec:
      hostNetwork: true
      serviceAccountName: uniserver
      dnsPolicy: ClusterFirstWithHostNet
      containers:
      - name: uniserver
        image: registry.jxit.net.cn:5000/uniserver/uniserver:2025-04-11_4.1
        ports:
        - containerPort: 12201
          hostPort: 12201
          name: gelf
        - containerPort: 1514
          hostPort: 1514
          name: syslog
        env:
        - name: GRAYLOG_PASSWORD_SECRET
          value: "somepasswordpepper"
        - name: GRAYLOG_ROOT_PASSWORD_SHA2 # password: admin@123
          value: "7676aaafb027c825bd9abab78b234070e702752f625b752e55e55b48e607e358"
        - name: "GRAYLOG_MONGODB_URI"
          value: "mongodb://mongo:27017/graylog"
        - name: GRAYLOG_ROOT_TIMEZONE
          value: "Asia/Shanghai"
        - name: GRAYLOG_HTTP_ENABLE_CORS
          value: "true"
        - name: GRAYLOG_SERVER_JAVA_OPTS
          value: "-Xms1g -Xmx1g"
        livenessProbe:
          exec:
            command: ["sh", "/usr/share/graylog/healthy/healthy.sh"]
          initialDelaySeconds: 30  # 容器启动后30秒开始检查
          failureThreshold: 3      # 检查失败3次算失败
          periodSeconds: 20        # 每隔60秒检查一次
          successThreshold: 1      # 检查成功一次就算成功
          timeoutSeconds: 5        # 检查超时时间
        readinessProbe:
          exec:
            command: ["sh", "/usr/share/graylog/healthy/healthy.sh"]
          initialDelaySeconds: 30  # 容器启动后30秒开始检查
          failureThreshold: 1      # 检查失败5次算失败
          periodSeconds: 10        # 每隔60秒检查一次
          successThreshold: 1      # 检查成功一次就算成功
          timeoutSeconds: 5        # 检查超时时间
        volumeMounts:
        - name: uniserver-data
          mountPath: /usr/share/graylog/data
        - name: uniserver-logo
          mountPath: /usr/share/graylog/logo
        - name: uniserver-healthy
          mountPath: /usr/share/graylog/healthy
      initContainers:
      - name: init-uniserver-conf
        image: registry.jxit.net.cn:5000/uniserver/uniconf:git-11
        command:
          - sh
          - -c
          - |
            cp /config/graylog.conf /tmp/graylog.conf
            python3 uniserver.py >> /tmp/graylog.conf
            cat /tmp/graylog.conf

            mkdir -p /usr/share/graylog/data/config
            cp /tmp/graylog.conf /usr/share/graylog/data/config/graylog.conf
            chmod 777 /usr/share/graylog/data -R
        env:
        - name: POD_IP                      #定义POD_IP的环境变量，宿主机IP
          valueFrom:
            fieldRef:
              fieldPath: status.podIP 
        volumeMounts:
        - name: config-volume
          mountPath: /config
        - name: uniserver-data
          mountPath: /usr/share/graylog/data
      volumes:
      - name: uniproxy-config
        configMap:
          name: uniproxy-config
      - name: config-volume
        configMap:
          name: uniserver-config
      - name: uniserver-logo
        configMap:
          name: uniserver-logo
      - name: uniserver-healthy
        configMap:
          name: uniserver-healthy
      - name: uniserver-data
        hostPath:
          path: /data/uniserver
          type: DirectoryOrCreate
