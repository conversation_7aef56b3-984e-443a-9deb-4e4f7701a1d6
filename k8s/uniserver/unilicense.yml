apiVersion: apps/v1
kind: Deployment
metadata:
  name: unilicense
  namespace: uniserver
  labels:
    app: unilicense
spec:
  replicas: 1
  selector:
    matchLabels:
      app: unilicense
  template:
    metadata:
      labels:
        app: unilicense
    spec:
      nodeSelector:
        node-role.kubernetes.io/master: "true"
      containers:
      - name: unilicense
        image: registry.jxit.net.cn:5000/uniserver/unilicense:svn-28459
        command: 
        - "sh"
        - "-c"
        - |
          license-server -l 12202 -i replace_license_str
          sleep 88888888888888
        ports:
        - containerPort: 12202
          hostPort: 12202
          name: license
---
apiVersion: v1
kind: Service
metadata:
  name: unilicense
  namespace: uniserver
  labels:
    app: unilicense
spec:
  selector:
    app: unilicense
  ports:
  - name: license
    port: 12202
    protocol: TCP
