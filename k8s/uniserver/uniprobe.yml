apiVersion: apps/v1
kind: DaemonSet
metadata:
  name: uniprobe
  namespace: uniserver
spec:
  selector:
    matchLabels:
      app: uniprobe
  template:
    metadata:
      labels:
        app: uniprobe
    spec:
      hostNetwork: true
      dnsPolicy: ClusterFirstWithHostNet
      containers:
      - name: uniprobe
        image: registry.jxit.net.cn:5000/uniserver/uniprobe:git-1384
        command:
        - sh
        - -c
        - |
          export proclist=5
          export exp=127.0.0.1:12201
          uniprobe -n 127.0.0.1:9000 -a xxxxxxxxxxxxxx
          sleep 88888888888888
        volumeMounts:
        - name: proc
          mountPath: /proc
        - name: sys
          mountPath: /sys
      volumes:
      - name: proc
        hostPath:
          path: /proc
      - name: sys
        hostPath:
          path: /sys
