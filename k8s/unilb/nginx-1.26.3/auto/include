
# Copyright (C) <PERSON>
# Copyright (C) Nginx, Inc.


echo $ngx_n "checking for $ngx_include ...$ngx_c"

cat << END >> $NGX_AUTOCONF_ERR

----------------------------------------
checking for $ngx_include

END


ngx_found=no

cat << END > $NGX_AUTOTEST.c

$NGX_INCLUDE_SYS_PARAM_H
#include <$ngx_include>

int main(void) {
    return 0;
}

END


ngx_test="$CC -o $NGX_AUTOTEST $NGX_AUTOTEST.c"

eval "$ngx_test >> $NGX_AUTOCONF_ERR 2>&1"

if [ -x $NGX_AUTOTEST ]; then

    ngx_found=yes

    echo " found"

    ngx_name=`echo $ngx_include \
              | tr abcdefghijklmnopqrstuvwxyz/. ABCDEFGHIJKLMNOPQRSTUVWXYZ__`


    have=NGX_HAVE_$ngx_name . auto/have_headers

    eval "NGX_INCLUDE_$ngx_name='#include <$ngx_include>'"

else
    echo " not found"

    echo "----------"    >> $NGX_AUTOCONF_ERR
    cat $NGX_AUTOTEST.c  >> $NGX_AUTOCONF_ERR
    echo "----------"    >> $NGX_AUTOCONF_ERR
    echo $ngx_test       >> $NGX_AUTOCONF_ERR
    echo "----------"    >> $NGX_AUTOCONF_ERR
fi

rm -rf $NGX_AUTOTEST*
