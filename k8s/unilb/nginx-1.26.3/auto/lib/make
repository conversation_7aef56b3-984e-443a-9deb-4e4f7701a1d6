
# Copyright (C) <PERSON>
# Copyright (C) Nginx, Inc.


if [ $PCRE != NONE -a $PCRE != NO -a $PCRE != YES ]; then
    . auto/lib/pcre/make
fi

if [ $OPENSSL != NONE -a $OPENSSL != NO -a $OPENSSL != YES ]; then
    . auto/lib/openssl/make
fi

if [ $ZLIB != NONE -a $ZLIB != NO -a $ZLIB != YES ]; then
    . auto/lib/zlib/make
fi

if [ $NGX_LIBATOMIC != NO -a $NGX_LIBATOMIC != YES ]; then
    . auto/lib/libatomic/make
fi

if [ $USE_PERL != NO ]; then
    . auto/lib/perl/make
fi
