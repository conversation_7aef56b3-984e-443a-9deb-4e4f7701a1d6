
# Copyright (C) <PERSON>
# Copyright (C) Nginx, Inc.


echo "checking for perl"


NGX_PERL_VER=`$NGX_PERL -v 2>&1 | grep '^This is perl' 2>&1 \
                                | sed -e 's/^This is perl, \(.*\)/\1/'`

if test -n "$NGX_PERL_VER"; then
    echo " + perl version: $NGX_PERL_VER"

    if [ "`$NGX_PERL -e 'use 5.008006; print "OK"'`" != "OK" ]; then
        echo
        echo "$0: error: perl 5.8.6 or higher is required"
        echo

        exit 1;
    fi

    if [ "`$NGX_PERL -MExtUtils::Embed -e 'print "OK"'`" != "OK" ]; then
        echo
        echo "$0: error: perl module ExtUtils::Embed is required"
        echo

        exit 1;
    fi

    NGX_PM_CFLAGS=`$NGX_PERL -MExtUtils::Embed -e ccopts`
    NGX_PM_LDFLAGS=`$NGX_PERL -MConfig -e 'print $Config{lddlflags}'`

    NGX_PERL_CFLAGS="$CFLAGS `$NGX_PERL -MExtUtils::Embed -e ccopts`"

    # gcc 4.1/4.2 warn about unused values in pTHX_
    NGX_PERL_CFLAGS=`echo $NGX_PERL_CFLAGS \
                     | sed -e 's/-Wunused-value/-Wno-unused-value/'`
    # icc8 warns 'declaration hides parameter "my_perl"' in ENTER and LEAVE
    NGX_PERL_CFLAGS=`echo $NGX_PERL_CFLAGS \
                     | sed -e 's/-wd171/-wd171 -wd1599/'`

    ngx_perl_ldopts=`$NGX_PERL -MExtUtils::Embed -e ldopts`

    ngx_perl_dlext=`$NGX_PERL -MConfig -e 'print $Config{dlext}'`
    ngx_perl_libdir="src/http/modules/perl/blib/arch/auto"
    ngx_perl_module="$ngx_perl_libdir/nginx/nginx.$ngx_perl_dlext"

    if $NGX_PERL -V:usemultiplicity | grep define > /dev/null; then
        have=NGX_HAVE_PERL_MULTIPLICITY . auto/have
        echo " + perl interpreter multiplicity found"
    fi

    if $NGX_PERL -V:useithreads | grep undef > /dev/null; then
        # FreeBSD port wants to link with -pthread non-threaded perl
        ngx_perl_ldopts=`echo $ngx_perl_ldopts | sed 's/ -pthread//'`
    fi

    if [ "$NGX_SYSTEM" = "Darwin" ]; then
        # OS X system perl wants to link universal binaries
        ngx_perl_ldopts=`echo $ngx_perl_ldopts \
                         | sed -e 's/-arch i386//' -e 's/-arch x86_64//'`
    fi

    if [ $USE_PERL = YES ]; then
        CORE_LINK="$CORE_LINK $ngx_perl_ldopts"
    fi

    NGX_LIB_PERL="$ngx_perl_ldopts"

    if test -n "$NGX_PERL_MODULES"; then
        have=NGX_PERL_MODULES value="(u_char *) \"$NGX_PERL_MODULES\""
        . auto/define
        NGX_PERL_MODULES_MAN=$NGX_PERL_MODULES/man3
    fi

else
    echo
    echo "$0: error: perl 5.8.6 or higher is required"
    echo

    exit 1;
fi
