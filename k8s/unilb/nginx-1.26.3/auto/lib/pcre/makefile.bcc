
# Copyright (C) <PERSON>
# Copyright (C) Nginx, Inc.


CFLAGS =	-q -O2 -tWM -w-8004 $(CPU_OPT)
PCREFLAGS =	-DHAVE_CONFIG_H -DPCRE_STATIC -DPOSIX_MALLOC_THRESHOLD=10 \
		-DSUPPORT_PCRE8 -DHAVE_MEMMOVE


pcre.lib:
	cd $(PCRE)

	bcc32 -c $(CFLAGS) -I. $(PCREFLAGS) pcre_*.c

	copy /y nul pcre.lst
	for %n in (*.obj) do @echo +%n ^^& >> pcre.lst
	echo + >> pcre.lst

	tlib pcre.lib @pcre.lst

pcre.h:
	cd $(PCRE)

	copy /y pcre.h.generic pcre.h
	copy /y config.h.generic config.h
	copy /y pcre_chartables.c.dist pcre_chartables.c
