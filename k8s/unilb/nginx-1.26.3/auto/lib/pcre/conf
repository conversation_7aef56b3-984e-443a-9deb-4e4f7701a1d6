
# Copyright (C) <PERSON>
# Copyright (C) Nginx, Inc.


if [ $PCRE != NONE ]; then

    if [ -f $PCRE/src/pcre2.h.generic ]; then

        PCRE_LIBRARY=PCRE2

        have=NGX_PCRE . auto/have
        have=NGX_PCRE2 . auto/have

        if [ "$NGX_PLATFORM" = win32 ]; then
            have=PCRE2_STATIC . auto/have
        fi

        CORE_INCS="$CORE_INCS $PCRE/src/"
        CORE_DEPS="$CORE_DEPS $PCRE/src/pcre2.h"

        case "$NGX_CC_NAME" in

            msvc)
                LINK_DEPS="$LINK_DEPS $PCRE/src/pcre2-8.lib"
                CORE_LIBS="$CORE_LIBS $PCRE/src/pcre2-8.lib"
            ;;

            *)
                LINK_DEPS="$LINK_DEPS $PCRE/.libs/libpcre2-8.a"
                CORE_LIBS="$CORE_LIBS $PCRE/.libs/libpcre2-8.a"
            ;;

        esac

    else

        PCRE_LIBRARY=PCRE

        have=NGX_PCRE . auto/have

        if [ "$NGX_PLATFORM" = win32 ]; then
            have=PCRE_STATIC . auto/have
        fi

        CORE_INCS="$CORE_INCS $PCRE"
        CORE_DEPS="$CORE_DEPS $PCRE/pcre.h"

        case "$NGX_CC_NAME" in

            msvc | owc | bcc)
                LINK_DEPS="$LINK_DEPS $PCRE/pcre.lib"
                CORE_LIBS="$CORE_LIBS $PCRE/pcre.lib"
            ;;

            *)
                LINK_DEPS="$LINK_DEPS $PCRE/.libs/libpcre.a"
                CORE_LIBS="$CORE_LIBS $PCRE/.libs/libpcre.a"
            ;;

        esac
    fi

    if [ $PCRE_JIT = YES ]; then
        have=NGX_HAVE_PCRE_JIT . auto/have
        PCRE_CONF_OPT="$PCRE_CONF_OPT --enable-jit"
    fi

else

    if [ "$NGX_PLATFORM" != win32 ]; then
        PCRE=NO
    fi

    if [ $PCRE = NO -a $PCRE2 != DISABLED ]; then

        ngx_feature="PCRE2 library"
        ngx_feature_name="NGX_PCRE2"
        ngx_feature_run=no
        ngx_feature_incs="#define PCRE2_CODE_UNIT_WIDTH 8
                          #include <pcre2.h>"
        ngx_feature_path=
        ngx_feature_libs="-lpcre2-8"
        ngx_feature_test="pcre2_code *re;
                          re = pcre2_compile(NULL, 0, 0, NULL, NULL, NULL);
                          if (re == NULL) return 1"
        . auto/feature

        if [ $ngx_found = no ]; then

            # pcre2-config

            ngx_pcre2_prefix=`pcre2-config --prefix 2>/dev/null`

            if [ -n "$ngx_pcre2_prefix" ]; then
                ngx_feature="PCRE2 library in $ngx_pcre2_prefix"
                ngx_feature_path=`pcre2-config --cflags \
                                  | sed -n -e 's/.*-I *\([^ ][^ ]*\).*/\1/p'`
                ngx_feature_libs=`pcre2-config --libs8`
                . auto/feature
            fi
        fi

        if [ $ngx_found = yes ]; then
            have=NGX_PCRE . auto/have
            CORE_INCS="$CORE_INCS $ngx_feature_path"
            CORE_LIBS="$CORE_LIBS $ngx_feature_libs"
            PCRE=YES
            PCRE_LIBRARY=PCRE2
        fi
    fi

    if [ $PCRE = NO ]; then

        ngx_feature="PCRE library"
        ngx_feature_name="NGX_PCRE"
        ngx_feature_run=no
        ngx_feature_incs="#include <pcre.h>"
        ngx_feature_path=
        ngx_feature_libs="-lpcre"
        ngx_feature_test="pcre *re;
                          re = pcre_compile(NULL, 0, NULL, 0, NULL);
                          if (re == NULL) return 1"
        . auto/feature

        if [ $ngx_found = no ]; then

            # FreeBSD port

            ngx_feature="PCRE library in /usr/local/"
            ngx_feature_path="/usr/local/include"

            if [ $NGX_RPATH = YES ]; then
                ngx_feature_libs="-R/usr/local/lib -L/usr/local/lib -lpcre"
            else
                ngx_feature_libs="-L/usr/local/lib -lpcre"
            fi

            . auto/feature
        fi

        if [ $ngx_found = no ]; then

            # RedHat RPM, Solaris package

            ngx_feature="PCRE library in /usr/include/pcre/"
            ngx_feature_path="/usr/include/pcre"
            ngx_feature_libs="-lpcre"

            . auto/feature
        fi

        if [ $ngx_found = no ]; then

            # NetBSD port

            ngx_feature="PCRE library in /usr/pkg/"
            ngx_feature_path="/usr/pkg/include"

            if [ $NGX_RPATH = YES ]; then
                ngx_feature_libs="-R/usr/pkg/lib -L/usr/pkg/lib -lpcre"
            else
                ngx_feature_libs="-L/usr/pkg/lib -lpcre"
            fi

            . auto/feature
        fi

        if [ $ngx_found = no ]; then

            # MacPorts

            ngx_feature="PCRE library in /opt/local/"
            ngx_feature_path="/opt/local/include"

            if [ $NGX_RPATH = YES ]; then
                ngx_feature_libs="-R/opt/local/lib -L/opt/local/lib -lpcre"
            else
                ngx_feature_libs="-L/opt/local/lib -lpcre"
            fi

            . auto/feature
        fi

        if [ $ngx_found = no ]; then

            # Homebrew on Apple Silicon

            ngx_feature="PCRE library in /opt/homebrew/"
            ngx_feature_path="/opt/homebrew/include"

            if [ $NGX_RPATH = YES ]; then
                ngx_feature_libs="-R/opt/homebrew/lib -L/opt/homebrew/lib -lpcre"
            else
                ngx_feature_libs="-L/opt/homebrew/lib -lpcre"
            fi

            . auto/feature
        fi

        if [ $ngx_found = yes ]; then
            CORE_INCS="$CORE_INCS $ngx_feature_path"
            CORE_LIBS="$CORE_LIBS $ngx_feature_libs"
            PCRE=YES
            PCRE_LIBRARY=PCRE
        fi

        if [ $PCRE = YES ]; then
            ngx_feature="PCRE JIT support"
            ngx_feature_name="NGX_HAVE_PCRE_JIT"
            ngx_feature_test="int jit = 0;
                              pcre_free_study(NULL);
                              pcre_config(PCRE_CONFIG_JIT, &jit);
                              if (jit != 1) return 1;"
            . auto/feature

            if [ $ngx_found = yes ]; then
                PCRE_JIT=YES
            fi
        fi
    fi

    if [ $PCRE != YES ]; then
cat << END

$0: error: the HTTP rewrite module requires the PCRE library.
You can either disable the module by using --without-http_rewrite_module
option, or install the PCRE library into the system, or build the PCRE library
statically from the source with nginx by using --with-pcre=<path> option.

END
        exit 1
    fi

fi
