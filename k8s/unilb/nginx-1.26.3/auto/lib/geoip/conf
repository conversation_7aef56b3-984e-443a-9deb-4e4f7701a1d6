
# Copyright (C) <PERSON>
# Copyright (C) Nginx, Inc.


    ngx_feature="GeoIP library"
    ngx_feature_name=
    ngx_feature_run=no
    ngx_feature_incs="#include <GeoIP.h>"
    ngx_feature_path=
    ngx_feature_libs="-lGeoIP"
    ngx_feature_test="GeoIP_open(NULL, 0)"
    . auto/feature


if [ $ngx_found = no ]; then

    # FreeBSD port

    ngx_feature="GeoIP library in /usr/local/"
    ngx_feature_path="/usr/local/include"

    if [ $NGX_RPATH = YES ]; then
        ngx_feature_libs="-R/usr/local/lib -L/usr/local/lib -lGeoIP"
    else
        ngx_feature_libs="-L/usr/local/lib -lGeoIP"
    fi

    . auto/feature
fi


if [ $ngx_found = no ]; then

    # NetBSD port

    ngx_feature="GeoIP library in /usr/pkg/"
    ngx_feature_path="/usr/pkg/include"

    if [ $NGX_RPATH = YES ]; then
        ngx_feature_libs="-R/usr/pkg/lib -L/usr/pkg/lib -lGeoIP"
    else
        ngx_feature_libs="-L/usr/pkg/lib -lGeoIP"
    fi

    . auto/feature
fi


if [ $ngx_found = no ]; then

    # MacPorts

    ngx_feature="GeoIP library in /opt/local/"
    ngx_feature_path="/opt/local/include"

    if [ $NGX_RPATH = YES ]; then
        ngx_feature_libs="-R/opt/local/lib -L/opt/local/lib -lGeoIP"
    else
        ngx_feature_libs="-L/opt/local/lib -lGeoIP"
    fi

    . auto/feature
fi


if [ $ngx_found = no ]; then

    # Homebrew on Apple Silicon

    ngx_feature="GeoIP library in /opt/homebrew/"
    ngx_feature_path="/opt/homebrew/include"

    if [ $NGX_RPATH = YES ]; then
        ngx_feature_libs="-R/opt/homebrew/lib -L/opt/homebrew/lib -lGeoIP"
    else
        ngx_feature_libs="-L/opt/homebrew/lib -lGeoIP"
    fi

    . auto/feature
fi


if [ $ngx_found = yes ]; then

    CORE_INCS="$CORE_INCS $ngx_feature_path"

    if [ $USE_GEOIP = YES ]; then
        CORE_LIBS="$CORE_LIBS $ngx_feature_libs"
    fi

    NGX_LIB_GEOIP=$ngx_feature_libs

    ngx_feature="GeoIP IPv6 support"
    ngx_feature_name="NGX_HAVE_GEOIP_V6"
    ngx_feature_run=no
    ngx_feature_incs="#include <stdio.h>
                      #include <GeoIP.h>"
    #ngx_feature_path=
    #ngx_feature_libs=
    ngx_feature_test="printf(\"%d\", GEOIP_CITY_EDITION_REV0_V6);"
    . auto/feature

else

cat << END

$0: error: the GeoIP module requires the GeoIP library.
You can either do not enable the module or install the library.

END

    exit 1
fi
