
# Copyright (C) <PERSON>
# Copyright (C) Nginx, Inc.


case "$NGX_CC_NAME" in

    msvc)
        ngx_makefile=makefile.msvc
        ngx_opt="CPU_OPT=\"$CPU_OPT\" LIBC=$LIBC"
        ngx_zlib="ZLIB=\"$ZLIB\""

    ;;

    owc)
        ngx_makefile=makefile.owc
        ngx_opt="CPU_OPT=\"$CPU_OPT\""
        ngx_zlib=`echo ZLIB=\"$ZLIB\" | sed -e "s/\//$ngx_regex_dirsep/g"`
    ;;

    bcc)
        ngx_makefile=makefile.bcc
        ngx_opt="-DCPU_OPT=\"$CPU_OPT\""
        ngx_zlib=`echo \-DZLIB=\"$ZLIB\" | sed -e "s/\//$ngx_regex_dirsep/g"`
    ;;

    *)
        ngx_makefile=
    ;;

esac


done=NO


case "$NGX_PLATFORM" in

    win32)

        if [ -n "$ngx_makefile" ]; then
            cat << END                                        >> $NGX_MAKEFILE

`echo "$ZLIB/zlib.lib:	$NGX_MAKEFILE" | sed -e "s/\//$ngx_regex_dirsep/g"`
	\$(MAKE) -f auto/lib/zlib/$ngx_makefile $ngx_opt $ngx_zlib

END

        else

            cat << END                                        >> $NGX_MAKEFILE

$ZLIB/libz.a:	$NGX_MAKEFILE
	cd $ZLIB \\
	&& \$(MAKE) distclean \\
	&& \$(MAKE) -f win32/Makefile.gcc \\
		CFLAGS="$ZLIB_OPT" CC="\$(CC)" \\
		libz.a

END

        fi

        done=YES
    ;;

    # FreeBSD: i386
    # Linux: i686

    *:i386 | *:i686)
        case $ZLIB_ASM in
            pentium)

                cat << END                                    >> $NGX_MAKEFILE

$ZLIB/libz.a:	$NGX_MAKEFILE
	cd $ZLIB \\
	&& \$(MAKE) distclean \\
	&& cp contrib/asm586/match.S . \\
	&& CFLAGS="$ZLIB_OPT -DASMV" CC="\$(CC)" \\
		./configure \\
	&& \$(MAKE) OBJA=match.o libz.a

END

                done=YES
            ;;

            pentiumpro)

                cat << END                                    >> $NGX_MAKEFILE

$ZLIB/libz.a:	$NGX_MAKEFILE
	cd $ZLIB \\
	&& \$(MAKE) distclean \\
	&& cp contrib/asm686/match.S . \\
	&& CFLAGS="$ZLIB_OPT -DASMV" CC="\$(CC)" \\
		./configure \\
	&& \$(MAKE) OBJA=match.o libz.a

END

                done=YES
            ;;

            NO)
            ;;

            *)
                echo "$0: error: invalid --with-zlib-asm=$ZLIB_ASM option."
                echo "The valid values are \"pentium\" and \"pentiumpro\" only".
                echo

                exit 1;
            ;;
        esac
    ;;

esac


if [ $done = NO ]; then

    cat << END                                                >> $NGX_MAKEFILE

$ZLIB/libz.a:	$NGX_MAKEFILE
	cd $ZLIB \\
	&& \$(MAKE) distclean \\
	&& CFLAGS="$ZLIB_OPT" CC="\$(CC)" \\
		./configure \\
	&& \$(MAKE) libz.a

END

fi
