
# Copyright (C) <PERSON>
# Copyright (C) Nginx, Inc.


if [ $NGX_LIBATOMIC != YES ]; then

    have=NGX_HAVE_LIBATOMIC . auto/have
    CORE_INCS="$CORE_INCS $NGX_LIBATOMIC/src"
    LINK_DEPS="$LINK_DEPS $NGX_LIBATOMIC/build/lib/libatomic_ops.a"
    CORE_LIBS="$CORE_LIBS $NGX_LIBATOMIC/build/lib/libatomic_ops.a"

else

    ngx_feature="atomic_ops library"
    ngx_feature_name=NGX_HAVE_LIBATOMIC
    ngx_feature_run=yes
    ngx_feature_incs="#define AO_REQUIRE_CAS
                      #include <atomic_ops.h>"
    ngx_feature_path=
    ngx_feature_libs="-latomic_ops"
    ngx_feature_test="AO_t  n = 0;
                      if (!AO_compare_and_swap(&n, 0, 1))
                          return 1;
                      if (AO_fetch_and_add(&n, 1) != 1)
                          return 1;
                      if (n != 2)
                          return 1;
                      AO_nop();"
    . auto/feature

    if [ $ngx_found = yes ]; then
        CORE_LIBS="$CORE_LIBS $ngx_feature_libs"
    else

cat << END

$0: error: libatomic_ops library was not found.

END
        exit 1
    fi
fi
