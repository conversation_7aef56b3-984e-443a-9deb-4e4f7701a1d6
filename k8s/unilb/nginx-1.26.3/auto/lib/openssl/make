
# Copyright (C) <PERSON>
# Copyright (C) Nginx, Inc.


case "$CC" in

    cl)

        case "$NGX_MACHINE" in

            amd64)
                OPENSSL_TARGET=VC-WIN64A
            ;;

            *)
                OPENSSL_TARGET=VC-WIN32
            ;;

        esac

        cat << END                                            >> $NGX_MAKEFILE

$OPENSSL/openssl/include/openssl/ssl.h:	$NGX_MAKEFILE
	\$(MAKE) -f auto/lib/openssl/makefile.msvc			\
		OPENSSL="$OPENSSL" OPENSSL_OPT="$OPENSSL_OPT"		\
		OPENSSL_TARGET="$OPENSSL_TARGET"

END

    ;;

    bcc32)

        ngx_opt=`echo "-DOPENSSL=\"$OPENSSL\" -DOPENSSL_OPT=\"$OPENSSL_OPT\"" \
            | sed -e "s/\//$ngx_regex_dirsep/g"`

        cat << END                                            >> $NGX_MAKEFILE

`echo "$OPENSSL\\openssl\\lib\\libeay32.lib:				\
	$OPENSSL\\openssl\\include\\openssl\\ssl.h"			\
	| sed -e "s/\//$ngx_regex_dirsep/g"`

`echo "$OPENSSL\\openssl\\lib\\ssleay32.lib:				\
	$OPENSSL\\openssl\\include\\openssl\\ssl.h"			\
	| sed -e "s/\//$ngx_regex_dirsep/g"`

`echo "$OPENSSL\\openssl\\include\\openssl\\ssl.h:	$NGX_MAKEFILE"	\
	| sed -e "s/\//$ngx_regex_dirsep/g"`
	\$(MAKE) -f auto/lib/openssl/makefile.bcc $ngx_opt

END

    ;;

    *)
        case $OPENSSL in
            /*) ngx_prefix="$OPENSSL/.openssl" ;;
            *)  ngx_prefix="$PWD/$OPENSSL/.openssl" ;;
        esac

        cat << END                                            >> $NGX_MAKEFILE

$OPENSSL/.openssl/include/openssl/ssl.h:	$NGX_MAKEFILE
	cd $OPENSSL \\
	&& if [ -f Makefile ]; then \$(MAKE) clean; fi \\
	&& ./config --prefix=$ngx_prefix no-shared no-threads $OPENSSL_OPT \\
	&& \$(MAKE) \\
	&& \$(MAKE) install_sw LIBDIR=lib

END

    ;;

esac
