
# Copyright (C) <PERSON>
# Copyright (C) Nginx, Inc.


    ngx_feature="Google perftools"
    ngx_feature_name=
    ngx_feature_run=no
    ngx_feature_incs=
    ngx_feature_path=
    ngx_feature_libs="-lprofiler"
    ngx_feature_test="void ProfilerStop(void);
                      ProfilerStop()"
    . auto/feature


if [ $ngx_found = no ]; then

    # FreeBSD port

    ngx_feature="Google perftools in /usr/local/"

    if [ $NGX_RPATH = YES ]; then
        ngx_feature_libs="-R/usr/local/lib -L/usr/local/lib -lprofiler"
    else
        ngx_feature_libs="-L/usr/local/lib -lprofiler"
    fi

    . auto/feature
fi


if [ $ngx_found = no ]; then

    # MacPorts

    ngx_feature="Google perftools in /opt/local/"

    if [ $NGX_RPATH = YES ]; then
        ngx_feature_libs="-R/opt/local/lib -L/opt/local/lib -lprofiler"
    else
        ngx_feature_libs="-L/opt/local/lib -lprofiler"
    fi

    . auto/feature
fi


if [ $ngx_found = no ]; then

    # Homebrew on Apple Silicon

    ngx_feature="Google perftools in /opt/homebrew/"

    if [ $NGX_RPATH = YES ]; then
        ngx_feature_libs="-R/opt/homebrew/lib -L/opt/homebrew/lib -lprofiler"
    else
        ngx_feature_libs="-L/opt/homebrew/lib -lprofiler"
    fi

    . auto/feature
fi


if [ $ngx_found = yes ]; then
    CORE_LIBS="$CORE_LIBS $ngx_feature_libs"

else

cat << END

$0: error: the Google perftools module requires the Google perftools
library. You can either do not enable the module or install the library.

END

    exit 1
fi
