
# Copyright (C) <PERSON>
# Copyright (C) Nginx, Inc.


have=NGX_WIN32 . auto/have_headers

CORE_INCS="$WIN32_INCS"
CORE_DEPS="$WIN32_DEPS"
CORE_SRCS="$WIN32_SRCS $IOCP_SRCS"
OS_CONFIG="$WIN32_CONFIG"
NGX_ICONS="$NGX_WIN32_ICONS"
SELECT_SRCS=$WIN32_SELECT_SRCS
POLL_SRCS=$WIN32_POLL_SRCS

ngx_pic_opt=
ngx_binext=".exe"

case "$NGX_CC_NAME" in

    clang | gcc)
        CORE_LIBS="$CORE_LIBS -ladvapi32 -lws2_32"
        MAIN_LINK="$MAIN_LINK -Wl,--export-all-symbols"
        MAIN_LINK="$MAIN_LINK -Wl,--out-implib=$NGX_OBJS/libnginx.a"
        MODULE_LINK="-shared -L $NGX_OBJS -lnginx"
    ;;

    *)
        CORE_LIBS="$CORE_LIBS advapi32.lib ws2_32.lib"
    ;;

esac

EVENT_MODULES="$EVENT_MODULES $IOCP_MODULE"
#EVENT_FOUND=YES

have=NGX_HAVE_INET6 . auto/have

have=NGX_HAVE_IOCP . auto/have
