
# Copyright (C) <PERSON>
# Copyright (C) Nginx, Inc.


# Open Watcom C 1.0, 1.2, 1.3

# optimizations

# maximize speed
CFLAGS="$CFLAGS -ot"
# reorder instructions for best pipeline usage
CFLAGS="$CFLAGS -op"
# inline intrinsic functions
CFLAGS="$CFLAGS -oi"
# inline expansion
CFLAGS="$CFLAGS -oe"
# disable stack checking calls
CFLAGS="$CFLAGS -s"

case $CPU in
    pentium)
        # optimize for Pentium and Athlon
        # register-based arguments passing conventions
        CPU_OPT="-5r"
        # stack-based arguments passing conventions
        #CPU_OPT="-5s"
    ;;

    pentiumpro)
        # optimize for Pentium Pro, Pentium II and Pentium III
        # register-based arguments passing conventions
        CPU_OPT="-6r"
        # stack-based arguments passing conventions
        #CPU_OPT="-6s"
    ;;
esac

CFLAGS="$CFLAGS $CPU_OPT"


# warnings

# maximum level
CFLAGS="$CFLAGS -wx"
#CFLAGS="$CFLAGS -w3"

# stop on warning
CFLAGS="$CFLAGS -we"

# built target is NT
CFLAGS="$CFLAGS -bt=nt"

# multithreaded
CFLAGS="$CFLAGS -bm"

# debug
CFLAGS="$CFLAGS -d2"

# quiet
CFLAGS="$CFLAGS -zq"

# Open Watcom C 1.2
have=NGX_HAVE_C99_VARIADIC_MACROS . auto/have


# the precompiled headers
#CORE_DEPS="$CORE_DEPS $NGX_OBJS/ngx_config.pch"
#NGX_PCH="$NGX_OBJS/ngx_config.pch"
#NGX_BUILD_PCH="-fhq=$NGX_OBJS/ngx_config.pch"
#NGX_USE_PCH="-fh=$NGX_OBJS/ngx_config.pch"


# the link flags, built target is NT GUI mode application
#CORE_LINK="$CORE_LINK -l=nt_win"


# the resource file
NGX_RCC="wrc \$(CORE_INCS) -fo=$NGX_OBJS/nginx.res "
NGX_RCC="$NGX_RCC $NGX_WIN32_RC $NGX_OBJS/nginx.exe"


ngx_include_opt="-i="
ngx_objout="-fo"
ngx_binout="-fe="
ngx_objext="obj"

ngx_regex_dirsep='\\'
ngx_dirsep="\\"

ngx_long_start=' '
ngx_long_end=' '
ngx_long_regex_cont=' \&\
	'
ngx_long_cont=' &
	'

ngx_regex_cont=' \&\
	'
ngx_cont=' &
	'
ngx_tab=' &
		'
