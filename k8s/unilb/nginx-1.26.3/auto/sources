
# Copyright (C) <PERSON>
# Copyright (C) Nginx, Inc.


CORE_MODULES="ngx_core_module ngx_errlog_module ngx_conf_module"

CORE_INCS="src/core"

CORE_DEPS="src/core/nginx.h \
           src/core/ngx_config.h \
           src/core/ngx_core.h \
           src/core/ngx_log.h \
           src/core/ngx_palloc.h \
           src/core/ngx_array.h \
           src/core/ngx_list.h \
           src/core/ngx_hash.h \
           src/core/ngx_buf.h \
           src/core/ngx_queue.h \
           src/core/ngx_string.h \
           src/core/ngx_parse.h \
           src/core/ngx_parse_time.h \
           src/core/ngx_inet.h \
           src/core/ngx_file.h \
           src/core/ngx_crc.h \
           src/core/ngx_crc32.h \
           src/core/ngx_murmurhash.h \
           src/core/ngx_md5.h \
           src/core/ngx_sha1.h \
           src/core/ngx_rbtree.h \
           src/core/ngx_radix_tree.h \
           src/core/ngx_rwlock.h \
           src/core/ngx_slab.h \
           src/core/ngx_times.h \
           src/core/ngx_shmtx.h \
           src/core/ngx_connection.h \
           src/core/ngx_cycle.h \
           src/core/ngx_conf_file.h \
           src/core/ngx_module.h \
           src/core/ngx_resolver.h \
           src/core/ngx_open_file_cache.h \
           src/core/ngx_crypt.h \
           src/core/ngx_proxy_protocol.h \
           src/core/ngx_syslog.h"


CORE_SRCS="src/core/nginx.c \
           src/core/ngx_log.c \
           src/core/ngx_palloc.c \
           src/core/ngx_array.c \
           src/core/ngx_list.c \
           src/core/ngx_hash.c \
           src/core/ngx_buf.c \
           src/core/ngx_queue.c \
           src/core/ngx_output_chain.c \
           src/core/ngx_string.c \
           src/core/ngx_parse.c \
           src/core/ngx_parse_time.c \
           src/core/ngx_inet.c \
           src/core/ngx_file.c \
           src/core/ngx_crc32.c \
           src/core/ngx_murmurhash.c \
           src/core/ngx_md5.c \
           src/core/ngx_sha1.c \
           src/core/ngx_rbtree.c \
           src/core/ngx_radix_tree.c \
           src/core/ngx_slab.c \
           src/core/ngx_times.c \
           src/core/ngx_shmtx.c \
           src/core/ngx_connection.c \
           src/core/ngx_cycle.c \
           src/core/ngx_spinlock.c \
           src/core/ngx_rwlock.c \
           src/core/ngx_cpuinfo.c \
           src/core/ngx_conf_file.c \
           src/core/ngx_module.c \
           src/core/ngx_resolver.c \
           src/core/ngx_open_file_cache.c \
           src/core/ngx_crypt.c \
           src/core/ngx_proxy_protocol.c \
           src/core/ngx_syslog.c"


EVENT_MODULES="ngx_events_module ngx_event_core_module"

EVENT_INCS="src/event src/event/modules src/event/quic"

EVENT_DEPS="src/event/ngx_event.h \
            src/event/ngx_event_timer.h \
            src/event/ngx_event_posted.h \
            src/event/ngx_event_connect.h \
            src/event/ngx_event_pipe.h \
            src/event/ngx_event_udp.h"

EVENT_SRCS="src/event/ngx_event.c \
            src/event/ngx_event_timer.c \
            src/event/ngx_event_posted.c \
            src/event/ngx_event_accept.c \
            src/event/ngx_event_udp.c \
            src/event/ngx_event_connect.c \
            src/event/ngx_event_pipe.c"


SELECT_MODULE=ngx_select_module
SELECT_SRCS=src/event/modules/ngx_select_module.c
WIN32_SELECT_SRCS=src/event/modules/ngx_win32_select_module.c

POLL_MODULE=ngx_poll_module
POLL_SRCS=src/event/modules/ngx_poll_module.c
WIN32_POLL_SRCS=src/event/modules/ngx_win32_poll_module.c

KQUEUE_MODULE=ngx_kqueue_module
KQUEUE_SRCS=src/event/modules/ngx_kqueue_module.c

DEVPOLL_MODULE=ngx_devpoll_module
DEVPOLL_SRCS=src/event/modules/ngx_devpoll_module.c

EVENTPORT_MODULE=ngx_eventport_module
EVENTPORT_SRCS=src/event/modules/ngx_eventport_module.c

EPOLL_MODULE=ngx_epoll_module
EPOLL_SRCS=src/event/modules/ngx_epoll_module.c

IOCP_MODULE=ngx_iocp_module
IOCP_SRCS=src/event/modules/ngx_iocp_module.c

FILE_AIO_SRCS="src/os/unix/ngx_file_aio_read.c"
LINUX_AIO_SRCS="src/os/unix/ngx_linux_aio_read.c"

UNIX_INCS="$CORE_INCS $EVENT_INCS src/os/unix"

UNIX_DEPS="$CORE_DEPS $EVENT_DEPS \
            src/os/unix/ngx_time.h \
            src/os/unix/ngx_errno.h \
            src/os/unix/ngx_alloc.h \
            src/os/unix/ngx_files.h \
            src/os/unix/ngx_channel.h \
            src/os/unix/ngx_shmem.h \
            src/os/unix/ngx_process.h \
            src/os/unix/ngx_setaffinity.h \
            src/os/unix/ngx_setproctitle.h \
            src/os/unix/ngx_atomic.h \
            src/os/unix/ngx_gcc_atomic_x86.h \
            src/os/unix/ngx_thread.h \
            src/os/unix/ngx_socket.h \
            src/os/unix/ngx_os.h \
            src/os/unix/ngx_user.h \
            src/os/unix/ngx_dlopen.h \
            src/os/unix/ngx_process_cycle.h"

# add to UNIX_DEPS
#            src/os/unix/ngx_gcc_atomic_amd64.h \
#            src/os/unix/ngx_gcc_atomic_sparc64.h \
#            src/os/unix/ngx_gcc_atomic_ppc.h \
#            src/os/unix/ngx_sunpro_atomic_sparc64.h \
#            src/os/unix/ngx_sunpro_x86.il \
#            src/os/unix/ngx_sunpro_amd64.il \
#            src/os/unix/ngx_sunpro_sparc64.il \


UNIX_SRCS="$CORE_SRCS $EVENT_SRCS \
            src/os/unix/ngx_time.c \
            src/os/unix/ngx_errno.c \
            src/os/unix/ngx_alloc.c \
            src/os/unix/ngx_files.c \
            src/os/unix/ngx_socket.c \
            src/os/unix/ngx_recv.c \
            src/os/unix/ngx_readv_chain.c \
            src/os/unix/ngx_udp_recv.c \
            src/os/unix/ngx_send.c \
            src/os/unix/ngx_writev_chain.c \
            src/os/unix/ngx_udp_send.c \
            src/os/unix/ngx_udp_sendmsg_chain.c \
            src/os/unix/ngx_channel.c \
            src/os/unix/ngx_shmem.c \
            src/os/unix/ngx_process.c \
            src/os/unix/ngx_daemon.c \
            src/os/unix/ngx_setaffinity.c \
            src/os/unix/ngx_setproctitle.c \
            src/os/unix/ngx_posix_init.c \
            src/os/unix/ngx_user.c \
            src/os/unix/ngx_dlopen.c \
            src/os/unix/ngx_process_cycle.c"

POSIX_DEPS=src/os/unix/ngx_posix_config.h

THREAD_POOL_MODULE=ngx_thread_pool_module
THREAD_POOL_DEPS=src/core/ngx_thread_pool.h
THREAD_POOL_SRCS="src/core/ngx_thread_pool.c
                  src/os/unix/ngx_thread_cond.c
                  src/os/unix/ngx_thread_mutex.c
                  src/os/unix/ngx_thread_id.c"

FREEBSD_DEPS="src/os/unix/ngx_freebsd_config.h src/os/unix/ngx_freebsd.h"
FREEBSD_SRCS=src/os/unix/ngx_freebsd_init.c
FREEBSD_SENDFILE_SRCS=src/os/unix/ngx_freebsd_sendfile_chain.c

LINUX_DEPS="src/os/unix/ngx_linux_config.h src/os/unix/ngx_linux.h"
LINUX_SRCS=src/os/unix/ngx_linux_init.c
LINUX_SENDFILE_SRCS=src/os/unix/ngx_linux_sendfile_chain.c


SOLARIS_DEPS="src/os/unix/ngx_solaris_config.h src/os/unix/ngx_solaris.h"
SOLARIS_SRCS=src/os/unix/ngx_solaris_init.c
SOLARIS_SENDFILEV_SRCS=src/os/unix/ngx_solaris_sendfilev_chain.c


DARWIN_DEPS="src/os/unix/ngx_darwin_config.h src/os/unix/ngx_darwin.h"
DARWIN_SRCS=src/os/unix/ngx_darwin_init.c
DARWIN_SENDFILE_SRCS=src/os/unix/ngx_darwin_sendfile_chain.c


WIN32_INCS="$CORE_INCS $EVENT_INCS src/os/win32"

WIN32_DEPS="$CORE_DEPS $EVENT_DEPS \
            src/os/win32/ngx_win32_config.h \
            src/os/win32/ngx_time.h \
            src/os/win32/ngx_errno.h \
            src/os/win32/ngx_alloc.h \
            src/os/win32/ngx_files.h \
            src/os/win32/ngx_shmem.h \
            src/os/win32/ngx_process.h \
            src/os/win32/ngx_atomic.h \
            src/os/win32/ngx_thread.h \
            src/os/win32/ngx_socket.h \
            src/os/win32/ngx_os.h \
            src/os/win32/ngx_user.h \
            src/os/win32/ngx_dlopen.h \
            src/os/win32/ngx_process_cycle.h"

WIN32_CONFIG=src/os/win32/ngx_win32_config.h

WIN32_SRCS="$CORE_SRCS $EVENT_SRCS \
            src/os/win32/ngx_errno.c \
            src/os/win32/ngx_alloc.c \
            src/os/win32/ngx_files.c \
            src/os/win32/ngx_shmem.c \
            src/os/win32/ngx_time.c \
            src/os/win32/ngx_process.c \
            src/os/win32/ngx_thread.c \
            src/os/win32/ngx_socket.c \
            src/os/win32/ngx_wsarecv.c \
            src/os/win32/ngx_wsarecv_chain.c \
            src/os/win32/ngx_udp_wsarecv.c \
            src/os/win32/ngx_wsasend.c \
            src/os/win32/ngx_wsasend_chain.c \
            src/os/win32/ngx_win32_init.c \
            src/os/win32/ngx_user.c \
            src/os/win32/ngx_dlopen.c \
            src/os/win32/ngx_event_log.c \
            src/os/win32/ngx_process_cycle.c \
            src/event/ngx_event_acceptex.c"

NGX_WIN32_ICONS="src/os/win32/nginx.ico"
NGX_WIN32_RC="src/os/win32/nginx.rc"


HTTP_FILE_CACHE_SRCS=src/http/ngx_http_file_cache.c

HTTP_HUFF_SRCS="src/http/ngx_http_huff_decode.c
                src/http/ngx_http_huff_encode.c"
