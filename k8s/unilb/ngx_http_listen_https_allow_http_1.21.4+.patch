diff --git a/src/http/ngx_http.c b/src/http/ngx_http.c
index 73c08d5..e8b2458 100644
--- a/src/http/ngx_http.c
+++ b/src/http/ngx_http.c
@@ -1232,6 +1232,7 @@ ngx_http_add_addresses(ngx_conf_t *cf, ngx_http_core_srv_conf_t *cscf,
     ngx_http_conf_addr_t  *addr;
 #if (NGX_HTTP_SSL)
     ngx_uint_t             ssl;
+    ngx_uint_t             https_allow_http;
 #endif
 #if (NGX_HTTP_V2)
     ngx_uint_t             http2;
@@ -1267,6 +1268,7 @@ ngx_http_add_addresses(ngx_conf_t *cf, ngx_http_core_srv_conf_t *cscf,
 
 #if (NGX_HTTP_SSL)
         ssl = lsopt->ssl || addr[i].opt.ssl;
+        https_allow_http = lsopt->https_allow_http || addr[i].opt.https_allow_http;
 #endif
 #if (NGX_HTTP_V2)
         http2 = lsopt->http2 || addr[i].opt.http2;
@@ -1303,6 +1305,7 @@ ngx_http_add_addresses(ngx_conf_t *cf, ngx_http_core_srv_conf_t *cscf,
         addr[i].opt.proxy_protocol = proxy_protocol;
 #if (NGX_HTTP_SSL)
         addr[i].opt.ssl = ssl;
+        addr[i].opt.https_allow_http = https_allow_http;
 #endif
 #if (NGX_HTTP_V2)
         addr[i].opt.http2 = http2;
@@ -1833,6 +1836,7 @@ ngx_http_add_addrs(ngx_conf_t *cf, ngx_http_port_t *hport,
         addrs[i].conf.default_server = addr[i].default_server;
 #if (NGX_HTTP_SSL)
         addrs[i].conf.ssl = addr[i].opt.ssl;
+        addrs[i].conf.https_allow_http = addr[i].opt.https_allow_http;
 #endif
 #if (NGX_HTTP_V2)
         addrs[i].conf.http2 = addr[i].opt.http2;
@@ -1898,6 +1902,7 @@ ngx_http_add_addrs6(ngx_conf_t *cf, ngx_http_port_t *hport,
         addrs6[i].conf.default_server = addr[i].default_server;
 #if (NGX_HTTP_SSL)
         addrs6[i].conf.ssl = addr[i].opt.ssl;
+        addrs6[i].conf.https_allow_http = addr[i].opt.https_allow_http;
 #endif
 #if (NGX_HTTP_V2)
         addrs6[i].conf.http2 = addr[i].opt.http2;
diff --git a/src/http/ngx_http_core_module.c b/src/http/ngx_http_core_module.c
index 0075db5..2a58f24 100644
--- a/src/http/ngx_http_core_module.c
+++ b/src/http/ngx_http_core_module.c
@@ -4199,6 +4199,17 @@ ngx_http_core_listen(ngx_conf_t *cf, ngx_command_t *cmd, void *conf)
             continue;
         }
 
+#if (NGX_HTTP_SSL)
+        if (ngx_strcmp(value[n].data, "https_allow_http") == 0) {
+            lsopt.https_allow_http = 1;
+            continue;
+
+            ngx_conf_log_error(NGX_LOG_EMERG, cf, 0,
+                               "the \"https_allow_http\" parameter requires "
+                               "ngx_http_ssl_module");
+            return NGX_CONF_ERROR;
+        }
+#endif
         ngx_conf_log_error(NGX_LOG_EMERG, cf, 0,
                            "invalid parameter \"%V\"", &value[n]);
         return NGX_CONF_ERROR;
diff --git a/src/http/ngx_http_core_module.h b/src/http/ngx_http_core_module.h
index 004a98e..093c937 100644
--- a/src/http/ngx_http_core_module.h
+++ b/src/http/ngx_http_core_module.h
@@ -82,6 +82,9 @@ typedef struct {
     unsigned                   reuseport:1;
     unsigned                   so_keepalive:2;
     unsigned                   proxy_protocol:1;
+#if (NGX_HTTP_SSL)
+    unsigned                   https_allow_http:1;
+#endif
 
     int                        backlog;
     int                        rcvbuf;
@@ -238,6 +241,9 @@ struct ngx_http_addr_conf_s {
     unsigned                   ssl:1;
     unsigned                   http2:1;
     unsigned                   proxy_protocol:1;
+#if (NGX_HTTP_SSL)
+    unsigned                   https_allow_http:1;
+#endif
 };
 
 
diff --git a/src/http/ngx_http_request.c b/src/http/ngx_http_request.c
index 013b715..1868f74 100644
--- a/src/http/ngx_http_request.c
+++ b/src/http/ngx_http_request.c
@@ -778,7 +778,11 @@ ngx_http_ssl_handshake(ngx_event_t *rev)
         ngx_log_debug0(NGX_LOG_DEBUG_HTTP, rev->log, 0, "plain http");
 
         c->log->action = "waiting for request";
-
+#if (NGX_HTTP_SSL)
+        if (hc->addr_conf->https_allow_http) {
+            hc->ssl = 0;
+        }
+#endif
         rev->handler = ngx_http_wait_request_handler;
         ngx_http_wait_request_handler(rev);
 
