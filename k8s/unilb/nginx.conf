user root;
worker_processes auto;
pid /run/nginx.pid;

events {
    worker_connections 1024;
    # multi_accept on;
}

http {
    sendfile on;
    tcp_nopush on;
    types_hash_max_size 2048;
    client_max_body_size 1024M;

    include /etc/nginx/mime.types;
    default_type application/octet-stream;
    access_log /var/log/nginx/access.log;
    error_log /var/log/nginx/error.log;

    upstream web {
        keepalive 300;
        keepalive_timeout  120s;
        keepalive_requests 100;
        server 172.20.0.2:9000;
    }
    upstream static {
        keepalive 300;
        keepalive_timeout  120s;
        keepalive_requests 100;
        server 127.0.0.1:8000;
    }

    server {
	listen 9000 ssl https_allow_http;
	# listen 9000 ssl;
        
        # SSL 证书配置（需要替换为您的实际证书路径）
	ssl_certificate /etc/ssl/certs/nginx-selfsigned.crt;
	ssl_certificate_key /etc/ssl/private/nginx-selfsigned.key;
        
        # 修复1：启用TLS 1.2/1.3，禁用不安全的协议
        ssl_protocols TLSv1.2 TLSv1.3;
        ssl_ciphers 'ECDHE-ECDSA-AES128-GCM-SHA256:ECDHE-RSA-AES128-GCM-SHA256:ECDHE-ECDSA-AES256-GCM-SHA384:ECDHE-RSA-AES256-GCM-SHA384';
        ssl_prefer_server_ciphers on;

        # 修复2：强制HSTS（1年有效期，包含子域名）
        add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;

        # 修复3：安全CORS配置（仅允许指定来源）
	proxy_hide_header 'Access-Control-Allow-Origin';
	proxy_hide_header 'Access-Control-Allow-Methods';
	proxy_hide_header 'Access-Control-Allow-Headers';
	proxy_hide_header 'Access-Control-Allow-Credentials';
	proxy_hide_header 'Access-Control-Max-Age';

        set $cors "";
        if ($http_origin = "https://**************:9000") {  # 允许自身IP跨域
            set $cors $http_origin;
        }
        add_header 'Access-Control-Allow-Origin' "$cors" always;
        add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS' always;
        add_header 'Access-Control-Allow-Headers' 'Authorization, Content-Type, X-Requested-With' always;
        add_header 'Access-Control-Allow-Credentials' 'false' always;  # 禁用凭据（除非必要）
        add_header 'Access-Control-Max-Age' '600' always;

        # 修复4：Polyfill 漏洞
        # location ~* ^/assets/polyfill.*\.js$ {
        #     return 404;
        # }

        # 修复5：java.io.File读取任意文件漏洞
        location /api/system/cluster_config/java.io.File {
            return 403;
        }
        
        # 修复6：/api/api-docs /api/api-browser 信息泄露漏洞
        location /api/api-docs {
            return 403;
        }
        location /api/api-browser {
            return 403;
        }
        
        # 修复7：/api/system/stats 普通用户也能拿到操作系统信息
        location /api/system/stats {
            return 403;
        }

        location /up-install {
            # 启用与后端的 keepalive 连接
            proxy_http_version 1.1;
            proxy_set_header Connection "";
            proxy_pass http://static/up-install;
            # return 404;
        }

        location /centos6-heart {
            # 启用与后端的 keepalive 连接
	    proxy_method PUT;
            proxy_http_version 1.1;
            proxy_set_header Connection "";
            proxy_pass http://web/api/sidecars;
        }

        location /centos6-confid {
            # 启用与后端的 keepalive 连接
            proxy_http_version 1.1;
            proxy_set_header Connection "";
            proxy_pass http://web/api/sidecar/configurations;
        }

        location / {
            proxy_pass http://web;
            proxy_http_version 1.1;
            proxy_set_header Host $host;
            proxy_set_header Connection "";
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_set_header Proxy-Connect-Timeout 5s;
            proxy_set_header Proxy-Timeout 10s;
	    # sub_filter 'req_body_len' 'not_allow';
	    # sub_filter 'rsp_body_len' 'not_allow';
            # sub_filter_types *;
	    # sub_filter_once off;
        }
    }
}
