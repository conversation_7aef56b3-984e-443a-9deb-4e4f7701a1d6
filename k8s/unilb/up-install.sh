LUJING="up-install"
HTTP_SERVER="**********:9000"
TOKEN="td8hmfdis9cpgoqt2jcv7q688aq808eft04p6vkl3lnppj6m9q6"
    
echo "******check  uniprobe exsist******"
if [[ -e /opt/${LUJING}/uniprobe ]]; then
  echo "******uniprobe exsits!******"
  rm -f /opt/${LUJING}/uniprobe* 
fi
    
echo "******kill uniprobe process******"
ps -ef | grep uniprobe | grep -v grep | awk '{print $2}' | xargs kill -9  
sleep 5
    
echo "******begin install uniprobe******"
echo "******create file dir******"
mkdir -p /opt/${LUJING}

echo "******change workespace******"
cd /opt/${LUJING}
    
echo "******download uniprobe software******"
if [[ `arch` =~ "x86_64" ]]; then
  if [[ -n "`cat /etc/issue | grep 'CentOS\|Red Hat' | grep 6 | grep -v grep`" ]]; then
    curl -k -o uniprobe https://${HTTP_SERVER}/${LUJING}/uniprobe-20241110-89-996-x86-centos6
  else
    curl -k -o uniprobe https://${HTTP_SERVER}/${LUJING}/uniprobe-20241110-89-996-x86
  fi
elif [[ `arch` =~ "aarch64" ]]; then
  curl -k -o uniprobe https://${HTTP_SERVER}/${LUJING}/uniprobe-20241110-89-996-arm
else 
  echo "Have no version for this os-release,please connect whit the manager!"
  exit
fi
    
echo "******run uniprobe worker******"
chmod +x /opt/${LUJING}/uniprobe
nohup /opt/${LUJING}/uniprobe -n ${HTTP_SERVER} -a $TOKEN > /dev/null 2>&1 &
