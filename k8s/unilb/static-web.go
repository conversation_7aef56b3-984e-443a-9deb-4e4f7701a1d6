package main

import (
	"flag"
	"fmt"
	"html/template"
	"net/http"
	"os"
	"path/filepath"
	"strings"
)

type FileInfo struct {
	Name string
	Path string
}

type DirectoryInfo struct {
	Path  string
	Files []FileInfo
	Dirs  []FileInfo
}

func main() {
	// 定义命令行参数
	var basePath string
	var port string

	flag.StringVar(&basePath, "path", "/usr/share/nginx/html", "本地文件系统路径")
	flag.StringVar(&port, "port", ":8000", "服务器监听端口")
	flag.Parse()

	// 确保端口以冒号开头
	if !strings.HasPrefix(port, ":") {
		port = ":" + port
	}

	http.HandleFunc("/", func(w http.ResponseWriter, r *http.Request) {
		serveFiles(w, r, basePath)
	})
	
	fmt.Printf("Server started at http://localhost%s\n", port)
	fmt.Printf("Serving files from: %s\n", basePath)
	http.ListenAndServe(port, nil)
}

func serveFiles(w http.ResponseWriter, r *http.Request, basePath string) {
	path := filepath.Join(basePath, strings.Replace(r.URL.Path, "//", "/", -1))
	info, err := os.Stat(path)
	if err != nil {
		http.Error(w, "File or directory not found", http.StatusNotFound)
		return
	}

	if info.IsDir() {
		dirInfo := getDirectoryInfo(path, basePath)
		tmpl := template.Must(template.New("index.html").Parse(`
			<!DOCTYPE html>
			<html>
			<head>
				<title>Directory Listing</title>
			</head>
			<body>
				<h1>Directory Listing</h1>
				<h2>{{ .Path }}</h2>
				<ul>
					{{ range .Dirs }}
						<li><a href="{{ .Path }}">{{ .Name }}/</a></li>
					{{ end }}
					{{ range .Files }}
						<li><a href="{{ .Path }}">{{ .Name }}</a></li>
					{{ end }}
				</ul>
			</body>
			</html>
		`))
		tmpl.Execute(w, dirInfo)
	} else {
		http.ServeFile(w, r, path)
	}
}

func getDirectoryInfo(path string, basePath string) DirectoryInfo {
	dirInfo := DirectoryInfo{
		Path:  strings.TrimPrefix(path, basePath),
		Files: []FileInfo{},
		Dirs:  []FileInfo{},
	}

	filepath.Walk(path, func(p string, info os.FileInfo, err error) error {
		if err != nil {
			return err
		}

		relPath, _ := filepath.Rel(path, p)
		if relPath == "." {
			return nil
		}

		if info.IsDir() {
			dirInfo.Dirs = append(dirInfo.Dirs, FileInfo{
				Name: info.Name(),
				Path: filepath.Join(dirInfo.Path, relPath),
			})
			return filepath.SkipDir
		} else {
			dirInfo.Files = append(dirInfo.Files, FileInfo{
				Name: info.Name(),
				Path: filepath.Join(dirInfo.Path, relPath),
			})
		}

		return nil
	})

	return dirInfo
}

