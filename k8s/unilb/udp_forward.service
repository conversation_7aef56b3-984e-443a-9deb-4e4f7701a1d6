[Unit]
Description=uniprobe
Documentation=http://aidynamic.com
After=network-online.target firewalld.service
Wants=network-online.target

[Service]
Type=simple
User=root
Group=root
ExecStart=/usr/local/bin/udp_forward -a 0.0.0.0 -l 12201 -b *******:12201,*******:12201 -s 1
ExecStop=/bin/kill 
KillMode=control-group
Restart=on-failure
StartLimitBurst=3
StartLimitInterval=60s

[Install]
WantedBy=multi-user.target