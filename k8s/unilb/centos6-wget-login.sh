#!/bin/bash

function getConfigInfo() {
  heartjson="$uniprobeTmpDir/uniserverheart.json"
  uniserverIp=$(echo $uniserver | awk -F ":" '{print $1}')
  heartdata="{\"node_name\":\"$localIP-$hostname\", \"node_details\": {\"operating_system\":\"Linux\",\"ip\":\"$uniserverIp\"}}"
  echo "$heartdata" > "$heartjson"

  authHeader=$(echo -n "$token:token" | base64)

  confIdList=$(wget --quiet \
    --no-check-certificate \
    --header="Content-Type: application/json" \
    --header="X-Graylog-Sidecar-Version: $version" \
    --header="X-Requested-By: 1" \
    --header="Authorization: Basic $authHeader" \
    --post-file="$heartjson" \
    -O - \
    "https://$uniserver/centos6-heart/$localmac" | grep -Po 'configuration_id[" :]+\K[^"]+')

  for confId in ${confIdList[@]}; do
    confStr=$(wget --quiet \
      --no-check-certificate \
      --header="Accept: application/json" \
      --header="Authorization: Basic $authHeader" \
      -O - \
      "https://$uniserver/centos6-confid/$confId?pretty=true")

    if echo "$confStr" | grep -q "device="; then
      export confUniprobeStr=$(echo "$confStr" | grep -Po 'template[" :]+\K[^"]+')
    elif echo "$confStr" | grep -q "inputs."; then
      export confUnibaseStr=$(echo "$confStr" | grep -Po 'template[" :]+\K[^"].*\]')
    fi
  done
}
