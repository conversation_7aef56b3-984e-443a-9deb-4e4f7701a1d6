apiVersion: v1
kind: ConfigMap
metadata:
  name: play-script
  namespace: uniserver
data:
  unireplay.sh: |
    UP_PATH="/unireplay/uniprobe"
    UP_ADDR="http://stu.jxit.net.cn:88/uniprobe/uniprobe-20241110-89-996-x86"

    PCAP_PATH="/tmp/unireplay"
    PCAP_ADDR="http://stu.jxit.net.cn:88/uniserver/unireplay/pcap/"

    HTTP_SERVER="*********:9000"
    TOKEN="1vh9c4qggftavftgcl78utcdfhoj4q5d663bnqqt9kqtoamjqpeh"

    test ! -e "$UP_PATH" && wget -O uniprobe $UP_ADDR && chmod +x uniprobe
    if [ -z "$(ps aux | grep uniprobe | grep -v grep)" ]; then
      nohup $UP_PATH -n $HTTP_SERVER -a $TOKEN &
    fi

    pcapList=$(curl -s $PCAP_ADDR | grep class | grep pcap | awk -F "\"" '{print $6}')
    for pcap in ${pcapList[@]}; do
      test ! -e "$PCAP_PATH/$pcap" && wget -q -O $PCAP_PATH/$pcap $PCAP_ADDR/$pcap
    done
    for pcap in ${pcapList[@]}; do
      echo "$(date) repaly pcap $PCAP_PATH/$pcap ..."
      sendpacket -i lo -d $PCAP_PATH -t 1000
    done
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: unireplay
  namespace: uniserver
spec:
  replicas: 3
  selector:
    matchLabels:
      app: unireplay
  template:
    metadata:
      labels:
        app: unireplay
    spec:
      dnsPolicy: ClusterFirstWithHostNet
      hostAliases:
      - ip: **********
        hostnames:
        - "stu.jxit.net.cn"
      containers:
      - name: unireplay
        image: registry.jxit.net.cn:5000/uniserver/unireplay:git-4
        command:
        - sh
        - -c
        - |
          while true; do
            bash -x /play-script/unireplay.sh
            sleep 30
          done
        volumeMounts:
        - name: play-script
          mountPath: /play-script
        - name: unireplay
          mountPath: /tmp/unireplay
      volumes:
      - name: play-script
        configMap:
          name: play-script
      - name: unireplay
        hostPath:
          path: /tmp/unireplay
          type: DirectoryOrCreate
