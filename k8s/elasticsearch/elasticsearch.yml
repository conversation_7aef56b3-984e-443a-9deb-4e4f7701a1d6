apiVersion: v1
kind: ServiceAccount
metadata:
  name: elasticsearch
  namespace: uniserver
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: elasticsearch-role
rules:
- apiGroups: [""]
  resources: ["pods"]
  verbs: ["get", "list", "watch"]
- apiGroups: [""]
  resources: ["pods/log"]
  verbs: ["get"]
- apiGroups: [""]
  resources: ["nodes"]
  verbs: ["get", "list", "watch"]
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: elasticsearch-binding
subjects:
- kind: ServiceAccount
  name: elasticsearch
  namespace: uniserver
roleRef:
  kind: ClusterRole
  name: elasticsearch-role
  apiGroup: rbac.authorization.k8s.io
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: elasticsearch-config
  namespace: uniserver
data:
  elasticsearch.yml: |
    cluster.name: es-cluster
    node.name: ${HOSTNAME}
    network.host: 0.0.0.0
    node.data: true
    node.master: true
    node.max_local_storage_nodes: 3
    discovery.zen.fd.ping_timeout: 120s
    discovery.zen.fd.ping_retries: 6
    discovery.zen.fd.ping_interval: 30s
    http.cors.enabled: true
    http.cors.allow-origin: "*"
    discovery.seed_hosts: ["elasticsearch-0.es-cluster"]
    cluster.initial_master_nodes: ["elasticsearch-0"]
---
apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: elasticsearch
  namespace: uniserver
spec:
  serviceName: es-cluster
  replicas: 1  # 根据您的节点数量调整
  selector:
    matchLabels:
      app: elasticsearch
  template:
    metadata:
      labels:
        app: elasticsearch
    spec:
      affinity:
        podAntiAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
            - labelSelector:
                matchExpressions:
                  - key: app
                    operator: In
                    values:
                      - elasticsearch
              topologyKey: "kubernetes.io/hostname"
      containers:
      - name: elasticsearch
        image: registry.jxit.net.cn:5000/uniserver/elasticsearch:7.10.2
        ports:
        - containerPort: 9200
          name: rest
          protocol: TCP
        - containerPort: 9300
          name: inter-node
          protocol: TCP
        env:
        - name: ES_JAVA_OPTS
          value: "-Xms2g -Xmx2g"
        - name: TAKE_FILE_OWNERSHIP
          value: "true"
        volumeMounts:
        - name: elasticsearch-data
          mountPath: /usr/share/elasticsearch/data
        - name: elasticsearch-config
          mountPath: /usr/share/elasticsearch/config/elasticsearch.yml
          subPath: elasticsearch.yml
      initContainers:
      - name: fix-permissions
        image: registry.jxit.net.cn:5000/alpine:3.19.1
        command: ["sh", "-c", "chown -R 1000:1000 /usr/share/elasticsearch/data"]
        securityContext:
          privileged: true
        volumeMounts:
        - name: elasticsearch-data
          mountPath: /usr/share/elasticsearch/data
      - name: increase-vm-max-map
        image: registry.jxit.net.cn:5000/alpine:3.19.1
        command: ["sysctl", "-w", "vm.max_map_count=262144"]
        securityContext:
          privileged: true
      - name: increase-fd-ulimit
        image: registry.jxit.net.cn:5000/alpine:3.19.1
        command: ["sh", "-c", "ulimit -n 65536"]
        securityContext:
          privileged: true
      volumes:
      - name: elasticsearch-config
        configMap:
          name: elasticsearch-config
      - name: elasticsearch-data
        hostPath:
          path: /data/elasticsearch
          type: DirectoryOrCreate
---
apiVersion: v1
kind: Service
metadata:
  name: es-cluster
  namespace: uniserver
spec:
  selector:
    app: elasticsearch
  clusterIP: None
  ports:
  - port: 9200
    name: rest
  - port: 9300
    name: inter-node
---
apiVersion: v1
kind: Service
metadata:
  name: elasticsearch
  namespace: uniserver
spec:
  selector:
    app: elasticsearch
  type: ClusterIP
  ports:
  - port: 9200
    targetPort: 9200
    name: rest