apiVersion: apps/v1
kind: Deployment
metadata:
  name: uniarch
  namespace: uniserver
  labels:
    app: uniarch
spec:
  replicas: 1
  selector:
    matchLabels:
      app: uniarch
  template:
    metadata:
      labels:
        app: uniarch
    spec:
      nodeSelector:
        node-role.kubernetes.io/master: "true"
      hostNetwork: true
      containers:
      - name: uniarch
        image: registry.jxit.net.cn:5000/uniserver/uniarch:git-186
        command:
        - "sh"
        - "-xc"
        - |
          while true; do
            sh /app/restart.sh
            sleep 30
          done
        env:
        - name: GELF_IP
          value: "127.0.0.1"
        - name: GELF_PORT
          value: "12201"
        - name: ES_URL
          value: "http://127.0.0.1:9200"
        - name: LOOKUP_URL
          value: "http://127.0.0.1:9000/up-install/zhuji.csv"
        - name: SYSTEM_URL
          value: "http://127.0.0.1:9000/up-install/xitong.csv"
        - name: SAMPLE
          value: "1"
        - name: PROTOCOL
          value: "udp"
        volumeMounts:
        - name: uniarch
          mountPath: /uniarch
      volumes:
      - name: uniarch
        persistentVolumeClaim:
          claimName: uniarch-pvc
